using Microsoft.UI.Xaml;
using Microsoft.UI.Xaml.Data;
using System;

namespace App2.Helpers
{
    public class BoolToVisibilityConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, string language)
        {
            if (value is bool boolValue)
            {
                return boolValue ? Visibility.Visible : Visibility.Collapsed;
            }
            
            return Visibility.Collapsed;
        }
        
        public object ConvertBack(object value, Type targetType, object parameter, string language)
        {
            if (value is Visibility visibility)
            {
                return visibility == Visibility.Visible;
            }
            
            return false;
        }
    }


    public class IdToVisibilityConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, string language)
        {
            if (value == null || parameter == null)
                return Visibility.Collapsed;
            int id = System.Convert.ToInt32(value);
            int targetId = System.Convert.ToInt32(parameter);
            if (id < 4 && targetId == 4) { // ���⴦�������idС��4�Ҳ���Ϊ4������ʾ
                return Visibility.Visible;
            }
            if (id == 4 && targetId == 5) {
                return Visibility.Visible;
            }
            return  Visibility.Collapsed;
        }

        public object ConvertBack(object value, Type targetType, object parameter, string language)
        {
            throw new NotImplementedException();
        }
    }
}