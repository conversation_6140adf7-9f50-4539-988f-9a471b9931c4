using Microsoft.UI.Dispatching;
using Microsoft.UI.Xaml;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace App2.Helpers
{
    class UiThreadDispatcher
    {

        public static Task RunAsync(Action action)
        {
            var dispatcher = GetDispatcher();
            if (dispatcher.HasThreadAccess)
            {
                action();
                return Task.CompletedTask;
            }

            var tcs = new TaskCompletionSource<bool>();
            dispatcher.TryEnqueue(() =>
            {
                try
                {
                    action();
                    tcs.SetResult(true);
                }
                catch (Exception ex)
                {
                    tcs.SetException(ex);
                }
            });
            return tcs.Task;
        }

        public static Task RunAsync(Func<Task> asyncAction)
        {
            var dispatcher = GetDispatcher();
            if (dispatcher.HasThreadAccess)
            {
                return asyncAction();
            }

            var tcs = new TaskCompletionSource<bool>();
            dispatcher.TryEnqueue(async () =>
            {
                try
                {
                    await asyncAction();
                    tcs.SetResult(true);
                }
                catch (Exception ex)
                {
                    tcs.SetException(ex);
                }
            });
            return tcs.Task;
        }

        private static DispatcherQueue GetDispatcher()
        {
            // 优先尝试获取当前页面的 DispatcherQueue
            if (Window.Current?.DispatcherQueue != null)
                return Window.Current.DispatcherQueue;

            // 备用方案：从 App 类获取主窗口
            if (App.Current is App app && app.MainWindow?.DispatcherQueue != null)
                return app.MainWindow.DispatcherQueue;

            // 最后尝试：获取当前线程的 DispatcherQueue
            return DispatcherQueue.GetForCurrentThread() ??
                   throw new InvalidOperationException("无法获取 DispatcherQueue");
        }
    }
}
