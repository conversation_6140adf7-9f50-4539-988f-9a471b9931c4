using System;
using System.Globalization;
using Microsoft.UI.Xaml.Data;

namespace App2.Helpers
{
    public class IdToIsCheckedConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, string language)
        {
            int selectedId = (int)value;
            int itemId = System.Convert.ToInt32(parameter);
            return selectedId == itemId;
        }

        public object ConvertBack(object value, Type targetType, object parameter, string language)
        {
            bool isChecked = (bool)value;
            int itemId = System.Convert.ToInt32(parameter);
            return isChecked ? itemId : null; // ʹ�� null ��� Binding.DoNothing
        }
    }
}