using Microsoft.UI.Xaml.Data;
using System;

namespace App2.Helpers
{
    public class DateTimeToStringConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, string language)
        {
            if (value is DateTime dateTime)
            {
                return dateTime.ToString("yyyy-MM-dd HH:mm:ss");
            }
            else if (value is DateTime nullableDateTime)
            {
                return nullableDateTime.ToString("yyyy-MM-dd HH:mm:ss");
            }
            
            return "";
        }

        public object ConvertBack(object value, Type targetType, object parameter, string language)
        {
            throw new NotImplementedException();
        }
    }
}
