using App2.Models;
using SocksSharp;
using SocksSharp.Proxy;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Net;
using System.Net.Sockets;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
namespace App2.Helpers

{

    
    public class ProxySocketTest
    {
        /// <summary>
        /// Proxytype == socket/http
        /// </summary>

        public async Task TestProxyBatchAsync(ObservableCollection<DataItem> proxies, string Proxytype, int maxConcurrency = 1)
        {
            var throttler = new SemaphoreSlim(maxConcurrency);
            var tasks = new List<Task>();

            foreach (var proxy in proxies)
            {
                
                tasks.Add(TestProxyWithThrottle(proxy, throttler,Proxytype));
            }

            await Task.WhenAll(tasks);
        }

        public  async Task TestProxyWithThrottle(DataItem proxy, SemaphoreSlim throttler,string Proxytype)
        {
            proxy.Status = "测试中...";
            await throttler.WaitAsync();
            try
            {

                if (Proxytype == "socket")
                {

                    //proxy.Status = await TestSocks5ProxyAvailable(proxy.Host, proxy.Port, proxy.Username, proxy.Password)
                    //     ? "可用"
                    //    : "不可用";
                    proxy.Status = await TestSocksProxyAsync(proxy.Host, proxy.Port, proxy.Username, proxy.Password)
                       ? "可用"
                      : "不可用";
                    proxy.LastUpdated = DateTime.Now;
                }
                else {
                    proxy.Status = await TestHttpProxyAvailable(proxy.Host, proxy.Port, proxy.Username, proxy.Password)
                        ? "可用"
                        : "不可用";
                    proxy.LastUpdated = DateTime.Now;
                }

            }
            finally
            {
                throttler.Release();
            }
        }

        /// <summary>
        /// 测试单个 SOCKS5 代理是否可用
        /// </summary>
        public async Task<bool> TestSocks5ProxyAvailable(
     string host,
     string port,
     string username,
     string password,
     int timeoutSeconds = 8)
        {
            if (!int.TryParse(port, out int portNum))
                return false;

            using var socket = new Socket(SocketType.Stream, ProtocolType.Tcp);
            using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(timeoutSeconds));

            try
            {
                // 连接代理服务器
                await socket.ConnectAsync(host, portNum, cts.Token);

                // 1. 发送握手请求（同时支持无认证和密码认证）
                byte[] handshake = { 0x05, 0x02, 0x00, 0x02 }; // 告知支持两种认证方式
                await SendAsync(socket, handshake, cts.Token);

                // 2. 读取握手响应
                byte[] handshakeResponse = await ReceiveAsync(socket, 2, cts.Token);
                if (handshakeResponse[0] != 0x05)
                    return false; // 无效的SOCKS版本

                // 3. 处理认证方式
                byte authMethod = handshakeResponse[1];
                if (authMethod == 0x02) // 需要用户名/密码认证
                {
                    if (string.IsNullOrEmpty(username))
                        return false;

                    // 构建认证数据
                    var authBuffer = new List<byte> { 0x01 };
                    authBuffer.Add((byte)username.Length);
                    authBuffer.AddRange(Encoding.UTF8.GetBytes(username));
                    authBuffer.Add((byte)password.Length);
                    authBuffer.AddRange(Encoding.UTF8.GetBytes(password));

                    await SendAsync(socket, authBuffer.ToArray(), cts.Token);

                    // 读取认证响应
                    byte[] authResponse = await ReceiveAsync(socket, 2, cts.Token);
                    if (authResponse[0] != 0x01 || authResponse[1] != 0x00)
                        return false;
                }
                else if (authMethod != 0x00) // 不需要认证
                {
                    return false; // 不支持的认证方式
                }

                // 4. 发送连接请求（使用域名）
                var connectBuffer = new List<byte> { 0x05, 0x01, 0x00, 0x03 };
                string targetHost = "example.com"; // 测试域名
                connectBuffer.Add((byte)targetHost.Length);
                connectBuffer.AddRange(Encoding.ASCII.GetBytes(targetHost));
                connectBuffer.AddRange(BitConverter.GetBytes(IPAddress.HostToNetworkOrder((short)80))); // 80端口

                await SendAsync(socket, connectBuffer.ToArray(), cts.Token);

                // 5. 读取连接响应
                byte[] response = await ReceiveAsync(socket, 10, cts.Token);
                return response.Length > 1 && response[1] == 0x00; // 0x00表示成功
            }
            catch (Exception ex)
            {
                Console.WriteLine($"代理测试失败: {ex.Message}");
                return false;
            }
        }

        // ========== 辅助方法 ========== //
        private async Task SendAsync(Socket socket, byte[] data, CancellationToken ct)
        {
            int totalSent = 0;
            while (totalSent < data.Length)
            {
                int sent = await socket.SendAsync(
                    new ArraySegment<byte>(data, totalSent, data.Length - totalSent),
                    SocketFlags.None,
                    ct
                );
                if (sent == 0) throw new SocketException(10054);
                totalSent += sent;
            }
        }

        private async Task<byte[]> ReceiveAsync(Socket socket, int length, CancellationToken ct)
        {
            var buffer = new byte[length];
            int totalReceived = 0;

            while (totalReceived < length)
            {
                int received = await socket.ReceiveAsync(
                    new ArraySegment<byte>(buffer, totalReceived, length - totalReceived),
                    SocketFlags.None,
                    ct
                );
                if (received == 0) throw new SocketException(10054);
                totalReceived += received;
            }
            return buffer;
        }

        /// <summary>
        /// 测试带账号密码认证的 HTTP 代理在目标网站是否可用
        /// </summary>
        public async Task<bool> TestHttpProxyAvailable(
            string proxyHost, string proxyPort, string username, string password,
            string targetUrl = "http://example.com/", int timeoutSeconds = 8)
        {
            if (!int.TryParse(proxyPort, out int portNum)) return false;
            using var client = new TcpClient();
            using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(timeoutSeconds));
            try
            {
                await client.ConnectAsync(proxyHost, portNum, cts.Token);

                using var stream = client.GetStream();
                stream.ReadTimeout = timeoutSeconds * 1000;
                stream.WriteTimeout = timeoutSeconds * 1000;

                var uri = new Uri(targetUrl);

                // 构造 Proxy-Authorization 头
                string auth = Convert.ToBase64String(Encoding.ASCII.GetBytes($"{username}:{password}"));
                string request =
                    $"GET {targetUrl} HTTP/1.1\r\n" +
                    $"Host: {uri.Host}\r\n" +
                    $"Proxy-Authorization: Basic {auth}\r\n" +
                    $"Connection: close\r\n\r\n";
                byte[] requestBytes = Encoding.ASCII.GetBytes(request);

                await stream.WriteAsync(requestBytes, 0, requestBytes.Length, cts.Token);

                // 读取响应
                var buffer = new byte[1024];
                int bytesRead = await stream.ReadAsync(buffer, 0, buffer.Length, cts.Token);
                if (bytesRead > 0)
                {
                    string response = Encoding.ASCII.GetString(buffer, 0, bytesRead);
                    if (response.StartsWith("HTTP/1.1") || response.StartsWith("HTTP/1.0"))
                        return true;
                }
                return false;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> TestSocksProxyAsync(
          string proxyHost,
          string proxyPort,
          string username,
          string password,
          string testHost = "example.com", // 修正为主机名
          int testPort = 80,
          int timeoutMs = 8000)
        {
            if (string.IsNullOrWhiteSpace(proxyHost) ||
                string.IsNullOrWhiteSpace(proxyPort) ||
                !int.TryParse(proxyPort, out int port) ||
                port <= 0)
                return false;

            var settings = new ProxySettings
            {
                Host = proxyHost,
                Port = port,
                Credentials = new NetworkCredential(username ?? string.Empty, password ?? string.Empty)
            };

            var proxyClient = new ProxyClient<Socks5>
            {
                Settings = settings
            };

            var cts = new CancellationTokenSource(timeoutMs);
            try
            {
                var connectTask = Task.Run(() =>
                {
                    try
                    {
                        using var stream = proxyClient.GetDestinationStream(testHost, testPort);
                        return stream != null;
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine(ex); // 打印详细异常
                        return false;
                    }
                }, cts.Token);

                return await connectTask;

            }
            catch (ProxyException ex)
            {
                Console.WriteLine($"Proxy error: {ex.Message}");
                return false;
            }
            catch (OperationCanceledException)
            {
                Console.WriteLine("Proxy connection timed out");
                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"General error: {ex.Message}");
                return false;
            }
          
            finally
            {
                cts.Dispose();
            }
        }
    }

}




