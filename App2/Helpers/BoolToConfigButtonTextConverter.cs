using Microsoft.UI.Xaml.Data;
using System;
using System.Diagnostics;

namespace App2.Helpers
{
    public class BoolToConfigButtonTextConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, string language)
        {
            // 添加调试信息
            Debug.WriteLine($"BoolToConfigButtonTextConverter: value={value}, type={value?.GetType()}");
            
            if (value is bool hasConfig)
            {
                var result = hasConfig ? "编辑配置" : "初始化";
                Debug.WriteLine($"BoolToConfigButtonTextConverter: returning {result}");
                return result;
            }
            Debug.WriteLine("BoolToConfigButtonTextConverter: returning 初始化 (fallback)");
            return "初始化";
        }

        public object ConvertBack(object value, Type targetType, object parameter, string language)
        {
            throw new NotImplementedException();
        }
    }
}
