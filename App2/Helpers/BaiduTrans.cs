using System;
using System.IO;
using System.Net;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using System.Web;
using System.Net.Http;

namespace App2.Helpers
{

    
    public class BaiduTrans
    {
        //static void Main()
        //{
        //    // 原文
        //    string q = "apple";
        //    // 源语言
        //    string from = "auto";
        //    // 目标语言
        //    string to = "zh";
        //    // 改成您的APP ID
        //    string appId = "20250730002419274";
        //    Random rd = new Random();
        //    string salt = rd.Next(100000).ToString();
        //    // 改成您的密钥
        //    string secretKey = "vUhpC5P4YhNL1jvYp8De";
        //    string sign = EncryptString(appId + q + salt + secretKey);
        //    string url = "http://api.fanyi.baidu.com/api/trans/vip/translate?";
        //    url += "q=" + HttpUtility.UrlEncode(q);
        //    url += "&from=" + from;
        //    url += "&to=" + to;
        //    url += "&appid=" + appId;
        //    url += "&salt=" + salt;
        //    url += "&sign=" + sign;
        //    HttpWebRequest request = (HttpWebRequest)WebRequest.Create(url);
        //    request.Method = "GET";
        //    request.ContentType = "text/html;charset=UTF-8";
        //    request.UserAgent = null;
        //    request.Timeout = 6000;
        //    HttpWebResponse response = (HttpWebResponse)request.GetResponse();
        //    Stream myResponseStream = response.GetResponseStream();
        //    StreamReader myStreamReader = new StreamReader(myResponseStream, Encoding.GetEncoding("utf-8"));
        //    string retString = myStreamReader.ReadToEnd();
        //    myStreamReader.Close();
        //    myResponseStream.Close();
        //    Console.WriteLine(retString);
        //    Console.ReadLine();
        //}
        // 计算MD5值
        public static string EncryptString(string str)
        {
            MD5 md5 = MD5.Create();
            // 将字符串转换成字节数组
            byte[] byteOld = Encoding.UTF8.GetBytes(str);
            // 调用加密方法
            byte[] byteNew = md5.ComputeHash(byteOld);
            // 将加密结果转换为字符串
            StringBuilder sb = new StringBuilder();
            foreach (byte b in byteNew)
            {
                // 将字节转换成16进制表示的字符串，
                sb.Append(b.ToString("x2"));
            }
            // 返回加密的字符串
            return sb.ToString();
        }

        public static async Task<string> TranslateAsync(string q, string from, string to)
        {
            string appId = "20250730002419274";
            string secretKey = "vUhpC5P4YhNL1jvYp8De";
            string salt = new Random().Next(100000).ToString();
            string sign = EncryptString(appId + q + salt + secretKey);
            string url = "http://api.fanyi.baidu.com/api/trans/vip/translate?";
            url += "q=" + System.Web.HttpUtility.UrlEncode(q);
            url += "&from=" + from;
            url += "&to=" + to;
            url += "&appid=" + appId;
            url += "&salt=" + salt;
            url += "&sign=" + sign;

            using var client = new HttpClient();
            var response = await client.GetAsync(url);
            var json = await response.Content.ReadAsStringAsync();

            // 简单解析返回
            using var doc = JsonDocument.Parse(json);
            if (doc.RootElement.TryGetProperty("trans_result", out var transResult))
            {
                var sb = new System.Text.StringBuilder();
                foreach (var item in transResult.EnumerateArray())
                {
                    sb.AppendLine(item.GetProperty("dst").GetString());
                }
                return sb.ToString().Trim();
            }
            else if (doc.RootElement.TryGetProperty("error_msg", out var errorMsg))
            {
                return "翻译失败: " + errorMsg.GetString();
            }
            return "翻译失败";
        }

    }

}




