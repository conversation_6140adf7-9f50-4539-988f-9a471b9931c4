using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using App2.Utils;

namespace App2.Examples
{
    /// <summary>
    /// LeidianToolsNew 使用示例
    /// </summary>
    public class LeidianToolsNewExamples
    {
        // 雷电模拟器路径
        private static readonly string LdPath = @"C:\leidian\LDPlayer9\dnconsole.exe";

        /// <summary>
        /// 基础操作示例
        /// </summary>
        public static async Task BasicOperationsExample()
        {
            Console.WriteLine("=== 基础操作示例 ===");

            // 1. 获取模拟器列表
            var listResult = await LeidianToolsNew.ListAsync(LdPath);
            if (listResult.Success)
            {
                Console.WriteLine($"模拟器列表:\n{listResult.Output}");
            }
            else
            {
                Console.WriteLine($"获取列表失败: {listResult.Error}");
            }

            // 2. 添加新模拟器
            var addResult = await LeidianToolsNew.AddAsync(LdPath, "new_simulator_001");
            Console.WriteLine($"添加模拟器: {(addResult.Success ? "成功" : "失败")} - {addResult.Output}");

            // 3. 启动模拟器 (使用名称)
            var launchResult = await LeidianToolsNew.LaunchAsync(LdPath, "simulators_001");
            Console.WriteLine($"启动模拟器: {(launchResult.Success ? "成功" : "失败")}");

            // 4. 启动模拟器 (使用索引)
            var launchByIndexResult = await LeidianToolsNew.LaunchAsync(LdPath, "0");
            Console.WriteLine($"按索引启动: {(launchByIndexResult.Success ? "成功" : "失败")}");

            // 5. 检测模拟器是否运行
            var isRunningResult = await LeidianToolsNew.IsRunningAsync(LdPath, "simulators_001");
            Console.WriteLine($"运行状态: {isRunningResult.Output}");
        }

        /// <summary>
        /// 模拟器管理示例
        /// </summary>
        public static async Task SimulatorManagementExample()
        {
            Console.WriteLine("\n=== 模拟器管理示例 ===");

            // 1. 复制模拟器
            var copyResult = await LeidianToolsNew.CopyAsync(LdPath, "wen_2", "simulators_001");
            Console.WriteLine($"复制模拟器: {(copyResult.Success ? "成功" : "失败")}");

            // 2. 重命名模拟器
            var renameResult = await LeidianToolsNew.RenameAsync(LdPath, "wen_2", "wen_3");
            Console.WriteLine($"重命名模拟器: {(renameResult.Success ? "成功" : "失败")}");

            // 3. 重启模拟器
            var rebootResult = await LeidianToolsNew.RebootAsync(LdPath, "simulators_001");
            Console.WriteLine($"重启模拟器: {(rebootResult.Success ? "成功" : "失败")}");

            // 4. 退出指定模拟器
            var quitResult = await LeidianToolsNew.QuitAsync(LdPath, "simulators_001");
            Console.WriteLine($"退出模拟器: {(quitResult.Success ? "成功" : "失败")}");

            // 5. 退出所有模拟器
            var quitAllResult = await LeidianToolsNew.QuitAllAsync(LdPath);
            Console.WriteLine($"退出所有模拟器: {(quitAllResult.Success ? "成功" : "失败")}");

            // 6. 删除模拟器
            var removeResult = await LeidianToolsNew.RemoveAsync(LdPath, "wen_4");
            Console.WriteLine($"删除模拟器: {(removeResult.Success ? "成功" : "失败")}");
        }

        /// <summary>
        /// 备份和恢复示例
        /// </summary>
        public static async Task BackupRestoreExample()
        {
            Console.WriteLine("\n=== 备份和恢复示例 ===");

            // 1. 备份模拟器
            var backupResult = await LeidianToolsNew.BackupAsync(LdPath, "simulators_001", @".\backup.ldbk");
            Console.WriteLine($"备份模拟器: {(backupResult.Success ? "成功" : "失败")}");

            // 2. 恢复模拟器
            var restoreResult = await LeidianToolsNew.RestoreAsync(LdPath, "simulators_001", @"backup.ldbk");
            Console.WriteLine($"恢复模拟器: {(restoreResult.Success ? "成功" : "失败")}");
        }

        /// <summary>
        /// 配置修改示例
        /// </summary>
        public static async Task ConfigurationExample()
        {
            Console.WriteLine("\n=== 配置修改示例 ===");

            // 1. 使用辅助方法创建配置参数
            var modifyParams = LeidianToolsNew.CreateModifyParams(
                resolution: "900,1600,320",
                cpu: 2,
                memory: 2048,
                manufacturer: "asus",
                model: "ASUS_Z00DUO",
                phoneNumber: "13800000000",
                imei: "auto",
                imsi: "auto",
                simSerial: "auto",
                androidId: "auto",
                mac: "auto",
                autoRotate: true,
                lockWindow: false,
                root: true
            );

            var modifyResult = await LeidianToolsNew.ModifyAsync(LdPath, "simulators_001", modifyParams);
            Console.WriteLine($"修改配置: {(modifyResult.Success ? "成功" : "失败")}");

            // 2. 单独设置分辨率
            var resolutionParams = new Dictionary<string, object>
            {
                { "resolution", "1080,1920,320" }
            };
            var resolutionResult = await LeidianToolsNew.ModifyAsync(LdPath, "simulators_001", resolutionParams);
            Console.WriteLine($"设置分辨率: {(resolutionResult.Success ? "成功" : "失败")}");

            // 3. 单独设置CPU和内存
            var performanceParams = new Dictionary<string, object>
            {
                { "cpu", 4 },
                { "memory", 4096 }
            };
            var performanceResult = await LeidianToolsNew.ModifyAsync(LdPath, "simulators_001", performanceParams);
            Console.WriteLine($"设置性能: {(performanceResult.Success ? "成功" : "失败")}");
        }

        /// <summary>
        /// 属性设置示例
        /// </summary>
        public static async Task PropertyExample()
        {
            Console.WriteLine("\n=== 属性设置示例 ===");

            // 1. 设置属性
            var setPropResult = await LeidianToolsNew.SetPropAsync(LdPath, "simulators_001", "channel_id", "5215");
            Console.WriteLine($"设置属性: {(setPropResult.Success ? "成功" : "失败")}");

            // 2. 获取属性
            var getPropResult = await LeidianToolsNew.GetPropAsync(LdPath, "simulators_001", "channel_id");
            Console.WriteLine($"获取属性: {(getPropResult.Success ? getPropResult.Output : getPropResult.Error)}");
        }

        /// <summary>
        /// ADB命令示例
        /// </summary>
        public static async Task AdbExample()
        {
            Console.WriteLine("\n=== ADB命令示例 ===");

            // 1. 执行简单ADB命令
            var adbResult = await LeidianToolsNew.AdbAsync(LdPath, "simulators_001", "shell uptime");
            Console.WriteLine($"ADB命令结果: {adbResult.Output}");

            // 2. 获取设备信息
            var deviceInfoResult = await LeidianToolsNew.AdbAsync(LdPath, "simulators_001", "shell getprop ro.product.model");
            Console.WriteLine($"设备型号: {deviceInfoResult.Output}");

            // 3. 安装应用程序
            var installResult = await LeidianToolsNew.AdbAsync(LdPath, "simulators_001", "install /path/to/app.apk");
            Console.WriteLine($"安装应用: {(installResult.Success ? "成功" : "失败")}");
        }

        /// <summary>
        /// 应用程序管理示例
        /// </summary>
        public static async Task AppManagementExample()
        {
            Console.WriteLine("\n=== 应用程序管理示例 ===");

            // 1. 安装APK文件
            var installAppResult = await LeidianToolsNew.InstallAppAsync(
                LdPath, 
                "simulators_001", 
                @".\yanghao.apk", 
                "com.example.yanghao"
            );
            Console.WriteLine($"安装APP: {(installAppResult.Success ? "成功" : "失败")}");

            // 2. 启动应用程序
            var runAppResult = await LeidianToolsNew.RunAppAsync(LdPath, "simulators_001", "com.example.yanghao");
            Console.WriteLine($"启动APP: {(runAppResult.Success ? "成功" : "失败")}");

            // 3. 只安装APK（不指定包名）
            var installOnlyResult = await LeidianToolsNew.InstallAppAsync(LdPath, "simulators_001", @".\app.apk");
            Console.WriteLine($"安装APK: {(installOnlyResult.Success ? "成功" : "失败")}");
        }

        /// <summary>
        /// 信息查询示例
        /// </summary>
        public static async Task InformationQueryExample()
        {
            Console.WriteLine("\n=== 信息查询示例 ===");

            // 1. 获取基本列表
            var listResult = await LeidianToolsNew.ListAsync(LdPath);
            Console.WriteLine($"基本列表:\n{listResult.Output}");

            // 2. 获取详细信息
            var list2Result = await LeidianToolsNew.List2Async(LdPath);
            Console.WriteLine($"详细信息:\n{list2Result.Output}");

            // 3. 获取所有显示器设备信息
            var list3AllResult = await LeidianToolsNew.List3Async(LdPath);
            Console.WriteLine($"显示器设备信息:\n{list3AllResult.Output}");

            // 4. 获取指定模拟器的显示器信息
            var list3SpecificResult = await LeidianToolsNew.List3Async(LdPath, "3");
            Console.WriteLine($"指定模拟器显示器信息:\n{list3SpecificResult.Output}");

            // 5. 排列显示的模拟器
            var sortResult = await LeidianToolsNew.SortWndAsync(LdPath);
            Console.WriteLine($"排列窗口: {(sortResult.Success ? "成功" : "失败")}");
        }

        /// <summary>
        /// 通用方法使用示例
        /// </summary>
        public static async Task GenericMethodExample()
        {
            Console.WriteLine("\n=== 通用方法使用示例 ===");

            // 1. 使用通用方法执行自定义命令
            var customParams = new Dictionary<string, object>
            {
                { "resolution", "1280,720,240" },
                { "cpu", 3 },
                { "memory", 1536 }
            };
            var customResult = await LeidianToolsNew.ExecuteAsync(LdPath, "modify", "simulators_001", customParams);
            Console.WriteLine($"自定义命令: {(customResult.Success ? "成功" : "失败")}");

            // 2. 执行不需要设备参数的命令
            var noDeviceResult = await LeidianToolsNew.ExecuteAsync(LdPath, "list");
            Console.WriteLine($"无设备命令:\n{noDeviceResult.Output}");

            // 3. 执行只需要设备参数的命令
            var deviceOnlyResult = await LeidianToolsNew.ExecuteAsync(LdPath, "launch", "simulators_001");
            Console.WriteLine($"设备命令: {(deviceOnlyResult.Success ? "成功" : "失败")}");
        }

        /// <summary>
        /// 错误处理示例
        /// </summary>
        public static async Task ErrorHandlingExample()
        {
            Console.WriteLine("\n=== 错误处理示例 ===");

            // 1. 错误的路径
            var wrongPathResult = await LeidianToolsNew.ExecuteAsync(@"C:\wrong\path\dnconsole.exe", "list");
            Console.WriteLine($"错误路径: {wrongPathResult.Error}");

            // 2. 不存在的设备
            var wrongDeviceResult = await LeidianToolsNew.LaunchAsync(LdPath, "non_existent_device");
            Console.WriteLine($"不存在设备: {(wrongDeviceResult.Success ? "成功" : wrongDeviceResult.Error)}");

            // 3. 检查命令执行结果
            var result = await LeidianToolsNew.IsRunningAsync(LdPath, "test_device");
            if (result.Success)
            {
                Console.WriteLine($"命令执行成功: {result.Output}");
                Console.WriteLine($"退出代码: {result.ExitCode}");
            }
            else
            {
                Console.WriteLine($"命令执行失败: {result.Error}");
                Console.WriteLine($"退出代码: {result.ExitCode}");
            }
        }

        /// <summary>
        /// 批量操作示例
        /// </summary>
        public static async Task BatchOperationsExample()
        {
            Console.WriteLine("\n=== 批量操作示例 ===");

            // 1. 批量启动多个模拟器
            string[] simulators = { "sim_001", "sim_002", "sim_003" };
            
            foreach (var sim in simulators)
            {
                var result = await LeidianToolsNew.LaunchAsync(LdPath, sim);
                Console.WriteLine($"启动 {sim}: {(result.Success ? "成功" : "失败")}");
                
                // 等待一段时间再启动下一个
                await Task.Delay(2000);
            }

            // 2. 批量修改配置
            var batchModifyParams = LeidianToolsNew.CreateModifyParams(
                resolution: "900,1600,320",
                cpu: 2,
                memory: 2048,
                root: true
            );

            foreach (var sim in simulators)
            {
                var result = await LeidianToolsNew.ModifyAsync(LdPath, sim, batchModifyParams);
                Console.WriteLine($"修改 {sim} 配置: {(result.Success ? "成功" : "失败")}");
            }

            // 3. 批量安装应用
            string apkPath = @".\common_app.apk";
            string packageName = "com.example.commonapp";

            foreach (var sim in simulators)
            {
                var result = await LeidianToolsNew.InstallAppAsync(LdPath, sim, apkPath, packageName);
                Console.WriteLine($"在 {sim} 安装应用: {(result.Success ? "成功" : "失败")}");
            }
        }

        /// <summary>
        /// 运行所有示例
        /// </summary>
        public static async Task RunAllExamples()
        {
            try
            {
                await BasicOperationsExample();
                await SimulatorManagementExample();
                await BackupRestoreExample();
                await ConfigurationExample();
                await PropertyExample();
                await AdbExample();
                await AppManagementExample();
                await InformationQueryExample();
                await GenericMethodExample();
                await ErrorHandlingExample();
                await BatchOperationsExample();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"运行示例时发生错误: {ex.Message}");
            }
        }
    }
}
