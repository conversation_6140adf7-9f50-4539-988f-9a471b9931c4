using System;
using Microsoft.UI.Xaml;
using Microsoft.UI.Xaml.Data;

namespace App2.Converters
{
    public class IdToVisibilityConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, string language)
        {
            // id > 3 ��ʾ��id <= 3 ����
            if (value is int id && id <= 3)
                return Visibility.Collapsed;
            return Visibility.Visible;
        }

        public object ConvertBack(object value, Type targetType, object parameter, string language)
        {
            throw new NotImplementedException();
        }
    }
}