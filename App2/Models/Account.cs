using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace App2.Models
{
    /// <summary>
    /// 用户账号模型（社交媒体账号）
    /// </summary>
    public class UserAccount : INotifyPropertyChanged
    {
        public int Id { get; set; }
        public int UserId { get; set; }
        public int SimulatorId { get; set; }
        public int ProxyId { get; set; }
        public string AccountName { get; set; } = string.Empty;
        public string LoginAccount { get; set; } = string.Empty;
        public string Password { get; set; } = string.Empty;
        public AccountStatus AccountStatus { get; set; } = AccountStatus.Yes;
        public AccountSource Source { get; set; } = AccountSource.Instagram;
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }

        // 添加选中状态属性
        private bool _isSelected = false;
        public bool IsSelected
        {
            get => _isSelected;
            set
            {
                if (_isSelected != value)
                {
                    _isSelected = value;
                    OnPropertyChanged();
                }
            }
        }

        // PropertyChanged 事件
        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    /// 账号状态枚举
    /// </summary>
    public enum AccountStatus
    {
        Yes,    // 可用
        No      // 不可用
    }

    /// <summary>
    /// 账号来源枚举
    /// </summary>
    public enum AccountSource
    {
        Instagram,
        Tiktok,
        X       // X (formerly Twitter)
    }

    /// <summary>
    /// 账号操作结果
    /// </summary>
    public class AccountOperationResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public UserAccount? Account { get; set; }
    }

    /// <summary>
    /// 批量操作结果
    /// </summary>
    public class BatchOperationResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public int SuccessCount { get; set; }
        public int FailureCount { get; set; }
        public List<string> ErrorMessages { get; set; } = new List<string>();
    }

    public class ExecTaskAccount
    {
        public int SimulatorId { get; set; }
        public string LoginAccount { get; set; } = string.Empty;
        public string Password { get; set; } = string.Empty;
        public string ProxyId { get; set; } = string.Empty;
    }


}
