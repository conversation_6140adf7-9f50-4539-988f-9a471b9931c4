using System;
using System.ComponentModel;
// using Microsoft.UI;
// using Microsoft.UI.Xaml.Media;

namespace App2.Models
{
    public class Simulator : INotifyPropertyChanged
    {
        private int _id;
        private string _title = "";
        private string? _marikName;
        private string? _type;
        private int _status;
        private DateTime _createdAt;
        private DateTime? _updatedAt;
        private string? _config;

        // 添加公共无参构造函数
        public Simulator()
        {
            _createdAt = DateTime.Now;
        }

        public int Id
        {
            get => _id;
            set
            {
                _id = value;
                OnPropertyChanged(nameof(Id));
            }
        }

        public string Title
        {
            get => _title;
            set
            {
                _title = value;
                OnPropertyChanged(nameof(Title));
            }
        }

        public string? MarikName
        {
            get => _marikName;
            set
            {
                _marikName = value;
                OnPropertyChanged(nameof(MarikName));
            }
        }

        public string? Type
        {
            get => _type;
            set
            {
                _type = value;
                OnPropertyChanged(nameof(Type));
            }
        }

        public int Status
        {
            get => _status;
            set
            {
                _status = value;
                OnPropertyChanged(nameof(Status));
                OnPropertyChanged(nameof(StatusText));
            }
        }

        public DateTime CreatedAt
        {
            get => _createdAt;
            set
            {
                _createdAt = value;
                OnPropertyChanged(nameof(CreatedAt));
            }
        }

        public DateTime? UpdatedAt
        {
            get => _updatedAt;
            set
            {
                _updatedAt = value;
                OnPropertyChanged(nameof(UpdatedAt));
            }
        }

        public string? Config
        {
            get => _config;
            set
            {
                _config = value;
                OnPropertyChanged(nameof(Config));
            }
        }

        // 只读属性
        public string Name => Title;

        public string StatusText => Status switch
        {
            1 => "启用",
            2 => "关闭",
            _ => "未知"
        };

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
