using System;
using System.Collections.Generic;

namespace App2.Models
{
    /// <summary>
    /// 用户群体模型
    /// </summary>
    public class UserGroup
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Platform { get; set; } = string.Empty;
        public string Source { get; set; } = string.Empty;
        public string TaskSource { get; set; } = string.Empty;
        public string Content { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
    }
   
}