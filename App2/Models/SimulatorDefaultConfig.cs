using System.Text.Json.Serialization;
using System.Text.Json;

namespace App2.Models
{
    // 添加 JSON 源生成器上下文
    [JsonSerializable(typeof(SimulatorDefaultConfig))]
    [JsonSerializable(typeof(SimulatorConfig))]
    internal partial class AppJsonSerializerContext : JsonSerializerContext
    {
    }

    /// <summary>
    /// 模拟器默认配置
    /// </summary>
    public class SimulatorDefaultConfig
    {
        /// <summary>
        /// 模拟器类型，默认为雷电模拟器 leidian
        /// </summary>
        [JsonPropertyName("type")]
        public string Type { get; set; } = "leidian";

        [JsonPropertyName("performance")]
        public PerformanceSettings Performance { get; set; } = new();

        [JsonPropertyName("game")]
        public GameSettings Game { get; set; } = new();

        [JsonPropertyName("other")]
        public OtherSettings Other { get; set; } = new();
    }

    /// <summary>
    /// 性能设置
    /// </summary>
    public class PerformanceSettings
    {
        /// <summary>
        /// 分辨率 格式: "宽,高,DPI"
        /// </summary>
        [JsonPropertyName("resolution")]
        public string Resolution { get; set; } = "1080,1920,320";

        /// <summary>
        /// CPU核心数 1-4
        /// </summary>
        [JsonPropertyName("cpu")]
        public string Cpu { get; set; } = "24";

        /// <summary>
        /// 内存大小 256|512|768|1024|1536|2048|4096|8192
        /// </summary>
        [JsonPropertyName("memory")]
        public string Memory { get; set; } = "4096";

        // 用于UI绑定的分离属性
        [JsonIgnore]
        public int Width
        {
            get
            {
                var parts = Resolution.Split(',');
                return parts.Length > 0 && int.TryParse(parts[0], out int w) ? w : 900;
            }
            set
            {
                var parts = Resolution.Split(',');
                parts[0] = value.ToString();
                Resolution = string.Join(",", parts);
            }
        }

        [JsonIgnore]
        public int Height
        {
            get
            {
                var parts = Resolution.Split(',');
                return parts.Length > 1 && int.TryParse(parts[1], out int h) ? h : 1600;
            }
            set
            {
                var parts = Resolution.Split(',');
                if (parts.Length < 2) parts = new[] { parts.Length > 0 ? parts[0] : "1080", "1920", "320" };
                parts[1] = value.ToString();
                Resolution = string.Join(",", parts);
            }
        }

        [JsonIgnore]
        public int Dpi
        {
            get
            {
                var parts = Resolution.Split(',');
                return parts.Length > 2 && int.TryParse(parts[2], out int d) ? d : 320;
            }
            set
            {
                var parts = Resolution.Split(',');
                if (parts.Length < 3) parts = new[] { parts.Length > 0 ? parts[0] : "1080", parts.Length > 1 ? parts[1] : "1920", "320" };
                parts[2] = value.ToString();
                Resolution = string.Join(",", parts);
            }
        }

        [JsonIgnore]
        public int CpuInt
        {
            get => int.TryParse(Cpu, out int c) ? c : 2;
            set => Cpu = value.ToString();
        }

        [JsonIgnore]
        public int MemoryInt
        {
            get => int.TryParse(Memory, out int m) ? m : 2048;
            set => Memory = value.ToString();
        }
    }

    /// <summary>
    /// 游戏设置
    /// </summary>
    public class GameSettings
    {
        /// <summary>
        /// 帧率 0-60
        /// </summary>
        [JsonPropertyName("fps")]
        public string Fps { get; set; } = "60";

        [JsonIgnore]
        public int FpsInt
        {
            get => int.TryParse(Fps, out int f) ? f : 60;
            set => Fps = value.ToString();
        }
    }

    /// <summary>
    /// 其他设置
    /// </summary>
    public class OtherSettings
    {
        /// <summary>
        /// 自动旋转 1|0
        /// </summary>
        [JsonPropertyName("autorotate")]
        public string AutoRotate { get; set; } = "1";

        /// <summary>
        /// 锁定窗口 1|0
        /// </summary>
        [JsonPropertyName("lockwindow")]
        public string LockWindow { get; set; } = "0";

        /// <summary>
        /// ROOT权限 1|0
        /// </summary>
        [JsonPropertyName("root")]
        public string Root { get; set; } = "1";

        [JsonIgnore]
        public bool AutoRotateBool
        {
            get => AutoRotate == "1";
            set => AutoRotate = value ? "1" : "0";
        }

        [JsonIgnore]
        public bool LockWindowBool
        {
            get => LockWindow == "1";
            set => LockWindow = value ? "1" : "0";
        }

        [JsonIgnore]
        public bool RootBool
        {
            get => Root == "1";
            set => Root = value ? "1" : "0";
        }
    }

    /// <summary>
    /// 模拟器配置（用于具体的模拟器实例）
    /// </summary>
    public class SimulatorConfig
    {
        // 模拟器类型
        public string? Type { get; set; }
        
        // 性能设置
        public int? Width { get; set; }
        public int? Height { get; set; }
        public int? Dpi { get; set; }
        public int? Cpu { get; set; }
        public int? Memory { get; set; }
        
        // 设备信息设置
        public string? Manufacturer { get; set; }
        public string? Model { get; set; }
        public string? PhoneNumber { get; set; }
        public string? Imei { get; set; }
        public string? Imsi { get; set; }
        public string? AndroidId { get; set; }
        
        // 其他高级设置
        public string? SimSerial { get; set; }
        public string? Mac { get; set; }
        
        // 功能开关设置
        public bool? AutoRotate { get; set; }
        public bool? LockWindow { get; set; }
        public bool? Root { get; set; }
    }
}
