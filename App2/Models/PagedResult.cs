using System;
using System.Collections.Generic;

namespace App2.Models
{
    /// <summary>
    /// ��ҳ���ģ��
    /// </summary>
    /// <typeparam name="T">��������</typeparam>
    public class PagedResult<T>
    {
        /// <summary>
        /// ��ǰҳ����
        /// </summary>
        public List<T> Data { get; set; } = new List<T>();

        /// <summary>
        /// ��ǰҳ�루��1��ʼ��
        /// </summary>
        public int CurrentPage { get; set; }

        /// <summary>
        /// ÿҳ��¼��
        /// </summary>
        public int PageSize { get; set; }

        /// <summary>
        /// �ܼ�¼��
        /// </summary>
        public int TotalCount { get; set; }

        /// <summary>
        /// ��ҳ��
        /// </summary>
        public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);

        /// <summary>
        /// �Ƿ�����һҳ
        /// </summary>
        public bool HasPreviousPage => CurrentPage > 1;

        /// <summary>
        /// �Ƿ�����һҳ
        /// </summary>
        public bool HasNextPage => CurrentPage < TotalPages;

        /// <summary>
        /// ��ʼ��¼���
        /// </summary>
        public int StartIndex => (CurrentPage - 1) * PageSize + 1;

        /// <summary>
        /// ������¼���
        /// </summary>
        public int EndIndex => Math.Min(CurrentPage * PageSize, TotalCount);
    }
}