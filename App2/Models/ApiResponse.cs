using System;

namespace App2.Models
{
    /// <summary>
    /// 通用API响应模型
    /// </summary>
    public class ApiResponse<T>
    {
        /// <summary>
        /// 状态码：200成功，-100失败
        /// </summary>
        public int Code { get; set; }

        /// <summary>
        /// 返回的数据
        /// </summary>
        public T? Data { get; set; }

        /// <summary>
        /// 消息提示
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// 创建一个成功的响应
        /// </summary>
        /// <param name="data">返回的数据</param>
        /// <param name="message">提示消息</param>
        public static ApiResponse<T> Success(T data, string message = "success")
        {
            return new ApiResponse<T>
            {
                Code = 200,
                Data = data,
                Message = message
            };
        }

        /// <summary>
        /// 创建一个错误的响应
        /// </summary>
        /// <param name="message">错误消息</param>
        /// <param name="data">附加数据</param>
        public static ApiResponse<T> Error(string message = "error", T? data = default)
        {
            return new ApiResponse<T>
            {
                Code = -100,
                Data = data,
                Message = message
            };
        }
    }
}
