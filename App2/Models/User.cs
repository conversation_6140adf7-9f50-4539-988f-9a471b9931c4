using System;

namespace App2.Models
{
    /// <summary>
    /// 用户模型
    /// </summary>
    public class User
    {
        public int Id { get; set; }
        public string Username { get; set; } = string.Empty;
        
        /// <summary>
        /// 密码哈希值（推荐使用）
        /// </summary>
        public string PasswordHash { get; set; } = string.Empty;
        
        /// <summary>
        /// 简单密码（兼容现有代码）
        /// </summary>
        public string Password { get; set; } = string.Empty;
        
        public string Salt { get; set; } = string.Empty;
        public string? Email { get; set; }
        public string? Phone { get; set; }
        public string? RealName { get; set; }
        public UserRole Role { get; set; } = UserRole.Operator;
        public UserStatus Status { get; set; } = UserStatus.Active;
        
        /// <summary>
        /// 是否激活（兼容现有代码）
        /// </summary>
        public bool IsActive 
        { 
            get => Status == UserStatus.Active; 
            set => Status = value ? UserStatus.Active : UserStatus.Inactive; 
        }
        
        public DateTime? LastLoginTime { get; set; }
        public string? LastLoginIp { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
    }

    /// <summary>
    /// 用户角色枚举
    /// </summary>
    public enum UserRole
    {
        Admin,
        Manager,
        Operator
    }

    /// <summary>
    /// 用户状态枚举
    /// </summary>
    public enum UserStatus
    {
        Active,
        Inactive,
        Locked
    }

    /// <summary>
    /// 登录结果
    /// </summary>
    public class LoginResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public User? User { get; set; }
        public string? SessionToken { get; set; }
    }
}