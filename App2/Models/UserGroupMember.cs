using System;

namespace App2.Models
{
    /// <summary>
    /// 用户组成员模型
    /// </summary>
    public class UserGroupMember
    {
        public int Id { get; set; }
        public string Nickname { get; set; } = string.Empty;
        public string UserId { get; set; } = string.Empty;
        public string Platform { get; set; } = string.Empty;
        public string Content { get; set; } = string.Empty;
        public string Source { get; set; } = string.Empty;
        
        /// <summary>
        /// 任务ID
        /// </summary>
        public int TaskId { get; set; } = 0;
        
        /// <summary>
        /// 任务来源文本
        /// </summary>
        public string TaskSource { get; set; } = string.Empty;
        
        /// <summary>
        /// 获取任务来源的文本描述（为了向后兼容保留）
        /// </summary>
        public string TaskSourceText => string.IsNullOrEmpty(TaskSource) ? "未知" : TaskSource;
    }
}
