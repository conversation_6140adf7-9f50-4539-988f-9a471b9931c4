using App2.Models;
using System;
using System.Collections.Generic;
using System.Security.Principal;

namespace App2.Models
{
    /// <summary>
    /// �����ģ��
    /// </summary>
    public class Tasks
    {
        public int Id { get; set; }
        public int Tid { get; set; }
        public string Platform { get; set; } = string.Empty;
        public string Uids { get; set; } = string.Empty;
        public string SearchUrl { get; set; } = string.Empty;
        public string Keywords { get; set; } = string.Empty;
        public string Content { get; set; } = string.Empty;
        public string Results { get; set; } = string.Empty;
        public int Status { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
    }

    public class TasksList
    {
        public int Id { get; set; }
        public int Tid { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Platform { get; set; } = string.Empty;
        public string Uids { get; set; } = string.Empty;
        public string AccountNum { get; set; } = string.Empty;
        public string SearchUrl { get; set; } = string.Empty;
        public string Keywords { get; set; } = string.Empty;
        public string Content { get; set; } = string.Empty;
        public string Results { get; set; } = string.Empty;
        public int Status { get; set; }
        public string StatusName { get; set; } = string.Empty;
        public Boolean StatusAble { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
    }

    public class TasksExec
    {
        public ExecTaskAccount Account { get; set; } 
        public TaskType TaskType { get; set; }

        public ProxyTaskData Proxy { get; set; }
        public int Id { get; set; }
        public string Platform { get; set; } = string.Empty;
        public string SearchUrl { get; set; } = string.Empty;
        public string Keywords { get; set; } = string.Empty;
        public string Content { get; set; } = string.Empty;
    }
}