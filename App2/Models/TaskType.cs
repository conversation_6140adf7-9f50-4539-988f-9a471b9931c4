using System;
using System.Collections.Generic;
using System.ComponentModel;

namespace App2.Models
{
    /// <summary>
    /// 任务类型模型
    /// </summary>
    public class TaskType : INotifyPropertyChanged
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public int UserNum { get; set; }
        public int IntervalTimeMin { get; set; }
        public int IntervalTimeMax { get; set; }
        public int CountTimeMin { get; set; }
        public int CountTimeMax { get; set; }
        public int FansNum { get; set; }

        private string _content;
        public string Content
        {
            get => _content;
            set
            {
                if (_content != value)
                {
                    _content = value;
                    OnPropertyChanged(nameof(Content));
                    UpdateCheckStatesFromContent();
                }
            }
        }

        private bool _isViewChecked;
        public bool IsViewChecked
        {
            get => _isViewChecked;
            set
            {
                if (_isViewChecked != value)
                {
                    _isViewChecked = value;
                    OnPropertyChanged(nameof(IsViewChecked));
                    UpdateContentFromChecks();
                }
            }
        }

        private bool _isCommentChecked;
        public bool IsCommentChecked
        {
            get => _isCommentChecked;
            set
            {
                if (_isCommentChecked != value)
                {
                    _isCommentChecked = value;
                    OnPropertyChanged(nameof(IsCommentChecked));
                    UpdateContentFromChecks();
                }
            }
        }

        private bool _isLikeChecked;
        public bool IsLikeChecked
        {
            get => _isLikeChecked;
            set
            {
                if (_isLikeChecked != value)
                {
                    _isLikeChecked = value;
                    OnPropertyChanged(nameof(IsLikeChecked));
                    UpdateContentFromChecks();
                }
            }
        }

        private void UpdateCheckStatesFromContent()
        {
            IsViewChecked = Content?.Contains("浏览") ?? false;
            IsCommentChecked = Content?.Contains("评论") ?? false;
            IsLikeChecked = Content?.Contains("点赞") ?? false;
        }

        private void UpdateContentFromChecks()
        {
            var list = new List<string>();
            if (IsViewChecked) list.Add("浏览");
            if (IsCommentChecked) list.Add("评论");
            if (IsLikeChecked) list.Add("点赞");
            Content = string.Join(",", list);
        }

        public event PropertyChangedEventHandler PropertyChanged;
        protected void OnPropertyChanged(string name)
            => PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(name));
    }

    public class TaskTypeRadio
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
    }
}