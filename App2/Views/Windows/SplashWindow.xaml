<?xml version="1.0" encoding="utf-8"?>
<Window
    x:Class="App2.Views.Windows.SplashWindow"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <Grid>
        <!-- 背景渐变 -->
        <Grid.Background>
            <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                <GradientStop Color="#FF6B73FF" Offset="0"/>
                <GradientStop Color="#FF9DD5FF" Offset="1"/>
            </LinearGradientBrush>
        </Grid.Background>

        <!-- 主要内容 -->
        <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
            <StackPanel.Spacing>30</StackPanel.Spacing>
            
            <!-- 应用图标 -->
            <Grid Width="120" Height="120">
                <!-- 阴影效果 -->
                <Border Width="120" Height="120" CornerRadius="20" 
                       Background="#20000000" Margin="2,2,0,0"/>
                <!-- 主图标 -->
                <Border Width="120" Height="120" CornerRadius="20" Background="White">
                    <FontIcon FontFamily="Segoe MDL2 Assets" Glyph="&#xE7C3;" 
                             FontSize="60" Foreground="#FF6B73FF"/>
                </Border>
            </Grid>

            <!-- 应用名称 -->
            <TextBlock Text="推广系统" FontSize="36" FontWeight="Bold" 
                      Foreground="White" HorizontalAlignment="Center"/>

            <!-- 加载动画 -->
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <StackPanel.Spacing>15</StackPanel.Spacing>
                <ProgressRing x:Name="LoadingRing" IsActive="True" Width="32" Height="32" 
                             Foreground="White"/>
                <TextBlock x:Name="LoadingText" Text="正在加载..." FontSize="16" 
                          Foreground="White" VerticalAlignment="Center"/>
            </StackPanel>

            <!-- 版本信息 -->
            <TextBlock x:Name="VersionText" Text="版本 1.0.0" FontSize="12" 
                      Foreground="#B0FFFFFF" HorizontalAlignment="Center" 
                      Margin="0,30,0,0"/>
        </StackPanel>

        <!-- 底部信息 -->
        <StackPanel VerticalAlignment="Bottom" HorizontalAlignment="Center" 
                   Margin="0,0,0,50">
            <StackPanel.Spacing>10</StackPanel.Spacing>
            <TextBlock Text="© 2025 推广系统. All rights reserved." 
                      FontSize="11" Foreground="#80FFFFFF" 
                      HorizontalAlignment="Center"/>
        </StackPanel>
    </Grid>
</Window>