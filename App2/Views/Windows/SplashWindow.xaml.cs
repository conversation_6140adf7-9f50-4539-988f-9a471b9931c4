using Microsoft.UI.Xaml;
using Microsoft.UI.Windowing;
using Windows.Graphics;
using System;
using System.Threading.Tasks;
using System.Reflection;

namespace App2.Views.Windows
{
    public sealed partial class SplashWindow : Window
    {
        public SplashWindow()
        {
            InitializeComponent();
            this.ExtendsContentIntoTitleBar = true;
            this.SetTitleBar(null);
            
            // 设置窗口大小和位置
            SetupWindow();
            
            // 设置版本信息
            SetVersionInfo();
        }

        /// <summary>
        /// 设置窗口属性
        /// </summary>
        private void SetupWindow()
        {
            try
            {
                if (AppWindow != null)
                {
                    // 设置窗口大小
                    var size = new SizeInt32(500, 400);
                    AppWindow.Resize(size);
                    
                    // 居中显示窗口
                    var displayArea = DisplayArea.GetFromWindowId(AppWindow.Id, DisplayAreaFallback.Primary);
                    if (displayArea != null)
                    {
                        var centerX = (displayArea.WorkArea.Width - size.Width) / 2;
                        var centerY = (displayArea.WorkArea.Height - size.Height) / 2;
                        AppWindow.Move(new PointInt32(centerX, centerY));
                    }

                    // 设置窗口标题
                    AppWindow.Title = "App2 - 启动中";
                    
                    // 禁用最小化和最大化按钮
                    if (AppWindow.Presenter is OverlappedPresenter presenter)
                    {
                        presenter.IsMinimizable = false;
                        presenter.IsMaximizable = false;
                        presenter.IsResizable = false;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"设置启动窗口失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 设置版本信息
        /// </summary>
        private void SetVersionInfo()
        {
            try
            {
                var version = Assembly.GetExecutingAssembly().GetName().Version;
                if (version != null)
                {
                    VersionText.Text = $"版本 {version.Major}.{version.Minor}.{version.Build}";
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取版本信息失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 更新加载状态文本
        /// </summary>
        /// <param name="text">状态文本</param>
        public void UpdateLoadingText(string text)
        {
            try
            {
                DispatcherQueue.TryEnqueue(() =>
                {
                    LoadingText.Text = text;
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"更新加载文本失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 显示启动动画
        /// </summary>
        /// <param name="duration">动画持续时间（毫秒）</param>
        public async Task ShowSplashAsync(int duration = 2000)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"显示启动动画，持续时间: {duration}ms");
                
                // 模拟加载过程
                var steps = new[]
                {
                    "初始化应用...",
                    "加载配置...",
                    "验证会话...",
                    "准备界面...",
                    "启动完成"
                };

                var stepDuration = duration / steps.Length;
                
                foreach (var step in steps)
                {
                    UpdateLoadingText(step);
                    await Task.Delay(stepDuration);
                }
                
                System.Diagnostics.Debug.WriteLine("启动动画完成");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"显示启动动画失败: {ex.Message}");
            }
        }
    }
}