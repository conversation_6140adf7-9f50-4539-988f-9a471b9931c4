<?xml version="1.0" encoding="utf-8"?>
<Window
    x:Class="App2.Views.Windows.LoginWindow"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:local="using:App2.Views.Windows"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    mc:Ignorable="d"
    Title="登录">

    <Window.SystemBackdrop>
        <MicaBackdrop />
    </Window.SystemBackdrop>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <Border 
            Width="450"
            Height="500"
            Background="{ThemeResource CardBackgroundFillColorDefaultBrush}"
            CornerRadius="8"
            Margin="0"
            Padding="24"
            HorizontalAlignment="Center"
            VerticalAlignment="Center">
            <Border.Shadow>
                <ThemeShadow />
            </Border.Shadow>
            
            <StackPanel Spacing="20">
   
                <TextBlock Text="推广系统"
                          FontSize="24"
                          Padding="24"
                          FontWeight="SemiBold"
                          HorizontalAlignment="Center"
                          Margin="0,0,0,8"/>

                <StackPanel Spacing="6">
                    <TextBlock Text="账号" 
                              FontWeight="Medium"
                              Foreground="{ThemeResource TextFillColorSecondaryBrush}"/>
                    <TextBox x:Name="UsernameTextBox"
                            PlaceholderText="请输入账号"
                            Height="36"/>
                </StackPanel>

                <StackPanel Spacing="6">
                    <TextBlock Text="密码" 
                              FontWeight="Medium"
                              Foreground="{ThemeResource TextFillColorSecondaryBrush}"/>
                    <PasswordBox x:Name="PasswordBox"
                                PlaceholderText="请输入密码"
                                Height="36"/>
                </StackPanel>

            
                <CheckBox x:Name="RememberPasswordCheckBox"
                         Content="记住密码"
                         Margin="0,4,0,0"/>

                <Button x:Name="LoginButton"
                       Content="登录"
                       Style="{StaticResource AccentButtonStyle}"
                       HorizontalAlignment="Stretch"
                       Height="36"
                       Margin="0,12,0,0"
                       Click="LoginButton_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
