<?xml version="1.0" encoding="utf-8"?>
<Window
    x:Class="App2.Views.Windows.MainWindow"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:local="using:App2.Views.Windows"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    mc:Ignorable="d"
    Title="推广系统">

    <Window.SystemBackdrop>
        <MicaBackdrop />
    </Window.SystemBackdrop>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="48"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- 顶部标题栏 -->
        <Grid x:Name="TitleBarGrid" Grid.Row="0" Background="{ThemeResource CardBackgroundFillColorDefaultBrush}">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>
            
            <TextBlock Text="推广系统" 
                      Grid.Column="0"
                      VerticalAlignment="Center"
                      Margin="16,0,0,0"
                      FontSize="16"
                      FontWeight="SemiBold"/>
            
            <!-- 用户信息菜单 -->
            <StackPanel Grid.Column="2" 
                       Orientation="Horizontal" 
                       VerticalAlignment="Center"
                       Margin="0,0,16,0">
                <Button x:Name="UserMenuButton"
                       Background="Transparent"
                       BorderThickness="0"
                       Padding="8,4"
                       CornerRadius="4">
                    <Button.Flyout>
                        <MenuFlyout x:Name="UserMenuFlyout" Placement="Bottom">
                            <MenuFlyoutItem x:Name="ChangePasswordMenuItem"
                                          Text="修改密码"
                                          Click="ChangePasswordMenuItem_Click">
                                <MenuFlyoutItem.Icon>
                                    <FontIcon Glyph="&#xE8AC;"/>
                                </MenuFlyoutItem.Icon>
                            </MenuFlyoutItem>
                            <MenuFlyoutSeparator/>
                            <MenuFlyoutItem x:Name="LogoutMenuItem"
                                          Text="退出登录"
                                          Click="LogoutMenuItem_Click">
                                <MenuFlyoutItem.Icon>
                                    <FontIcon Glyph="&#xE7E8;"/>
                                </MenuFlyoutItem.Icon>
                            </MenuFlyoutItem>
                            <MenuFlyoutItem x:Name="ExitAppMenuItem"
                                          Text="关闭程序"
                                          Click="ExitAppMenuItem_Click">
                                <MenuFlyoutItem.Icon>
                                    <FontIcon Glyph="&#xE8BB;"/>
                                </MenuFlyoutItem.Icon>
                            </MenuFlyoutItem>
                        </MenuFlyout>
                    </Button.Flyout>
                    <StackPanel Orientation="Horizontal">
                        <PersonPicture ProfilePicture="/Assets/Images/user.png" 
                                      Width="32" 
                                      Height="32"/>
                        <StackPanel Orientation="Vertical" 
                                   VerticalAlignment="Center"
                                   Margin="8,0,4,0">
                            <TextBlock x:Name="UserNameTextBlock"
                                      Text="用户名"
                                      FontSize="12"
                                      FontWeight="Medium"/>
                            <TextBlock x:Name="UserRoleTextBlock"
                                      Text="角色"
                                      FontSize="10"
                                      Foreground="{ThemeResource TextFillColorSecondaryBrush}"/>
                        </StackPanel>
                        <FontIcon Glyph="&#xE70D;" 
                                 FontSize="12"
                                 VerticalAlignment="Center"
                                 Foreground="{ThemeResource TextFillColorSecondaryBrush}"/>
                    </StackPanel>
                </Button>
            </StackPanel>
        </Grid>

        <!-- 主内容区 -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="220"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- 左侧导航菜单 -->
            <NavigationView x:Name="NavView"
                           Grid.Column="0"
                           IsSettingsVisible="True"
                           IsBackButtonVisible="Collapsed"
                           PaneDisplayMode="Left"
                           SelectionChanged="NavView_SelectionChanged"
                           Background="{ThemeResource SolidBackgroundFillColorBaseBrush}">
                <NavigationView.MenuItems>
                    <!-- <NavigationViewItem x:Name="ChatMenuItem" 
                                       Content="聊天消息" 
                                       Tag="ChatPage">
                        <NavigationViewItem.Icon>
                            <FontIcon Glyph="&#xE8BD;"/>
                        </NavigationViewItem.Icon>
                    </NavigationViewItem> -->
                    <NavigationViewItem x:Name="SimulatorMenuItem" 
                                       Content="模拟器" 
                                       Tag="SimulatorPage"
                                       IsSelected="True">
                        <NavigationViewItem.Icon>
                            <FontIcon Glyph="&#xE7F4;"/>
                        </NavigationViewItem.Icon>
                    </NavigationViewItem>

                    <NavigationViewItem x:Name="AccountMenuItem" 
                                       Content="账号管理" 
                                       Tag="AccountPage">
                        <NavigationViewItem.Icon>
                            <FontIcon Glyph="&#xE77B;"/>
                        </NavigationViewItem.Icon>
                    </NavigationViewItem>
                    
                    <NavigationViewItem x:Name="UserGroupMenuItem" 
                                       Content="用户群体" 
                                       Tag="UserGroupPage">
                        <NavigationViewItem.Icon>
                            <FontIcon Glyph="&#xE716;"/>
                        </NavigationViewItem.Icon>
                    </NavigationViewItem>
                    
                    <NavigationViewItem x:Name="TaskMenuItem" 
                                       Content="任务执行" 
                                       Tag="TaskPage">
                        <NavigationViewItem.Icon>
                            <FontIcon Glyph="&#xE7C0;"/>
                        </NavigationViewItem.Icon>
                    </NavigationViewItem>
                    
                    <NavigationViewItem x:Name="ConfigMenuItem" 
                                       Content="代理配置" 
                                       Tag="ConfigPage">
                        <NavigationViewItem.Icon>
                            <FontIcon Glyph="&#xE713;"/>
                        </NavigationViewItem.Icon>
                    </NavigationViewItem>
                </NavigationView.MenuItems>
            </NavigationView>

            <!-- 右侧内容区 -->
            <Grid x:Name="ContentGrid" Grid.Column="1" Margin="0,0,0,0">
                <Frame x:Name="ContentFrame"/>
            </Grid>
        </Grid>
        
        <!-- 修改密码对话框 -->
        <ContentDialog x:Name="ChangePasswordDialog"
                      Title="修改密码"
                      PrimaryButtonText="确定"
                      CloseButtonText="取消"
                      DefaultButton="Primary"
                      IsPrimaryButtonEnabled="{x:Bind IsPasswordValid, Mode=OneWay}"
                      PrimaryButtonClick="ChangePasswordDialog_PrimaryButtonClick">
            <StackPanel Spacing="12" MinWidth="320">
                <PasswordBox x:Name="OldPasswordBox" 
                           Header="原密码" 
                           PlaceholderText="请输入原密码"
                           PasswordRevealMode="Peek"
                           PasswordChanged="PasswordBox_PasswordChanged"/>
                
                <PasswordBox x:Name="NewPasswordBox" 
                           Header="新密码" 
                           PlaceholderText="请输入新密码"
                           PasswordRevealMode="Peek"
                           PasswordChanged="PasswordBox_PasswordChanged"/>
                
                <PasswordBox x:Name="ConfirmPasswordBox" 
                           Header="确认新密码" 
                           PlaceholderText="请再次输入新密码"
                           PasswordRevealMode="Peek"
                           PasswordChanged="PasswordBox_PasswordChanged"/>
                
                <InfoBar x:Name="PasswordInfoBar"
                       IsOpen="False"
                       Severity="Error"
                       Title="错误"
                       Message="密码不能为空且新密码必须一致"/>
            </StackPanel>
        </ContentDialog>
    </Grid>
</Window>

