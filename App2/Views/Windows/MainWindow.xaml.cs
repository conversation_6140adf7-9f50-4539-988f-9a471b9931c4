using System;
using Microsoft.UI.Xaml;
using Microsoft.UI.Xaml.Controls;
using Microsoft.UI.Xaml.Media.Animation;
using App2.Views.Pages;
using Microsoft.UI.Windowing;
using Windows.Graphics;
using App2.Services;
using System.Threading.Tasks;

namespace App2.Views.Windows
{
    /// <summary>
    /// 管理后台主窗口
    /// </summary>
    public sealed partial class MainWindow : Window
    {
        // 密码验证属性
        private bool IsPasswordValid =>
            !string.IsNullOrEmpty(OldPasswordBox?.Password) &&
            !string.IsNullOrEmpty(NewPasswordBox?.Password) &&
            !string.IsNullOrEmpty(ConfirmPasswordBox?.Password) &&
            NewPasswordBox?.Password == ConfirmPasswordBox?.Password;
        public MainWindow()
        {
            try
            {
                InitializeComponent();

                // 扩展内容到标题栏并隐藏系统按钮
                ExtendsContentIntoTitleBar = true;

                // 设置顶部Grid作为可拖拽的标题栏区域
                try
                {
                    SetTitleBar(TitleBarGrid);
                }
                catch
                {
                    // 如果设置标题栏失败，继续执行
                }

                // 设置窗口大小
                if (AppWindow != null)
                {
                    try
                    {
                        // 设置窗口的首选大小
                        var appWindow = AppWindow;
                        var size = new SizeInt32(1200, 800);
                        appWindow.Resize(size);

                        // 完全隐藏标题栏按钮
                        if (appWindow.TitleBar != null)
                        {
                            // 隐藏图标和系统菜单
                            appWindow.TitleBar.IconShowOptions = IconShowOptions.HideIconAndSystemMenu;

                            // 设置标题栏高度为0来完全隐藏按钮区域
                            appWindow.TitleBar.PreferredHeightOption = TitleBarHeightOption.Collapsed;

                            // 设置所有按钮状态为完全透明
                            var transparent = Microsoft.UI.Colors.Transparent;
                            appWindow.TitleBar.ButtonBackgroundColor = transparent;
                            appWindow.TitleBar.ButtonForegroundColor = transparent;
                            appWindow.TitleBar.ButtonHoverBackgroundColor = transparent;
                            appWindow.TitleBar.ButtonHoverForegroundColor = transparent;
                            appWindow.TitleBar.ButtonPressedBackgroundColor = transparent;
                            appWindow.TitleBar.ButtonPressedForegroundColor = transparent;
                            appWindow.TitleBar.ButtonInactiveBackgroundColor = transparent;
                            appWindow.TitleBar.ButtonInactiveForegroundColor = transparent;
                        }

                        // 居中显示窗口
                        var displayArea = DisplayArea.GetFromWindowId(appWindow.Id, DisplayAreaFallback.Primary);
                        if (displayArea != null)
                        {
                            var centerX = (displayArea.WorkArea.Width - size.Width) / 2;
                            var centerY = (displayArea.WorkArea.Height - size.Height) / 2;
                            appWindow.Move(new PointInt32(centerX, centerY));
                        }
                    }
                    catch
                    {
                        // 窗口设置失败，使用默认设置
                    }
                }

                // 显示当前用户信息
                LoadUserInfo();

                // 默认导航到模拟器页面（避免AccountPage不存在的问题）
                try
                {
                    ContentFrame.Navigate(typeof(SimulatorPage), null, new EntranceNavigationTransitionInfo());
                }
                catch
                {
                    // 如果导航失败，不做任何操作
                }

                // 检查是否是首次运行，如果是则显示提示对话框
                CheckFirstRunAndShowTipsAsync();

                // 启动边距更新定时器
                StartMarginUpdateTimer();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"MainWindow初始化失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 加载用户信息
        /// </summary>
        private void LoadUserInfo()
        {
            try
            {
                var currentUser = SessionService.CurrentUser;
                if (currentUser != null)
                {
                    UserNameTextBlock.Text = currentUser.RealName ?? currentUser.Username;
                    UserRoleTextBlock.Text = currentUser.Role switch
                    {
                        App2.Models.UserRole.Admin => "管理员",
                        App2.Models.UserRole.Manager => "管理者",
                        App2.Models.UserRole.Operator => "操作员",
                        _ => "未知角色"
                    };
                }
                else
                {
                    UserNameTextBlock.Text = "未知用户";
                    UserRoleTextBlock.Text = "未知角色";
                }
            }
            catch
            {
                // 如果加载用户信息失败，设置默认值
                UserNameTextBlock.Text = "系统用户";
                UserRoleTextBlock.Text = "用户";
            }
        }

        /// <summary>
        /// 修改密码菜单项点击事件
        /// </summary>
        private async void ChangePasswordMenuItem_Click(object sender, RoutedEventArgs e)
        {
            // 重置密码框和提示
            OldPasswordBox.Password = string.Empty;
            NewPasswordBox.Password = string.Empty;
            ConfirmPasswordBox.Password = string.Empty;
            PasswordInfoBar.IsOpen = false;

            // 显示修改密码对话框
            await ChangePasswordDialog.ShowAsync();
        }

        /// <summary>
        /// 退出登录菜单项点击事件
        /// </summary>
        private async void LogoutMenuItem_Click(object sender, RoutedEventArgs e)
        {
            ContentDialog confirmDialog = new ContentDialog
            {
                Title = "确认退出",
                Content = "确定要退出登录吗？",
                PrimaryButtonText = "确定",
                CloseButtonText = "取消",
                DefaultButton = ContentDialogButton.Primary,
                XamlRoot = Content.XamlRoot
            };

            ContentDialogResult result = await confirmDialog.ShowAsync();

            if (result == ContentDialogResult.Primary)
            {
                // 清除会话信息
                await SessionService.LogoutAsync();

                // 打开登录窗口
                var loginWindow = new LoginWindow();
                loginWindow.Activate();

                // 关闭当前窗口
                Close();
            }
        }

        /// <summary>
        /// 关闭程序菜单项点击事件
        /// </summary>
        private async void ExitAppMenuItem_Click(object sender, RoutedEventArgs e)
        {
            ContentDialog confirmDialog = new ContentDialog
            {
                Title = "确认关闭",
                Content = "确定要关闭程序吗？",
                PrimaryButtonText = "确定",
                CloseButtonText = "取消",
                DefaultButton = ContentDialogButton.Primary,
                XamlRoot = Content.XamlRoot
            };

            ContentDialogResult result = await confirmDialog.ShowAsync();

            if (result == ContentDialogResult.Primary)
            {
                // 直接关闭应用程序，不清除会话信息
                Application.Current.Exit();
            }
        }

        /// <summary>
        /// 密码框内容变更事件
        /// </summary>
        private void PasswordBox_PasswordChanged(object sender, RoutedEventArgs e)
        {
            // 验证密码
            if (OldPasswordBox != null && NewPasswordBox != null && ConfirmPasswordBox != null)
            {
                bool isOldPasswordEmpty = string.IsNullOrEmpty(OldPasswordBox.Password);
                bool isNewPasswordEmpty = string.IsNullOrEmpty(NewPasswordBox.Password);
                bool isConfirmPasswordEmpty = string.IsNullOrEmpty(ConfirmPasswordBox.Password);
                bool isPasswordMatch = NewPasswordBox.Password == ConfirmPasswordBox.Password;

                // 显示或隐藏错误信息
                if ((isOldPasswordEmpty || isNewPasswordEmpty || isConfirmPasswordEmpty) &&
                    (OldPasswordBox.Password.Length > 0 || NewPasswordBox.Password.Length > 0 || ConfirmPasswordBox.Password.Length > 0))
                {
                    PasswordInfoBar.Message = "所有密码字段不能为空";
                    PasswordInfoBar.IsOpen = true;
                }
                else if (!isPasswordMatch && !isNewPasswordEmpty && !isConfirmPasswordEmpty)
                {
                    PasswordInfoBar.Message = "新密码与确认密码不一致";
                    PasswordInfoBar.IsOpen = true;
                }
                else
                {
                    PasswordInfoBar.IsOpen = false;
                }

                // 通知绑定属性更新
                ChangePasswordDialog.IsPrimaryButtonEnabled = IsPasswordValid;
            }
        }

        /// <summary>
        /// 修改密码对话框确认按钮点击事件
        /// </summary>
        private async void ChangePasswordDialog_PrimaryButtonClick(ContentDialog sender, ContentDialogButtonClickEventArgs args)
        {
            // 延迟关闭对话框，以便显示处理结果
            var deferral = args.GetDeferral();

            try
            {
                // 显示加载状态
                PasswordInfoBar.Title = "处理中";
                PasswordInfoBar.Message = "正在修改密码...";
                PasswordInfoBar.Severity = InfoBarSeverity.Informational;
                PasswordInfoBar.IsOpen = true;

                // 禁用按钮，防止重复提交
                sender.IsPrimaryButtonEnabled = false;
                sender.CloseButtonText = "";

                // 获取当前用户ID
                var currentUser = SessionService.CurrentUser;
                if (currentUser == null)
                {
                    PasswordInfoBar.Title = "错误";
                    PasswordInfoBar.Message = "无法获取当前用户信息";
                    PasswordInfoBar.Severity = InfoBarSeverity.Error;
                    PasswordInfoBar.IsOpen = true;

                    // 重新启用按钮
                    sender.CloseButtonText = "取消";
                    deferral.Complete();
                    return;
                }

                // 调用修改密码API
                var result = await DatabaseService.ChangePasswordAsync(
                    currentUser.Id,
                    OldPasswordBox.Password,
                    NewPasswordBox.Password);

                if (result.Success)
                {
                    // 修改成功
                    PasswordInfoBar.Title = "成功";
                    PasswordInfoBar.Message = result.Message;
                    PasswordInfoBar.Severity = InfoBarSeverity.Success;
                    PasswordInfoBar.IsOpen = true;

                    // 延迟关闭对话框
                    await Task.Delay(1500);
                    deferral.Complete();
                }
                else
                {
                    // 修改失败
                    PasswordInfoBar.Title = "错误";
                    PasswordInfoBar.Message = result.Message;
                    PasswordInfoBar.Severity = InfoBarSeverity.Error;
                    PasswordInfoBar.IsOpen = true;

                    // 重新启用按钮
                    sender.IsPrimaryButtonEnabled = true;
                    sender.CloseButtonText = "取消";
                    deferral.Complete();
                }
            }
            catch (Exception ex)
            {
                // 处理异常
                PasswordInfoBar.Title = "错误";
                PasswordInfoBar.Message = $"修改密码时发生错误: {ex.Message}";
                PasswordInfoBar.Severity = InfoBarSeverity.Error;
                PasswordInfoBar.IsOpen = true;

                // 重新启用按钮
                sender.IsPrimaryButtonEnabled = true;
                sender.CloseButtonText = "取消";
                deferral.Complete();
            }
        }

        /// <summary>
        /// 检查是否是首次运行并显示提示对话框
        /// </summary>
        private async void CheckFirstRunAndShowTipsAsync()
        {
            try
            {
                // 检查是否是首次运行
                if (SettingsService.IsFirstRun())
                {
                    // 延迟显示提示，等待UI完全加载
                    await Task.Delay(1000);

                    try
                    {
                        // 创建提示内容
                        var tipContent = new StackPanel();
                        tipContent.Spacing = 8;

                        var welcomeText = new TextBlock
                        {
                            Text = "欢迎使用推广系统",
                            FontWeight = Microsoft.UI.Text.FontWeights.SemiBold,
                            FontSize = 16,
                            Margin = new Thickness(0, 0, 0, 8)
                        };
                        tipContent.Children.Add(welcomeText);

                        var tip1 = new TextBlock
                        {
                            Text = "• 点击左侧菜单可以切换不同功能模块",
                            TextWrapping = TextWrapping.Wrap
                        };
                        tipContent.Children.Add(tip1);

                        var tip2 = new TextBlock
                        {
                            Text = "• 点击右上角用户头像可以修改密码或退出登录",
                            TextWrapping = TextWrapping.Wrap
                        };
                        tipContent.Children.Add(tip2);

                        var tip3 = new TextBlock
                        {
                            Text = "• 如需帮助，请联系系统管理员",
                            TextWrapping = TextWrapping.Wrap
                        };
                        tipContent.Children.Add(tip3);

                        // 创建对话框
                        var dialog = new ContentDialog
                        {
                            Title = "使用提示",
                            Content = tipContent,
                            PrimaryButtonText = "不再提示",
                            CloseButtonText = "我知道了",
                            DefaultButton = ContentDialogButton.Close,
                            XamlRoot = Content.XamlRoot
                        };

                        // 显示对话框
                        var result = await dialog.ShowAsync();

                        // 如果用户点击"不再提示"，设置为非首次运行
                        if (result == ContentDialogResult.Primary)
                        {
                            SettingsService.SetFirstRunCompleted();
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"显示提示对话框失败: {ex.Message}");
                        // 即使显示失败，也设置为非首次运行，避免反复尝试
                        SettingsService.SetFirstRunCompleted();
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"检查首次运行状态失败: {ex.Message}");
                // 显示提示失败不影响主流程
            }
        }

        private void NavView_SelectionChanged(NavigationView sender, NavigationViewSelectionChangedEventArgs args)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"=== 导航事件触发 ===");
                System.Diagnostics.Debug.WriteLine($"IsSettingsSelected: {args.IsSettingsSelected}");
                System.Diagnostics.Debug.WriteLine($"SelectedItem: {args.SelectedItem}");

                // 显示一个消息框来确认事件被触发
                if (args.IsSettingsSelected)
                {
                    // 导航到测试页面
                    if (ContentFrame.CurrentSourcePageType != typeof(SettingsPage))
                    {
                        ContentFrame.Navigate(typeof(SettingsPage), null, new EntranceNavigationTransitionInfo());
                    }
                    return;
                }

                if (args.SelectedItem is NavigationViewItem selectedItem)
                {
                    string pageName = selectedItem.Tag?.ToString();
                    Type pageType = null;

                    System.Diagnostics.Debug.WriteLine($"选中菜单项: {pageName}");

                    switch (pageName)
                    {
                        case "SimulatorPage":
                            pageType = typeof(SimulatorPage);
                            break;
                        case "ChatPage":
                            pageType = typeof(ChatPage);
                            break;
                        case "AccountPage":
                            pageType = typeof(AccountPage);
                            break;
                        case "UserGroupPage":
                            pageType = typeof(UserGroupPage);
                            break;
                        case "TaskPage":
                            pageType = typeof(TaskPage);
                            break;
                        case "ConfigPage":
                            pageType = typeof(ConfigPage);
                            break;
                    }

                    if (pageType != null && ContentFrame.CurrentSourcePageType != pageType)
                    {
                        System.Diagnostics.Debug.WriteLine($"导航到页面: {pageType.Name}");
                        ContentFrame.Navigate(pageType, null, new EntranceNavigationTransitionInfo());
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"导航处理异常: {ex.Message}");
            }
        }

        private DispatcherTimer? _marginUpdateTimer;
        private bool _lastPaneState = true;

        /// <summary>
        /// 根据导航菜单状态更新内容区域的边距
        /// </summary>
        private void UpdateContentMargin()
        {
            try
            {
                if (NavView == null || ContentGrid == null) return;

                // 检查导航菜单是否处于最小化状态
                bool isMinimized = !NavView.IsPaneOpen;

                // 只有状态发生变化时才更新
                if (isMinimized != _lastPaneState)
                {
                    _lastPaneState = isMinimized;

                    if (isMinimized)
                    {
                        // 菜单最小化时，将内容区域左移到紧贴左侧边缘
                        // NavigationView在Compact模式下通常会留出48px的空间给汉堡菜单按钮
                        ContentGrid.Margin = new Thickness(-172, 0, 0, 0); // 220 - 48 = 172
                    }
                    else
                    {
                        // 菜单展开时，恢复正常边距
                        ContentGrid.Margin = new Thickness(0, 0, 0, 0);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"更新内容边距失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 启动边距更新定时器
        /// </summary>
        private void StartMarginUpdateTimer()
        {
            try
            {
                _marginUpdateTimer = new DispatcherTimer();
                _marginUpdateTimer.Interval = TimeSpan.FromMilliseconds(100);
                _marginUpdateTimer.Tick += (sender, e) => UpdateContentMargin();
                _marginUpdateTimer.Start();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"启动边距更新定时器失败: {ex.Message}");
                // 定时器启动失败不影响主要功能
                _marginUpdateTimer = null;
            }
        }
    }
}

