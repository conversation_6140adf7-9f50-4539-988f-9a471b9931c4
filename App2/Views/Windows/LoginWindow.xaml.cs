using System;
using Microsoft.UI.Xaml;
using Microsoft.UI.Xaml.Controls;
using Microsoft.UI.Windowing;
using Windows.Graphics;
using App2.Services;
using System.Threading.Tasks;

namespace App2.Views.Windows
{
    public sealed partial class LoginWindow : Window
    {
        public LoginWindow()
        {
            InitializeComponent();
            this.ExtendsContentIntoTitleBar = true;
            this.SetTitleBar(null);

            // 设置窗口大小
            if (AppWindow != null)
            {
                // 设置窗口的首选大小
                var appWindow = AppWindow;
                var size = new SizeInt32(450, 500);
                appWindow.Resize(size);

                // 居中显示窗口
                var displayArea = DisplayArea.GetFromWindowId(appWindow.Id, DisplayAreaFallback.Primary);
                if (displayArea != null)
                {
                    var centerX = (displayArea.WorkArea.Width - size.Width) / 2;
                    var centerY = (displayArea.WorkArea.Height - size.Height) / 2;
                    appWindow.Move(new PointInt32(centerX, centerY));
                }
            }

            // 绑定事件
            RememberPasswordCheckBox.Checked += RememberPasswordCheckBox_Checked;
            RememberPasswordCheckBox.Unchecked += RememberPasswordCheckBox_Unchecked;

            // 绑定文本框变化事件，实时保存凭据
            UsernameTextBox.TextChanged += OnCredentialsChanged;
            PasswordBox.PasswordChanged += OnCredentialsChanged;

            // 延迟加载保存的凭据，确保窗口完全初始化
            this.Activated += LoginWindow_Activated;
        }

        private bool _credentialsLoaded = false;
        private async void LoginWindow_Activated(object sender, WindowActivatedEventArgs e)
        {
            if (!_credentialsLoaded && e.WindowActivationState != WindowActivationState.Deactivated)
            {
                _credentialsLoaded = true;
                // 给一点时间让应用完全初始化
                await Task.Delay(100);
                LoadSavedCredentials();
            }
        }

        /// <summary>
        /// 加载保存的凭据
        /// </summary>
        private void LoadSavedCredentials()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("开始加载保存的凭据...");

                bool rememberPassword = SettingsService.GetRememberPassword();
                System.Diagnostics.Debug.WriteLine($"记住密码设置: {rememberPassword}");

                if (rememberPassword)
                {
                    var savedUsername = SettingsService.GetSavedUsername();
                    var savedPassword = SettingsService.GetSavedPassword();

                    System.Diagnostics.Debug.WriteLine($"保存的用户名: {savedUsername}");
                    System.Diagnostics.Debug.WriteLine($"保存的密码: {(string.IsNullOrEmpty(savedPassword) ? "空" : "有值")}");

                    if (!string.IsNullOrEmpty(savedUsername))
                    {
                        UsernameTextBox.Text = savedUsername;
                        System.Diagnostics.Debug.WriteLine("用户名已填充");
                    }

                    if (!string.IsNullOrEmpty(savedPassword))
                    {
                        PasswordBox.Password = savedPassword;
                        System.Diagnostics.Debug.WriteLine("密码已填充");
                    }

                    RememberPasswordCheckBox.IsChecked = true;
                    System.Diagnostics.Debug.WriteLine("记住密码复选框已勾选");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("没有启用记住密码功能");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"加载凭据失败: {ex.Message}");
            }
        }

        private void RememberPasswordCheckBox_Checked(object sender, RoutedEventArgs e)
        {
            // 当用户勾选记住密码时，设置记住密码标志
            SettingsService.SetRememberPassword(true);

            // 如果当前有输入的凭据，立即保存
            if (!string.IsNullOrEmpty(UsernameTextBox.Text) && !string.IsNullOrEmpty(PasswordBox.Password))
            {
                try
                {
                    SettingsService.SaveCredentials(UsernameTextBox.Text, PasswordBox.Password);
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"勾选记住密码时保存凭据失败: {ex.Message}");
                }
            }
        }

        private void RememberPasswordCheckBox_Unchecked(object sender, RoutedEventArgs e)
        {
            // 当用户取消勾选记住密码时，清除保存的凭据
            SettingsService.ClearSavedCredentials();
        }

        private async void LoginButton_Click(object sender, RoutedEventArgs e)
        {
            string username = UsernameTextBox.Text.Trim();
            string password = PasswordBox.Password;

            // 验证输入
            if (string.IsNullOrEmpty(username))
            {
                await ShowErrorDialog("请输入账号");
                return;
            }

            if (string.IsNullOrEmpty(password))
            {
                await ShowErrorDialog("请输入密码");
                return;
            }

            // 禁用登录按钮，显示加载状态
            LoginButton.IsEnabled = false;
            LoginButton.Content = "登录中...";

            try
            {

                // 使用数据库验证登录
                var loginResult = await DatabaseService.ValidateLoginAsync(username, password, "127.0.0.1");


                if (loginResult.Success && loginResult.User != null && !string.IsNullOrEmpty(loginResult.SessionToken))
                {
                    System.Diagnostics.Debug.WriteLine("6666666666666666666");
                    // 如果勾选了记住密码，保存凭据
                    if (RememberPasswordCheckBox.IsChecked == true)
                    {
                        System.Diagnostics.Debug.WriteLine("记住密码已勾选，保存凭据");
                        try
                        {
                            SettingsService.SaveCredentials(username, password);
                            System.Diagnostics.Debug.WriteLine("凭据保存成功");
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"保存凭据引发的异常:\"{ex.GetType().Name}\"(位于 {ex.Source} 中)");
                            System.Diagnostics.Debug.WriteLine($"保存凭据失败: {ex.Message}");
                            // 不阻止登录流程，只记录错误
                        }
                    }

                    // 保存会话信息
                    await SessionService.SaveSessionAsync(loginResult.User, loginResult.SessionToken);

                    // 保存最后登录时间
                    SettingsService.SaveLastLoginTime();

                    // 登录成功，通过App类安全地切换到主窗口
                    this.DispatcherQueue.TryEnqueue(() =>
                    {
                        try
                        {
                            var app = Application.Current as App;
                            if (app != null)
                            {
                                // 通过App类管理窗口切换
                                app.SwitchToMainWindow();
                            }
                            else
                            {
                                // 备用方案：直接创建主窗口
                                var mainWindow = new MainWindow();
                                mainWindow.Activate();
                                this.Close();
                            }
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"切换到主窗口失败: {ex}");
                        }
                    });
                }
                else
                {
                    await ShowErrorDialog(loginResult.Message);
                }
            }
            catch (Exception ex)
            {
                await ShowErrorDialog($"登录失败：{ex.Message}");
            }
            finally
            {
                // 恢复登录按钮状态
                LoginButton.IsEnabled = true;
                LoginButton.Content = "登录";
            }
        }



        /// <summary>
        /// 凭据变化时的处理
        /// </summary>
        private void OnCredentialsChanged(object sender, RoutedEventArgs e)
        {
            // 如果勾选了记住密码，实时保存凭据
            if (RememberPasswordCheckBox.IsChecked == true)
            {
                string username = UsernameTextBox.Text.Trim();
                string password = PasswordBox.Password;

                if (!string.IsNullOrEmpty(username) && !string.IsNullOrEmpty(password))
                {
                    try
                    {
                        SettingsService.SaveCredentials(username, password);
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"实时保存凭据失败: {ex.Message}");
                    }
                }
            }
        }

        private async Task ShowErrorDialog(string message)
        {
            ContentDialog dialog = new ContentDialog()
            {
                Title = "提示",
                Content = message,
                CloseButtonText = "确定",
                XamlRoot = this.Content.XamlRoot
            };

            await dialog.ShowAsync();
        }
    }
}
