using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using Microsoft.UI.Xaml;
using Microsoft.UI.Xaml.Controls;
using Microsoft.UI.Xaml.Media;
using Windows.System;
using App2.Models;
using App2.Services;

namespace App2.Views.Pages
{
    public sealed partial class UserGroupPage : Page, INotifyPropertyChanged
    {
        private ObservableCollection<UserGroupMemberViewModel> _users = null!;
        
        // 分页相关属性
        private int _currentPage = 1;
        private int _pageSize = 10;
        private int _totalCount = 0;
        private int _totalPages = 0;
        private bool _isLoading = false;

        public event PropertyChangedEventHandler? PropertyChanged;

        public bool HasNoData => _users?.Count == 0 && !_isLoading;

        public UserGroupPage()
        {
            InitializeComponent();

            // 初始化UI
            InitializeUI();

            // 异步加载数据，不阻塞UI
            _ = Task.Run(async () =>
            {
                try
                {
                    await Task.Delay(100); // 给UI一点时间完成初始化
                    this.DispatcherQueue.TryEnqueue(() =>
                    {
                        _ = LoadDataAsync();
                    });
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"异步初始化失败: {ex.Message}");
                }
            });
        }

        private void OnPropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        private void InitializeUI()
        {
            try
            {
                // 初始化空列表
                _users = new ObservableCollection<UserGroupMemberViewModel>();
                UserListView.ItemsSource = _users;

                // 绑定事件
                BatchDeleteButton.Click += BatchDeleteButton_Click;
                PlatformComboBox.SelectionChanged += Filter_Changed;
                TaskSourceComboBox.SelectionChanged += Filter_Changed;
                UserNameSearchBox.QuerySubmitted += Search_QuerySubmitted;
                KeywordSearchBox.QuerySubmitted += Search_QuerySubmitted;

                // 初始化分页UI
                UpdatePaginationUI();
                OnPropertyChanged(nameof(HasNoData));

                System.Diagnostics.Debug.WriteLine("UI初始化完成");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"UI初始化失败: {ex.Message}");
            }
        }

        private async Task LoadDataAsync()
        {
            if (_isLoading) return;

            try
            {
                _isLoading = true;
                OnPropertyChanged(nameof(HasNoData));
                
                System.Diagnostics.Debug.WriteLine("开始加载用户群体数据...");

                // 先测试基本连接
                var connectionTest = await Utils.SimpleUserGroupLoader.TestBasicConnectionAsync();
                System.Diagnostics.Debug.WriteLine($"连接测试结果: {connectionTest}");

                // 确保有最少的数据
                var dataEnsure = await Utils.SimpleUserGroupLoader.EnsureMinimalDataAsync();
                System.Diagnostics.Debug.WriteLine($"数据确保结果: {dataEnsure}");

                // 测试服务
                var serviceTest = await Utils.SimpleUserGroupLoader.TestUserGroupServiceAsync();
                System.Diagnostics.Debug.WriteLine($"服务测试结果: {serviceTest}");

                // 加载数据
                await LoadUsersAsync();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"加载数据异常: {ex.Message}");
                // 显示空列表
                this.DispatcherQueue.TryEnqueue(() =>
                {
                    if (_users == null)
                    {
                        _users = new ObservableCollection<UserGroupMemberViewModel>();
                        UserListView.ItemsSource = _users;
                    }
                    UpdatePaginationUI();
                    OnPropertyChanged(nameof(HasNoData));
                });
            }
            finally
            {
                _isLoading = false;
                OnPropertyChanged(nameof(HasNoData));
            }
        }

        private async Task LoadUsersAsync()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("开始从数据库加载数据...");

                // 从数据库获取分页数据
                var pagedResult = await UserGroupService.GetUserGroupMembersPagedAsync(_currentPage, _pageSize);

                // 在UI线程上更新数据
                this.DispatcherQueue.TryEnqueue(() =>
                {
                    try
                    {
                        // 更新分页信息
                        _totalCount = pagedResult.TotalCount;
                        _totalPages = pagedResult.TotalPages;

                        System.Diagnostics.Debug.WriteLine($"获取到第{_currentPage}页数据，共 {pagedResult.Data.Count} 条记录，总计 {_totalCount} 条");

                        // 清空现有数据
                        _users.Clear();

                        // 添加新数据
                        foreach (var member in pagedResult.Data)
                        {
                            _users.Add(new UserGroupMemberViewModel(this)
                            {
                                Id = member.Id,
                                Nickname = member.Nickname,
                                UserId = member.UserId,
                                Platform = member.Platform,
                                Source = member.Source,
                                Content = member.Content,
                                TaskId = member.TaskId,  // 添加TaskId
                                TaskSource = member.TaskSource,
                                TaskSourceText = member.TaskSourceText,
                                IsSelected = false
                            });
                        }

                        // 更新分页UI
                        UpdatePaginationUI();
                        OnPropertyChanged(nameof(HasNoData));

                        System.Diagnostics.Debug.WriteLine($"UI更新完成，显示 {_users.Count} 条记录");
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"UI更新失败: {ex.Message}");
                    }
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"LoadUsersAsync 异常: {ex.Message}");
                
                // 在UI线程显示错误
                this.DispatcherQueue.TryEnqueue(async () =>
                {
                    try
                    {
                        ContentDialog errorDialog = new ContentDialog
                        {
                            Title = "错误",
                            Content = $"加载用户数据失败：{ex.Message}",
                            CloseButtonText = "确定",
                            XamlRoot = this.XamlRoot
                        };
                        await errorDialog.ShowAsync();
                    }
                    catch
                    {
                        // 忽略对话框显示错误
                    }
                });
            }
        }

        /// <summary>
        /// 单个用户删除方法
        /// </summary>
        public async void DeleteSingleUser(UserGroupMemberViewModel user)
        {
            try
            {
                // 确认删除对话框
                ContentDialog confirmDialog = new ContentDialog
                {
                    Title = "确认删除",
                    Content = $"确定要删除用户 \"{user.Nickname}\" 吗？",
                    PrimaryButtonText = "删除",
                    CloseButtonText = "取消",
                    DefaultButton = ContentDialogButton.Close,
                    XamlRoot = this.XamlRoot
                };

                var result = await confirmDialog.ShowAsync();

                if (result == ContentDialogResult.Primary)
                {
                    // 执行数据库删除操作
                    var (success, message) = await UserGroupService.DeleteUserGroupMemberAsync(user.Id);

                    if (success)
                    {
                        // 从UI集合中移除
                        _users.Remove(user);
                        _totalCount--;
                        UpdatePaginationUI();
                        OnPropertyChanged(nameof(HasNoData));
                    }
                    else
                    {
                        // 显示错误信息
                        ContentDialog errorDialog = new ContentDialog
                        {
                            Title = "删除失败",
                            Content = message,
                            CloseButtonText = "确定",
                            XamlRoot = this.XamlRoot
                        };
                        await errorDialog.ShowAsync();
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"删除用户失败: {ex.Message}");
            }
        }

        private void BatchDeleteButton_Click(object sender, RoutedEventArgs e)
        {
            _ = DeleteSelectedUsersAsync();
        }

        private async Task DeleteSelectedUsersAsync()
        {
            try
            {
                // 获取选中的用户
                var selectedUsers = _users.Where(u => u.IsSelected).ToList();

                if (selectedUsers.Count == 0)
                {
                    ContentDialog dialog = new ContentDialog
                    {
                        Title = "提示",
                        Content = "请先选择要删除的用户",
                        CloseButtonText = "确定",
                        XamlRoot = this.XamlRoot
                    };
                    await dialog.ShowAsync();
                    return;
                }

                // 确认删除
                ContentDialog confirmDialog = new ContentDialog
                {
                    Title = "确认删除",
                    Content = $"确定要删除选中的 {selectedUsers.Count} 个用户吗？",
                    PrimaryButtonText = "删除",
                    CloseButtonText = "取消",
                    DefaultButton = ContentDialogButton.Close,
                    XamlRoot = this.XamlRoot
                };

                var result = await confirmDialog.ShowAsync();

                if (result == ContentDialogResult.Primary)
                {
                    // 获取要删除的ID列表
                    var idsToDelete = selectedUsers.Select(u => u.Id).ToList();

                    // 执行数据库批量删除操作
                    var (success, message) = await UserGroupService.DeleteUserGroupMembersAsync(idsToDelete);

                    if (success)
                    {
                        // 从UI集合中移除
                        foreach (var user in selectedUsers)
                        {
                            _users.Remove(user);
                        }
                        
                        _totalCount -= selectedUsers.Count;
                        UpdatePaginationUI();
                        OnPropertyChanged(nameof(HasNoData));

                        // 显示成功信息
                        ContentDialog successDialog = new ContentDialog
                        {
                            Title = "删除成功",
                            Content = message,
                            CloseButtonText = "确定",
                            XamlRoot = this.XamlRoot
                        };
                        await successDialog.ShowAsync();
                    }
                    else
                    {
                        // 显示错误信息
                        ContentDialog errorDialog = new ContentDialog
                        {
                            Title = "删除失败",
                            Content = message,
                            CloseButtonText = "确定",
                            XamlRoot = this.XamlRoot
                        };
                        await errorDialog.ShowAsync();
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"批量删除失败: {ex.Message}");
            }
        }

        private void Filter_Changed(object sender, SelectionChangedEventArgs e)
        {
            _ = ApplyFiltersAsync(sender as ComboBox);
        }

        private void Search_QuerySubmitted(AutoSuggestBox sender, AutoSuggestBoxQuerySubmittedEventArgs args)
        {
            _ = ApplyFiltersAsync(null);
        }

        private async Task ApplyFiltersAsync(ComboBox? triggerComboBox = null)
        {
            if (_isLoading) return;

            try
            {
                _isLoading = true;
                OnPropertyChanged(nameof(HasNoData));
                
                // 重置到第一页
                _currentPage = 1;
                
                // 获取筛选条件
                string selectedPlatform = "";
                string? selectedTaskSource = null;
                string nicknameSearch = "";
                string keywordSearch = "";
                
                // 获取筛选条件
                try
                {
                    // 平台筛选
                    if (triggerComboBox?.Name == "PlatformComboBox")
                    {
                        if (triggerComboBox.SelectedItem is ComboBoxItem platformItem && 
                            platformItem.Content?.ToString() != "社交平台")
                        {
                            selectedPlatform = platformItem.Content?.ToString() ?? "";
                        }
                    }
                    else
                    {
                        var platformComboBox = this.FindName("PlatformComboBox") as ComboBox;
                        if (platformComboBox?.SelectedItem is ComboBoxItem platformItem && 
                            platformItem.Content?.ToString() != "社交平台")
                        {
                            selectedPlatform = platformItem.Content?.ToString() ?? "";
                        }
                    }

                    // 任务来源筛选
                    if (triggerComboBox?.Name == "TaskSourceComboBox")
                    {
                        if (triggerComboBox.SelectedItem is ComboBoxItem sourceItem && 
                            sourceItem.Content?.ToString() != "任务来源" &&
                            !string.IsNullOrEmpty(sourceItem.Tag?.ToString()))
                        {
                            selectedTaskSource = sourceItem.Tag?.ToString();
                        }
                    }
                    else
                    {
                        var taskSourceComboBox = this.FindName("TaskSourceComboBox") as ComboBox;
                        if (taskSourceComboBox?.SelectedItem is ComboBoxItem sourceItem && 
                            sourceItem.Content?.ToString() != "任务来源" &&
                            !string.IsNullOrEmpty(sourceItem.Tag?.ToString()))
                        {
                            selectedTaskSource = sourceItem.Tag?.ToString();
                        }
                    }

                    // 昵称搜索
                    var userNameSearchBox = this.FindName("UserNameSearchBox") as AutoSuggestBox;
                    if (userNameSearchBox != null && !string.IsNullOrEmpty(userNameSearchBox.Text))
                    {
                        nicknameSearch = userNameSearchBox.Text.Trim();
                    }

                    // 关键词搜索
                    var keywordSearchBox = this.FindName("KeywordSearchBox") as AutoSuggestBox;
                    if (keywordSearchBox != null && !string.IsNullOrEmpty(keywordSearchBox.Text))
                    {
                        keywordSearch = keywordSearchBox.Text.Trim();
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"获取筛选条件失败: {ex.Message}");
                }

                System.Diagnostics.Debug.WriteLine($"筛选条件 - 平台: '{selectedPlatform}', 任务来源: {selectedTaskSource}, 昵称: '{nicknameSearch}', 关键词: '{keywordSearch}'");

                // 从数据库获取筛选后的分页数据
                var pagedResult = await UserGroupService.GetUserGroupMembersPagedAsync(
                    _currentPage,
                    _pageSize,
                    string.IsNullOrEmpty(selectedPlatform) ? null : selectedPlatform,
                    null,
                    string.IsNullOrEmpty(nicknameSearch) ? null : nicknameSearch,
                    string.IsNullOrEmpty(keywordSearch) ? null : keywordSearch,
                    selectedTaskSource
                );

                // 在UI线程更新
                this.DispatcherQueue.TryEnqueue(() =>
                {
                    try
                    {
                        // 更新分页信息
                        _totalCount = pagedResult.TotalCount;
                        _totalPages = pagedResult.TotalPages;

                        // 更新UI
                        _users.Clear();
                        foreach (var member in pagedResult.Data)
                        {
                            _users.Add(new UserGroupMemberViewModel(this)
                            {
                                Id = member.Id,
                                Nickname = member.Nickname,
                                UserId = member.UserId,
                                Platform = member.Platform,
                                Source = member.Source,
                                Content = member.Content,
                                TaskId = member.TaskId,  // 添加TaskId
                                TaskSource = member.TaskSource,
                                TaskSourceText = member.TaskSourceText,
                                IsSelected = false
                            });
                        }
                        
                        // 更新分页UI
                        UpdatePaginationUI();
                        OnPropertyChanged(nameof(HasNoData));
                        
                        System.Diagnostics.Debug.WriteLine($"筛选完成，显示 {_users.Count} 条记录");
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"UI更新失败: {ex.Message}");
                    }
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"ApplyFiltersAsync 异常: {ex.Message}");
                // 如果筛选失败，重新加载所有数据
                _ = LoadUsersAsync();
            }
            finally
            {
                _isLoading = false;
                OnPropertyChanged(nameof(HasNoData));
            }
        }

        private void SelectAllCheckBox_Checked(object sender, RoutedEventArgs e)
        {
            if (_users != null)
            {
                foreach (var user in _users)
                {
                    user.IsSelected = true;
                }
            }
        }

        private void SelectAllCheckBox_Unchecked(object sender, RoutedEventArgs e)
        {
            if (_users != null)
            {
                foreach (var user in _users)
                {
                    user.IsSelected = false;
                }
            }
        }

        #region 分页相关方法

        private void UpdatePaginationUI()
        {
            try
            {
                // 计算总页数
                _totalPages = _totalCount > 0 ? (int)Math.Ceiling((double)_totalCount / _pageSize) : 0;

                // 更新总记录数显示
                if (this.FindName("TotalCountText") is TextBlock totalCountText)
                {
                    totalCountText.Text = $"共 {_totalCount} 条";
                }

                // 更新分页按钮状态
                if (this.FindName("PreviousPageButton") is Button previousPageButton)
                {
                    previousPageButton.IsEnabled = _currentPage > 1;
                }
                
                if (this.FindName("NextPageButton") is Button nextPageButton)
                {
                    nextPageButton.IsEnabled = _currentPage < _totalPages;
                }

                // 更新页码按钮
                if (this.FindName("PageNumbersPanel") is StackPanel pageNumbersPanel)
                {
                    pageNumbersPanel.Children.Clear();
                    GeneratePageNumberButtons(pageNumbersPanel);
                }

                System.Diagnostics.Debug.WriteLine($"分页UI更新 - 当前页: {_currentPage}, 总页数: {_totalPages}, 总记录: {_totalCount}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"更新分页UI失败: {ex.Message}");
            }
        }

        private void GeneratePageNumberButtons(StackPanel pageNumbersPanel)
        {
            if (_totalPages <= 0 || pageNumbersPanel == null) return;

            int startPage = Math.Max(1, _currentPage - 2);
            int endPage = Math.Min(_totalPages, _currentPage + 2);

            if (_currentPage <= 3)
            {
                endPage = Math.Min(_totalPages, 5);
            }

            if (_currentPage >= _totalPages - 2)
            {
                startPage = Math.Max(1, _totalPages - 4);
            }

            if (startPage > 1)
            {
                AddPageButton(pageNumbersPanel, 1);
                if (startPage > 2)
                {
                    AddEllipsis(pageNumbersPanel);
                }
            }

            for (int i = startPage; i <= endPage; i++)
            {
                AddPageButton(pageNumbersPanel, i);
            }

            if (endPage < _totalPages)
            {
                if (endPage < _totalPages - 1)
                {
                    AddEllipsis(pageNumbersPanel);
                }
                AddPageButton(pageNumbersPanel, _totalPages);
            }
        }

        private void AddPageButton(StackPanel pageNumbersPanel, int pageNumber)
        {
            var button = new Button
            {
                Content = pageNumber.ToString(),
                Width = 32,
                Height = 32,
                Tag = pageNumber,
                FontSize = 13
            };

            if (pageNumber == _currentPage)
            {
                button.Background = new SolidColorBrush(Microsoft.UI.Colors.DodgerBlue);
                button.Foreground = new SolidColorBrush(Microsoft.UI.Colors.White);
                button.BorderBrush = new SolidColorBrush(Microsoft.UI.Colors.DodgerBlue);
            }
            else
            {
                button.Background = new SolidColorBrush(Microsoft.UI.Colors.Transparent);
                button.Foreground = new SolidColorBrush(Microsoft.UI.Colors.Black);
                button.BorderBrush = new SolidColorBrush(Microsoft.UI.Colors.LightGray);
            }

            button.Click += PageButton_Click;
            pageNumbersPanel.Children.Add(button);
        }

        private void AddEllipsis(StackPanel pageNumbersPanel)
        {
            var textBlock = new TextBlock
            {
                Text = "...",
                VerticalAlignment = VerticalAlignment.Center,
                Margin = new Thickness(8, 0, 8, 0)
            };
            pageNumbersPanel.Children.Add(textBlock);
        }

        private async void PageButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is int pageNumber)
            {
                await GoToPageAsync(pageNumber);
            }
        }

        private async void PreviousPage_Click(object sender, RoutedEventArgs e)
        {
            if (_currentPage > 1)
            {
                await GoToPageAsync(_currentPage - 1);
            }
        }

        private async void NextPage_Click(object sender, RoutedEventArgs e)
        {
            if (_currentPage < _totalPages)
            {
                await GoToPageAsync(_currentPage + 1);
            }
        }

        private async void PageSizeComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (sender is ComboBox comboBox && comboBox.SelectedItem is ComboBoxItem item && item.Tag != null)
            {
                if (int.TryParse(item.Tag.ToString(), out int newPageSize))
                {
                    _pageSize = newPageSize;
                    _currentPage = 1;
                    await ApplyFiltersAsync();
                }
            }
        }

        private async Task GoToPageAsync(int pageNumber)
        {
            if (pageNumber < 1 || pageNumber > _totalPages || pageNumber == _currentPage)
                return;

            if (_isLoading) return;

            try
            {
                _isLoading = true;
                OnPropertyChanged(nameof(HasNoData));

                // 设置当前页码
                _currentPage = pageNumber;
                
                System.Diagnostics.Debug.WriteLine($"跳转到第{pageNumber}页");

                // 获取当前的筛选条件（不重置页码）
                string selectedPlatform = "";
                string? selectedTaskSource = null;
                string nicknameSearch = "";
                string keywordSearch = "";
                
                try
                {
                    // 平台筛选
                    var platformComboBox = this.FindName("PlatformComboBox") as ComboBox;
                    if (platformComboBox?.SelectedItem is ComboBoxItem platformItem && 
                        platformItem.Content?.ToString() != "社交平台")
                    {
                        selectedPlatform = platformItem.Content?.ToString() ?? "";
                    }

                    // 任务来源筛选
                    var taskSourceComboBox = this.FindName("TaskSourceComboBox") as ComboBox;
                    if (taskSourceComboBox?.SelectedItem is ComboBoxItem sourceItem && 
                        sourceItem.Content?.ToString() != "任务来源" &&
                        !string.IsNullOrEmpty(sourceItem.Tag?.ToString()))
                    {
                        selectedTaskSource = sourceItem.Tag?.ToString();
                    }

                    // 昵称搜索
                    var userNameSearchBox = this.FindName("UserNameSearchBox") as AutoSuggestBox;
                    if (userNameSearchBox != null && !string.IsNullOrEmpty(userNameSearchBox.Text))
                    {
                        nicknameSearch = userNameSearchBox.Text.Trim();
                    }

                    // 关键词搜索
                    var keywordSearchBox = this.FindName("KeywordSearchBox") as AutoSuggestBox;
                    if (keywordSearchBox != null && !string.IsNullOrEmpty(keywordSearchBox.Text))
                    {
                        keywordSearch = keywordSearchBox.Text.Trim();
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"获取筛选条件失败: {ex.Message}");
                }

                // 从数据库获取指定页码的分页数据
                var pagedResult = await UserGroupService.GetUserGroupMembersPagedAsync(
                    _currentPage,
                    _pageSize,
                    string.IsNullOrEmpty(selectedPlatform) ? null : selectedPlatform,
                    null,
                    string.IsNullOrEmpty(nicknameSearch) ? null : nicknameSearch,
                    string.IsNullOrEmpty(keywordSearch) ? null : keywordSearch,
                    selectedTaskSource
                );

                // 在UI线程更新
                this.DispatcherQueue.TryEnqueue(() =>
                {
                    try
                    {
                        // 更新分页信息
                        _totalCount = pagedResult.TotalCount;
                        _totalPages = pagedResult.TotalPages;

                        System.Diagnostics.Debug.WriteLine($"获取到第{_currentPage}页数据，共 {pagedResult.Data.Count} 条记录，总计 {_totalCount} 条");

                        // 更新UI
                        _users.Clear();
                        foreach (var member in pagedResult.Data)
                        {
                            _users.Add(new UserGroupMemberViewModel(this)
                            {
                                Id = member.Id,
                                Nickname = member.Nickname,
                                UserId = member.UserId,
                                Platform = member.Platform,
                                Source = member.Source,
                                Content = member.Content,
                                TaskId = member.TaskId,  // 添加TaskId
                                TaskSource = member.TaskSource,
                                TaskSourceText = member.TaskSourceText,
                                IsSelected = false
                            });
                        }
                        
                        // 更新分页UI
                        UpdatePaginationUI();
                        OnPropertyChanged(nameof(HasNoData));
                        
                        System.Diagnostics.Debug.WriteLine($"页面跳转完成，当前显示第{_currentPage}页，共 {_users.Count} 条记录");
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"UI更新失败: {ex.Message}");
                    }
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"GoToPageAsync 异常: {ex.Message}");
                // 如果跳转失败，重新加载当前页数据
                _ = LoadUsersAsync();
            }
            finally
            {
                _isLoading = false;
                OnPropertyChanged(nameof(HasNoData));
            }
        }

        #endregion
    }

    // 用户群体成员视图模型
    public partial class UserGroupMemberViewModel : ICommand, INotifyPropertyChanged
    {
        private readonly UserGroupPage _parentPage;
        private bool _isSelected = false;

        public UserGroupMemberViewModel(UserGroupPage parentPage)
        {
            _parentPage = parentPage;
        }

        public int Id { get; set; }
        public string Nickname { get; set; } = string.Empty;
        public string UserId { get; set; } = string.Empty;
        public string Platform { get; set; } = string.Empty;
        public string Source { get; set; } = string.Empty;
        public string Content { get; set; } = string.Empty;
        public int TaskId { get; set; } = 0;  // 添加TaskId属性
        public string TaskSource { get; set; } = string.Empty;
        public string TaskSourceText { get; set; } = string.Empty;
        
        public bool IsSelected 
        { 
            get => _isSelected;
            set 
            {
                if (_isSelected != value)
                {
                    _isSelected = value;
                    PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(nameof(IsSelected)));
                }
            }
        }

        public ICommand DeleteCommand => this;
        public event EventHandler? CanExecuteChanged;
        public event PropertyChangedEventHandler? PropertyChanged;

        public bool CanExecute(object? parameter) => true;

        public void Execute(object? parameter)
        {
            _parentPage.DeleteSingleUser(this);
        }
    }
}
