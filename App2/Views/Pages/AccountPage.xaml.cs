using Microsoft.UI.Xaml;
using Microsoft.UI.Xaml.Controls;
using Microsoft.UI.Xaml.Data;
using Microsoft.UI.Xaml.Media;
using App2.Services;
using App2.Models;
using System;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Collections.ObjectModel;
using Windows.Storage;
using Windows.Storage.Pickers;
using Microsoft.UI.Windowing;
using System.Collections;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Threading;

namespace App2.Views.Pages
{
    // 对话框管理器 - 确保一次只显示一个对话框
    public class DialogManager
    {
        private readonly Page _page;
        private readonly SemaphoreSlim _semaphore;
        private volatile bool _isDialogOpen = false;

        public DialogManager(Page page)
        {
            _page = page ?? throw new ArgumentNullException(nameof(page));
            _semaphore = new SemaphoreSlim(1, 1);
        }

        // 显示错误对话框
        public async Task ShowErrorDialogAsync(string title, string message)
        {
            if (_isDialogOpen)
            {
                System.Diagnostics.Debug.WriteLine($"对话框已打开，跳过显示错误对话框: {title}");
                return;
            }

            await _semaphore.WaitAsync();
            try
            {
                if (_isDialogOpen)
                {
                    System.Diagnostics.Debug.WriteLine($"对话框已打开，跳过显示错误对话框: {title}");
                    return;
                }

                _isDialogOpen = true;

                var dialog = new ContentDialog()
                {
                    Title = title,
                    Content = message,
                    CloseButtonText = "确定",
                    XamlRoot = _page.XamlRoot
                };

                await dialog.ShowAsync();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"显示错误对话框失败: {ex.Message}");
            }
            finally
            {
                _isDialogOpen = false;
                _semaphore.Release();
            }
        }

        // 显示信息对话框
        public async Task ShowInfoDialogAsync(string title, string message)
        {
            if (_isDialogOpen)
            {
                System.Diagnostics.Debug.WriteLine($"对话框已打开，跳过显示信息对话框: {title}");
                return;
            }

            await _semaphore.WaitAsync();
            try
            {
                if (_isDialogOpen)
                {
                    System.Diagnostics.Debug.WriteLine($"对话框已打开，跳过显示信息对话框: {title}");
                    return;
                }

                _isDialogOpen = true;

                var dialog = new ContentDialog()
                {
                    Title = title,
                    Content = message,
                    CloseButtonText = "确定",
                    XamlRoot = _page.XamlRoot
                };

                await dialog.ShowAsync();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"显示信息对话框失败: {ex.Message}");
            }
            finally
            {
                _isDialogOpen = false;
                _semaphore.Release();
            }
        }

        // 显示确认对话框
        public async Task<bool> ShowConfirmDialogAsync(string title, string message)
        {
            if (_isDialogOpen)
            {
                System.Diagnostics.Debug.WriteLine($"对话框已打开，跳过显示确认对话框: {title}");
                return false;
            }

            await _semaphore.WaitAsync();
            try
            {
                if (_isDialogOpen)
                {
                    System.Diagnostics.Debug.WriteLine($"对话框已打开，跳过显示确认对话框: {title}");
                    return false;
                }

                _isDialogOpen = true;

                var dialog = new ContentDialog()
                {
                    Title = title,
                    Content = message,
                    PrimaryButtonText = "确定",
                    CloseButtonText = "取消",
                    XamlRoot = _page.XamlRoot
                };

                var result = await dialog.ShowAsync();
                return result == ContentDialogResult.Primary;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"显示确认对话框失败: {ex.Message}");
                return false;
            }
            finally
            {
                _isDialogOpen = false;
                _semaphore.Release();
            }
        }

        // 显示自定义对话框
        public async Task<ContentDialogResult> ShowDialogAsync(ContentDialog dialog)
        {
            if (_isDialogOpen)
            {
                System.Diagnostics.Debug.WriteLine($"对话框已打开，跳过显示自定义对话框: {dialog.Title}");
                return ContentDialogResult.None;
            }

            await _semaphore.WaitAsync();
            try
            {
                if (_isDialogOpen)
                {
                    System.Diagnostics.Debug.WriteLine($"对话框已打开，跳过显示自定义对话框: {dialog.Title}");
                    return ContentDialogResult.None;
                }

                _isDialogOpen = true;
                return await dialog.ShowAsync();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"显示自定义对话框失败: {ex.Message}");
                return ContentDialogResult.None;
            }
            finally
            {
                _isDialogOpen = false;
                _semaphore.Release();
            }
        }
    }

    public sealed partial class AccountPage : Page, INotifyPropertyChanged
    {
        // 会话级别的气泡提示状态管理
        private static bool _hasShownImportTip = false;

        // 账号页面设置文件路径
        private static readonly string AccountSettingsDirectory = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData), "App2");
        private static readonly string AccountSettingsFilePath = Path.Combine(AccountSettingsDirectory, "account_settings.dat");

        // 当前用户和数据
        private User? _currentUser;
        private AccountSource _currentPlatform = AccountSource.Instagram;
        private AccountStatus? _currentStatusFilter = null;
        private ObservableCollection<UserAccount> _accounts = new ObservableCollection<UserAccount>();

        // 添加防止重复加载的标志
        private bool _isLoading = false;

        // 分页相关属性
        private List<UserAccount> _allAccounts = new List<UserAccount>();
        private int _currentPage = 1;
        private int _pageSize = 10;
        private int _totalPages = 1;
        private int _totalItems = 0;

        // 对话框管理器
        private readonly DialogManager _dialogManager;

        // 防止重复绑定事件
        private bool _eventsbound = false;

        // 用于绑定的属性
        public bool HasAccounts => _accounts.Count > 0;
        public bool IsEmptyList => _accounts.Count == 0;

        // 分页绑定属性
        public int CurrentPage
        {
            get => _currentPage;
            set
            {
                _currentPage = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(PageInfo));
                OnPropertyChanged(nameof(CanGoToPreviousPage));
                OnPropertyChanged(nameof(CanGoToNextPage));
            }
        }

        public int TotalPages
        {
            get => _totalPages;
            set
            {
                _totalPages = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(PageInfo));
                OnPropertyChanged(nameof(CanGoToPreviousPage));
                OnPropertyChanged(nameof(CanGoToNextPage));
            }
        }

        public int TotalItems
        {
            get => _totalItems;
            set
            {
                _totalItems = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(PageInfo));
            }
        }

        public string PageInfo => $"第 {CurrentPage} 页，共 {TotalPages} 页，总计 {TotalItems} 条";

        public bool CanGoToPreviousPage => CurrentPage > 1;
        public bool CanGoToNextPage => CurrentPage < TotalPages;

        // PropertyChanged 事件
        public event PropertyChangedEventHandler? PropertyChanged;

        private void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        // 绑定所有事件
        private void BindEvents()
        {
            if (_eventsbound) return;

            // 平台标签页切换事件
            PlatformTabView.SelectionChanged += PlatformTabView_SelectionChanged;

            // 状态筛选事件 - 延迟绑定，确保控件已初始化
            this.Loaded += (s, e) =>
            {
                if (StatusFilterComboBox != null)
                {
                    StatusFilterComboBox.SelectionChanged += StatusFilter_SelectionChanged;
                }
                if (SelectAllCheckBox != null)
                {
                    SelectAllCheckBox.Click += SelectAllCheckBox_Click;
                }
                if (ImportHelpButton != null)
                {
                    ImportHelpButton.Click += ImportHelpButton_Click;
                }
                if (DownloadTemplateButton != null)
                {
                    DownloadTemplateButton.Click += DownloadTemplateButton_Click;
                }
            };

            // 按钮点击事件
            ImportButton.Click += ImportButton_Click;
            BatchDeleteButton.Click += BatchDeleteButton_Click;

            _eventsbound = true;
        }

        public AccountPage()
        {
            InitializeComponent();

            // 初始化对话框管理器
            _dialogManager = new DialogManager(this);

            // 绑定数据源
            AccountsListView.ItemsSource = _accounts;

            // 监听集合变化以更新UI状态
            _accounts.CollectionChanged += (s, e) =>
            {
                OnPropertyChanged(nameof(HasAccounts));
                OnPropertyChanged(nameof(IsEmptyList));
            };

            // 获取当前用户
            _currentUser = SessionService.CurrentUser;
            
            // 添加调试信息
            if (_currentUser != null)
            {
                System.Diagnostics.Debug.WriteLine($"AccountPage 初始化 - 当前用户: {_currentUser.Username} (ID: {_currentUser.Id})");
            }
            else
            {
                System.Diagnostics.Debug.WriteLine("AccountPage 初始化 - 当前用户为空！");
            }

            // 绑定事件
            BindEvents();

            // 绑定气泡关闭事件
            if (ImportTip != null)
            {
                ImportTip.Closed += ImportTip_Closed;
            }

            // 延迟初始化数据，避免在事件绑定完成前触发
            this.Loaded += async (s, e) =>
            {
                if (!_isLoading && _currentUser != null)
                {
                    System.Diagnostics.Debug.WriteLine("页面加载完成，开始初始化数据");
                    await LoadAccountsAsync();
                }
                
                // 只有当用户在此会话中没有关闭过气泡时才显示
                if (!_hasShownImportTip)
                {
                    await Task.Delay(1000);
                    ShowImportTip();
                }
            };
        }

        // 气泡关闭事件处理
        private void ImportTip_Closed(TeachingTip sender, TeachingTipClosedEventArgs args)
        {
            // 用户关闭了气泡，标记为已显示，此会话中不再显示
            _hasShownImportTip = true;
            System.Diagnostics.Debug.WriteLine("导入提示气泡已关闭，设置会话标志");
        }

        // 显示导入提示（只在会话中首次显示）
        private void ShowImportTip()
        {
            try
            {
                if (ImportTip != null && !_hasShownImportTip)
                {
                    this.DispatcherQueue.TryEnqueue(() =>
                    {
                        try
                        {
                            System.Diagnostics.Debug.WriteLine("显示导入提示气泡");
                            ImportTip.IsOpen = true;
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"显示导入提示失败: {ex.Message}");
                        }
                    });
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("气泡已在此会话中显示过，跳过显示");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"ShowImportTip 异常: {ex.Message}");
            }
        }

        // 重置气泡提示状态（用于新登录会话）
        public static void ResetImportTipState()
        {
            _hasShownImportTip = false;
            System.Diagnostics.Debug.WriteLine("重置导入提示气泡状态");
        }

        // 加载账号数据
        private async Task LoadAccountsAsync()
        {
            // 防止重复加载
            if (_isLoading)
            {
                System.Diagnostics.Debug.WriteLine("正在加载中，跳过重复请求");
                return;
            }

            try
            {
                _isLoading = true;

                if (_currentUser == null)
                {
                    System.Diagnostics.Debug.WriteLine("当前用户为空，无法加载账号");
                    return;
                }

                System.Diagnostics.Debug.WriteLine($"=== 开始加载账号数据 ===");

                // 清空现有数据
                _accounts.Clear();
                
                // 立即更新UI状态
                OnPropertyChanged(nameof(HasAccounts));
                OnPropertyChanged(nameof(IsEmptyList));

                // 获取所有数据
                var allAccounts = await AccountService.GetUserAccountsAsync(
                    _currentUser.Id,
                    _currentPlatform,
                    _currentStatusFilter);

                System.Diagnostics.Debug.WriteLine($"从数据库获取到 {allAccounts.Count} 个账号");

                // 更新缓存
                _allAccounts = allAccounts;
                _totalItems = allAccounts.Count;
                _totalPages = (int)Math.Ceiling((double)_totalItems / _pageSize);

                // 确保当前页不超出范围
                if (_currentPage > _totalPages && _totalPages > 0)
                {
                    _currentPage = _totalPages;
                }
                else if (_currentPage < 1)
                {
                    _currentPage = 1;
                }

                // 获取当前页的数据
                var pageAccounts = _allAccounts
                    .Skip((_currentPage - 1) * _pageSize)
                    .Take(_pageSize)
                    .ToList();

                // 添加到UI集合
                foreach (var account in pageAccounts)
                {
                    _accounts.Add(account);
                }

                // 更新分页属性
                CurrentPage = _currentPage;
                TotalPages = _totalPages;
                TotalItems = _totalItems;

                // 更新UI状态
                OnPropertyChanged(nameof(HasAccounts));
                OnPropertyChanged(nameof(IsEmptyList));

                // 更新复选框相关状态
                UpdateSelectAllCheckBoxState();
                UpdateBatchDeleteButtonState();

                System.Diagnostics.Debug.WriteLine($"=== 加载完成 ===");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"加载账号失败: {ex.Message}");
                await _dialogManager.ShowErrorDialogAsync("加载账号失败", ex.Message);
            }
            finally
            {
                _isLoading = false;
            }
        }

        #region 事件处理器

        // 平台标签页切换事件
        private async void PlatformTabView_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            try
            {
                // 防止在初始化时触发
                if (_isLoading)
                {
                    return;
                }

                if (sender is TabView tabView)
                {
                    var newPlatform = tabView.SelectedIndex switch
                    {
                        0 => AccountSource.Instagram,
                        1 => AccountSource.Tiktok,
                        2 => AccountSource.X,
                        _ => AccountSource.Instagram
                    };

                    // 只有当平台真正改变时才重新加载
                    if (newPlatform != _currentPlatform)
                    {
                        _currentPlatform = newPlatform;
                        _currentPage = 1; // 重置到第一页
                        await LoadAccountsAsync();
                    }
                }
            }
            catch (Exception ex)
            {
                await _dialogManager.ShowErrorDialogAsync("切换平台失败", ex.Message);
            }
        }

        // 状态筛选变化事件
        private async void StatusFilter_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            try
            {
                // 防止在初始化时触发
                if (_isLoading)
                {
                    return;
                }

                if (sender is ComboBox comboBox)
                {
                    var newStatusFilter = comboBox.SelectedIndex switch
                    {
                        0 => (AccountStatus?)null, // 所有状态
                        1 => (AccountStatus?)AccountStatus.Yes, // 可用
                        2 => (AccountStatus?)AccountStatus.No, // 不可用
                        _ => (AccountStatus?)null
                    };

                    // 只有当状态筛选真正改变时才重新加载
                    if (newStatusFilter != _currentStatusFilter)
                    {
                        _currentStatusFilter = newStatusFilter;
                        _currentPage = 1; // 重置到第一页
                        await LoadAccountsAsync();
                    }
                }
            }
            catch (Exception ex)
            {
                await _dialogManager.ShowErrorDialogAsync("筛选失败", ex.Message);
            }
        }

        // 防止重复点击的标志
        private volatile bool _isShowingHelpDialog = false;

        // 导入帮助按钮点击事件
        private async void ImportHelpButton_Click(object sender, RoutedEventArgs e)
        {
            // 防止重复点击
            if (_isShowingHelpDialog)
            {
                System.Diagnostics.Debug.WriteLine("帮助对话框已在显示中，跳过重复点击");
                return;
            }

            try
            {
                _isShowingHelpDialog = true;

                System.Diagnostics.Debug.WriteLine("开始显示导入帮助对话框");

                var dialog = new ContentDialog()
                {
                    Title = "导入账号帮助",
                    PrimaryButtonText = "我知道了",
                    XamlRoot = this.XamlRoot
                };

                // 创建简化的内容
                var content = "支持的文件格式:\n• .txt 文本文件\n• .csv 文件\n\n📝 txt格式说明\n• 昵称,登录账号,密码 \n\n📝 csv格式说明 \n• 昵称 登录账号 密码";
                dialog.Content = content;

                var result = await _dialogManager.ShowDialogAsync(dialog);
                System.Diagnostics.Debug.WriteLine($"帮助对话框结果: {result}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"显示帮助对话框异常: {ex.Message}");
                await _dialogManager.ShowErrorDialogAsync("显示帮助失败", ex.Message);
            }
            finally
            {
                _isShowingHelpDialog = false;
                System.Diagnostics.Debug.WriteLine("帮助对话框显示完成，重置标志");
            }
        }

        // 导入账号按钮点击事件
        private async void ImportButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // 关闭提示气泡
                ImportTip.IsOpen = false;

                // 创建文件选择器
                var picker = new FileOpenPicker
                {
                    ViewMode = PickerViewMode.Thumbnail,
                    SuggestedStartLocation = PickerLocationId.DocumentsLibrary
                };
                picker.FileTypeFilter.Add(".txt");
                picker.FileTypeFilter.Add(".csv");

                // 使用窗口句柄
                var app = Application.Current as App;
                if (app?.MainWindow != null)
                {
                    var hwnd = WinRT.Interop.WindowNative.GetWindowHandle(app.MainWindow);
                    WinRT.Interop.InitializeWithWindow.Initialize(picker, hwnd);
                }

                // 打开文件选择器
                var file = await picker.PickSingleFileAsync();
                if (file != null)
                {
                    await ImportAccountsFromFileAsync(file);
                }
            }
            catch (Exception ex)
            {
                await _dialogManager.ShowErrorDialogAsync("导入失败", ex.Message);
            }
        }

        // 从文件导入账号
        private async Task ImportAccountsFromFileAsync(StorageFile file)
        {
            try
            {
                if (_currentUser == null)
                {
                    await _dialogManager.ShowErrorDialogAsync("用户错误", "当前用户为空，请重新登录");
                    return;
                }

                string content = string.Empty;
                
                try
                {
                    // 尝试多种编码方式读取文件
                    var stream = await file.OpenReadAsync();
                    using var inputStream = stream.AsStreamForRead();
                    
                    // 检测文件编码并读取内容
                    content = await ReadFileWithEncodingDetectionAsync(inputStream);
                    
                    if (string.IsNullOrWhiteSpace(content))
                    {
                        await _dialogManager.ShowErrorDialogAsync("文件为空", "选择的文件没有内容或无法读取");
                        return;
                    }
                }
                catch (Exception readEx)
                {
                    System.Diagnostics.Debug.WriteLine($"文件读取异常详情: {readEx}");
                    await _dialogManager.ShowErrorDialogAsync("文件读取失败", 
                        $"无法读取文件内容。可能的原因：\n" +
                        $"1. 文件编码不支持（请使用UTF-8或GB2312编码）\n" +
                        $"2. 文件已损坏或被占用\n" +
                        $"3. 文件格式不正确\n\n" +
                        $"错误详情：{readEx.Message}");
                    return;
                }

                // 分割行时支持不同的换行符
                var lines = content.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);
                var accountsToImport = new List<UserAccount>();

                foreach (var line in lines)
                {
                    var trimmedLine = line.Trim();
                    if (string.IsNullOrEmpty(trimmedLine)) continue;

                    // 支持多种分隔符：逗号、制表符、空格
                    var parts = trimmedLine.Split(new[] { ',', '\t', ' ' }, StringSplitOptions.RemoveEmptyEntries);

                    string accountName = "";
                    string loginAccount = "";
                    string password = "";

                    if (parts.Length >= 3)
                    {
                        accountName = parts[0].Trim();
                        loginAccount = parts[1].Trim();
                        password = parts[2].Trim();
                    }
                    else if (parts.Length == 2)
                    {
                        string field1 = parts[0].Trim();
                        string field2 = parts[1].Trim();
                        
                        // 智能判断：如果第二个字段看起来像邮箱或用户名，则认为是账号名和登录账号
                        if (field2.Contains("@") || field2.Contains("_") || field2.Contains("."))
                        {
                            accountName = field1;
                            loginAccount = field2;
                        }
                        else
                        {
                            loginAccount = field1;
                            accountName = loginAccount;
                            password = field2;
                        }
                    }
                    else if (parts.Length == 1)
                    {
                        loginAccount = parts[0].Trim();
                        accountName = loginAccount;
                    }

                    if (!string.IsNullOrEmpty(loginAccount))
                    {
                        accountsToImport.Add(new UserAccount
                        {
                            UserId = _currentUser.Id,
                            SimulatorId = 0,
                            AccountName = accountName,
                            LoginAccount = loginAccount,
                            Password = password,
                            AccountStatus = AccountStatus.Yes,
                            Source = _currentPlatform,
                            CreatedAt = DateTime.Now,
                            UpdatedAt = DateTime.Now
                        });
                    }
                }

                if (accountsToImport.Count > 0)
                {
                    var result = await AccountService.BatchImportAccountsAsync(accountsToImport);

                    if (result.Success)
                    {
                        var message = $"成功导入 {result.SuccessCount} 个账号";
                        if (result.FailureCount > 0)
                        {
                            message += $"，{result.FailureCount} 个账号导入失败";
                            if (result.ErrorMessages.Count > 0)
                            {
                                message += $"\n\n失败原因：\n{string.Join("\n", result.ErrorMessages.Take(3))}";
                                if (result.ErrorMessages.Count > 3)
                                {
                                    message += $"\n...等共{result.ErrorMessages.Count}个错误";
                                }
                            }
                        }
                        
                        await _dialogManager.ShowInfoDialogAsync("导入成功", message);
                        await LoadAccountsAsync();
                    }
                    else
                    {
                        await _dialogManager.ShowErrorDialogAsync("导入失败", result.Message);
                    }
                }
                else
                {
                    await _dialogManager.ShowInfoDialogAsync("导入结果", 
                        "没有找到有效的账号数据，请检查文件格式\n\n" +
                        "支持的格式：\n" +
                        "• 账号名称,登录账号,密码\n" +
                        "分隔符支持：逗号(,)");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"导入账号总异常: {ex}");
                await _dialogManager.ShowErrorDialogAsync("导入失败", $"导入过程中发生错误: {ex.Message}");
            }
        }

        // 使用编码检测读取文件内容
        private async Task<string> ReadFileWithEncodingDetectionAsync(Stream stream)
        {
            try
            {
                // 重置流位置
                stream.Position = 0;
                
                // 读取文件的前几个字节来检测 BOM
                var buffer = new byte[4];
                var bytesRead = await stream.ReadAsync(buffer, 0, 4);
                stream.Position = 0; // 重置位置

                System.Text.Encoding encoding = null;

                // 检测 BOM
                if (bytesRead >= 3 && buffer[0] == 0xEF && buffer[1] == 0xBB && buffer[2] == 0xBF)
                {
                    // UTF-8 BOM
                    encoding = System.Text.Encoding.UTF8;
                    System.Diagnostics.Debug.WriteLine("检测到 UTF-8 编码");
                }
                else if (bytesRead >= 2 && buffer[0] == 0xFF && buffer[1] == 0xFE)
                {
                    // UTF-16 LE BOM
                    encoding = System.Text.Encoding.Unicode;
                    System.Diagnostics.Debug.WriteLine("检测到 UTF-16 LE 编码");
                }
                else if (bytesRead >= 2 && buffer[0] == 0xFE && buffer[1] == 0xFF)
                {
                    // UTF-16 BE BOM
                    encoding = System.Text.Encoding.BigEndianUnicode;
                    System.Diagnostics.Debug.WriteLine("检测到 UTF-16 BE 编码");
                }
                else
                {
                    // 没有 BOM，尝试多种编码
                    var encodingsToTry = new[]
                    {
                        System.Text.Encoding.UTF8,
                        System.Text.Encoding.GetEncoding("GB2312"),
                        System.Text.Encoding.GetEncoding("GBK"),
                        System.Text.Encoding.Default,
                        System.Text.Encoding.ASCII
                    };

                    foreach (var testEncoding in encodingsToTry)
                    {
                        try
                        {
                            stream.Position = 0;
                            using var reader = new StreamReader(stream, testEncoding, detectEncodingFromByteOrderMarks: false, bufferSize: 1024, leaveOpen: true);
                            var testContent = await reader.ReadToEndAsync();
                            
                            // 检查内容是否有效（不包含替换字符）
                            if (!string.IsNullOrEmpty(testContent) && !testContent.Contains('\uFFFD'))
                            {
                                encoding = testEncoding;
                                System.Diagnostics.Debug.WriteLine($"成功使用 {testEncoding.EncodingName} 编码读取文件");
                                break;
                            }
                        }
                        catch
                        {
                            // 继续尝试下一种编码
                        }
                    }
                }

                // 如果没有找到合适的编码，使用 UTF-8 作为默认
                if (encoding == null)
                {
                    encoding = System.Text.Encoding.UTF8;
                    System.Diagnostics.Debug.WriteLine("未检测到合适编码，使用 UTF-8 作为默认");
                }

                // 使用确定的编码读取文件
                stream.Position = 0;
                using var finalReader = new StreamReader(stream, encoding, detectEncodingFromByteOrderMarks: true);
                var content = await finalReader.ReadToEndAsync();
                
                System.Diagnostics.Debug.WriteLine($"文件读取完成，内容长度: {content.Length}");
                return content;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"编码检测读取失败: {ex.Message}");
                throw new InvalidOperationException($"无法读取文件内容，可能的编码问题：{ex.Message}", ex);
            }
        }

        // 项目复选框点击事件
        private void ItemCheckBox_Click(object sender, RoutedEventArgs e)
        {
            UpdateSelectAllCheckBoxState();
            UpdateBatchDeleteButtonState();
        }

        // 更新批量删除按钮状态
        private void UpdateBatchDeleteButtonState()
        {
            var selectedCount = _accounts.Count(a => a.IsSelected);
            BatchDeleteButton.IsEnabled = selectedCount > 0;
        }

        // 更新全选复选框状态
        private void UpdateSelectAllCheckBoxState()
        {
            if (SelectAllCheckBox == null || _accounts.Count == 0)
            {
                if (SelectAllCheckBox != null)
                    SelectAllCheckBox.IsChecked = false;
                return;
            }

            var selectedCount = _accounts.Count(a => a.IsSelected);
            var totalCount = _accounts.Count;

            if (selectedCount == 0)
            {
                SelectAllCheckBox.IsChecked = false;
            }
            else if (selectedCount == totalCount)
            {
                SelectAllCheckBox.IsChecked = true;
            }
            else
            {
                SelectAllCheckBox.IsChecked = null;
            }
        }

        // 全选复选框点击事件
        private void SelectAllCheckBox_Click(object sender, RoutedEventArgs e)
        {
            if (sender is CheckBox checkBox)
            {
                bool? isChecked = checkBox.IsChecked;

                foreach (var account in _accounts)
                {
                    account.IsSelected = isChecked == true;
                }

                UpdateBatchDeleteButtonState();
            }
        }

        // 编辑按钮点击事件
        private async void EditButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is Button button && button.Tag is UserAccount account)
                {
                    await ShowEditAccountDialogAsync(account);
                }
            }
            catch (Exception ex)
            {
                await _dialogManager.ShowErrorDialogAsync("编辑失败", ex.Message);
            }
        }

        // 删除按钮点击事件
        private async void DeleteButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is Button button && button.Tag is UserAccount account)
                {
                    var confirmResult = await _dialogManager.ShowConfirmDialogAsync(
                        "确认删除",
                        $"确定要删除账号 {account.AccountName} 吗？此操作不可撤销。");

                    if (confirmResult)
                    {
                        var result = await AccountService.DeleteAccountAsync(account.Id);

                        if (result.Success)
                        {
                            await _dialogManager.ShowInfoDialogAsync("删除成功", result.Message);
                            await LoadAccountsAsync();
                        }
                        else
                        {
                            await _dialogManager.ShowErrorDialogAsync("删除失败", result.Message);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                await _dialogManager.ShowErrorDialogAsync("删除失败", ex.Message);
            }
        }

        // 解绑模拟器按钮点击事件
        private async void UnbindSimulator_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is Button button && button.Tag is UserAccount account)
                {
                    var confirmResult = await _dialogManager.ShowConfirmDialogAsync(
                        "确认解绑",
                        $"确定要解绑账号 {account.AccountName} 的模拟器吗？");

                    if (confirmResult)
                    {
                        var updatedAccount = new UserAccount
                        {
                            Id = account.Id,
                            UserId = account.UserId,
                            SimulatorId = 0,
                            AccountName = account.AccountName,
                            LoginAccount = account.LoginAccount,
                            AccountStatus = account.AccountStatus,
                            Source = account.Source,
                            CreatedAt = account.CreatedAt,
                            UpdatedAt = DateTime.Now
                        };

                        var result = await AccountService.UpdateAccountAsync(updatedAccount);

                        if (result.Success)
                        {
                            await _dialogManager.ShowInfoDialogAsync("解绑成功", "模拟器已成功解绑");
                            await LoadAccountsAsync();
                        }
                        else
                        {
                            await _dialogManager.ShowErrorDialogAsync("解绑失败", result.Message);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                await _dialogManager.ShowErrorDialogAsync("解绑失败", ex.Message);
            }
        }

        #endregion

        #region 辅助方法

        // 显示编辑账号对话框
        private async Task ShowEditAccountDialogAsync(UserAccount account)
        {
            var dialog = new ContentDialog()
            {
                Title = "编辑账号",
                PrimaryButtonText = "保存",
                CloseButtonText = "取消",
                XamlRoot = this.XamlRoot
            };

            var stackPanel = new StackPanel { Spacing = 12 };

            var nameTextBox = new TextBox
            {
                Header = "账号名称",
                Text = account.AccountName ?? "",
                PlaceholderText = "请输入账号名称"
            };

            var loginTextBox = new TextBox
            {
                Header = "登录账号",
                Text = account.LoginAccount ?? "",
                PlaceholderText = "请输入登录账号"
            };

            var passwordTextBox = new TextBox
            {
                Header = "密码",
                Text = account.Password ?? string.Empty,
                PlaceholderText = "请输入密码（留空表示不修改）"
            };

            var statusComboBox = new ComboBox
            {
                Header = "账号状态",
                SelectedIndex = account.AccountStatus == AccountStatus.Yes ? 0 : 1
            };
            statusComboBox.Items.Add("可用");
            statusComboBox.Items.Add("不可用");

            stackPanel.Children.Add(nameTextBox);
            stackPanel.Children.Add(loginTextBox);
            stackPanel.Children.Add(passwordTextBox);
            stackPanel.Children.Add(statusComboBox);

            dialog.Content = stackPanel;

            var result = await _dialogManager.ShowDialogAsync(dialog);

            if (result == ContentDialogResult.Primary)
            {
                if (string.IsNullOrWhiteSpace(nameTextBox.Text) || string.IsNullOrWhiteSpace(loginTextBox.Text))
                {
                    await _dialogManager.ShowErrorDialogAsync("输入错误", "账号名称和登录账号不能为空");
                    return;
                }

                account.AccountName = nameTextBox.Text?.Trim() ?? "";
                account.LoginAccount = loginTextBox.Text?.Trim() ?? "";
                account.AccountStatus = statusComboBox.SelectedIndex == 0 ? AccountStatus.Yes : AccountStatus.No;
                
                if (!string.IsNullOrWhiteSpace(passwordTextBox.Text))
                {
                    account.Password = passwordTextBox.Text.Trim();
                }

                var updateResult = await AccountService.UpdateAccountAsync(account);

                if (updateResult.Success)
                {
                    await _dialogManager.ShowInfoDialogAsync("更新成功", updateResult.Message);
                    await LoadAccountsAsync();
                }
                else
                {
                    await _dialogManager.ShowErrorDialogAsync("更新失败", updateResult.Message);
                }
            }
        }

        // 批量删除按钮点击事件
        private async void BatchDeleteButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var selectedItems = _accounts.Where(a => a.IsSelected).ToList();

                if (selectedItems.Count == 0)
                {
                    await _dialogManager.ShowInfoDialogAsync("提示", "请先选择要删除的账号");
                    return;
                }

                var confirmResult = await _dialogManager.ShowConfirmDialogAsync(
                    "确认删除",
                    $"确定要删除选中的 {selectedItems.Count} 个账号吗？此操作不可撤销。");

                if (confirmResult)
                {
                    var accountIds = selectedItems.Select(a => a.Id).ToList();
                    var result = await AccountService.BatchDeleteAccountsAsync(accountIds);

                    if (result.Success)
                    {
                        await _dialogManager.ShowInfoDialogAsync("删除成功", result.Message);
                        await LoadAccountsAsync();
                    }
                    else
                    {
                        await _dialogManager.ShowErrorDialogAsync("删除失败", result.Message);
                    }
                }
            }
            catch (Exception ex)
            {
                await _dialogManager.ShowErrorDialogAsync("批量删除失败", ex.Message);
            }
        }

        private async void PaginationButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is string action)
            {
                switch (action)
                {
                    case "First":
                        await GoToPageAsync(1);
                        break;
                    case "Previous":
                        await GoToPageAsync(_currentPage - 1);
                        break;
                    case "Next":
                        await GoToPageAsync(_currentPage + 1);
                        break;
                    case "Last":
                        await GoToPageAsync(_totalPages);
                        break;
                }
            }
        }

        // 转到指定页
        private async Task GoToPageAsync(int page)
        {
            if (page < 1 || page > _totalPages || page == _currentPage)
                return;

            _currentPage = page;
            await LoadCurrentPageData();
        }

        // 加载当前页数据
        private async Task LoadCurrentPageData()
        {
            try
            {
                _isLoading = true;

                _accounts.Clear();

                var pageAccounts = _allAccounts
                    .Skip((_currentPage - 1) * _pageSize)
                    .Take(_pageSize)
                    .ToList();

                foreach (var account in pageAccounts)
                {
                    _accounts.Add(account);
                }

                CurrentPage = _currentPage;

                OnPropertyChanged(nameof(HasAccounts));
                OnPropertyChanged(nameof(IsEmptyList));

                UpdateSelectAllCheckBoxState();
                UpdateBatchDeleteButtonState();
            }
            catch (Exception ex)
            {
                await _dialogManager.ShowErrorDialogAsync("加载页面数据失败", ex.Message);
            }
            finally
            {
                _isLoading = false;
            }
        }

        // 下载模板按钮点击事件
        private async void DownloadTemplateButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // 创建选择模板类型的对话框
                var dialog = new ContentDialog()
                {
                    Title = "选择模板类型",
                    PrimaryButtonText = "下载 TXT 模板",
                    SecondaryButtonText = "下载 CSV 模板",
                    CloseButtonText = "取消",
                    XamlRoot = this.XamlRoot
                };

                // 创建对话框内容
                var stackPanel = new StackPanel { Spacing = 16 };
                
                var titleBlock = new TextBlock
                {
                    Text = "请选择要下载的模板文件类型：",
                    FontSize = 16,
                    FontWeight = Microsoft.UI.Text.FontWeights.SemiBold,
                    Margin = new Thickness(0, 0, 0, 8)
                };

                var txtInfo = new TextBlock
                {
                    Text = "📝 TXT 模板：账号名称,登录账号,密码",
                    FontSize = 14,
                    Foreground = new Microsoft.UI.Xaml.Media.SolidColorBrush(Microsoft.UI.Colors.DarkGreen)
                };

                var csvInfo = new TextBlock
                {
                    Text = "📊 CSV 模板：账号名称 登录账号 密码",
                    FontSize = 14,
                    Foreground = new Microsoft.UI.Xaml.Media.SolidColorBrush(Microsoft.UI.Colors.DarkBlue)
                };

                stackPanel.Children.Add(titleBlock);
                stackPanel.Children.Add(txtInfo);
                stackPanel.Children.Add(csvInfo);
                dialog.Content = stackPanel;

                var result = await _dialogManager.ShowDialogAsync(dialog);

                if (result == ContentDialogResult.Primary)
                {
                    // 下载 TXT 模板
                    await DownloadTemplateFileAsync("txt");
                }
                else if (result == ContentDialogResult.Secondary)
                {
                    // 下载 CSV 模板
                    await DownloadTemplateFileAsync("csv");
                }
            }
            catch (Exception ex)
            {
                await _dialogManager.ShowErrorDialogAsync("下载模板失败", ex.Message);
            }
        }

        // 下载模板文件
        // 参数 fileType 文件类型：txt 或 csv
        private async Task DownloadTemplateFileAsync(string fileType)
        {
            try
            {
                // 创建文件保存选择器
                var savePicker = new FileSavePicker();
                
                // 获取当前窗口句柄
                var app = Application.Current as App;
                if (app?.MainWindow != null)
                {
                    var hwnd = WinRT.Interop.WindowNative.GetWindowHandle(app.MainWindow);
                    WinRT.Interop.InitializeWithWindow.Initialize(savePicker, hwnd);
                }

                // 设置文件类型和默认文件名
                if (fileType.ToLower() == "txt")
                {
                    savePicker.SuggestedStartLocation = PickerLocationId.DocumentsLibrary;
                    savePicker.FileTypeChoices.Add("文本文件", new List<string>() { ".txt" });
                    savePicker.SuggestedFileName = "账号导入模板.txt";
                }
                else
                {
                    savePicker.SuggestedStartLocation = PickerLocationId.DocumentsLibrary;
                    savePicker.FileTypeChoices.Add("CSV文件", new List<string>() { ".csv" });
                    savePicker.SuggestedFileName = "账号导入模板.csv";
                }

                // 显示保存对话框
                var file = await savePicker.PickSaveFileAsync();
                if (file != null)
                {
                    // 创建模板内容
                    string templateContent = GenerateTemplateContent(fileType);

                    // 写入文件
                    await FileIO.WriteTextAsync(file, templateContent);

                    // 显示成功消息
                    await _dialogManager.ShowInfoDialogAsync(
                        "下载成功", 
                        $"模板文件已保存到：\n{file.Path}\n\n您可以参考模板格式填写账号信息，然后使用导入功能。");
                }
            }
            catch (Exception ex)
            {
                await _dialogManager.ShowErrorDialogAsync("保存模板文件失败", $"无法保存模板文件：{ex.Message}");
            }
        }

        // 生成模板文件内容
        // 参数 fileType 文件类型：txt 或 csv
        // 返回模板内容
        private string GenerateTemplateContent(string fileType)
        {
            if (fileType.ToLower() == "txt")
            {
                return @"小红的账号,<EMAIL>,123456
小明的账号,<EMAIL>,abcdef";
            }
            else
            {
                return @"小红的账号 <EMAIL> 123456
小明的账号 <EMAIL> abcdef";
            }
        }

        #endregion
    }
}