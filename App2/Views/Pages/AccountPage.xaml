<?xml version="1.0" encoding="utf-8"?>
<Page
    x:Class="App2.Views.Pages.AccountPage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:local="using:App2.Views.Pages"
    xmlns:models="using:App2.Models"
    xmlns:converters="using:App2.Converters"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    mc:Ignorable="d"
    Background="{ThemeResource ApplicationPageBackgroundThemeBrush}">

    <Page.Resources>
        <!-- 账号状态转换器 -->
        <converters:AccountStatusConverter x:Key="AccountStatusConverter"/>
        <converters:AccountStatusColorConverter x:Key="AccountStatusColorConverter"/>
        <converters:EmptyListToVisibilityConverter x:Key="EmptyListToVisibilityConverter"/>
        <converters:DateTimeToStringConverter x:Key="DateTimeToStringConverter"/>
        <converters:BoolToVisibilityConverter x:Key="BoolToVisibilityConverter"/>
        <converters:SimulatorIdConverter x:Key="SimulatorIdConverter"/>
        <converters:SimulatorIdVisibilityConverter x:Key="SimulatorIdVisibilityConverter"/>
        
        <!-- 增强的复选框样式 -->
        <Style x:Key="EnhancedCheckBoxStyle" TargetType="CheckBox">
            <Setter Property="MinWidth" Value="20"/>
            <Setter Property="MinHeight" Value="20"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="CheckBox">
                        <Grid Background="Transparent">
                            <VisualStateManager.VisualStateGroups>
                                <VisualStateGroup x:Name="CheckStates">
                                    <VisualState x:Name="Checked">
                                        <Storyboard>
                                            <ObjectAnimationUsingKeyFrames Storyboard.TargetName="CheckGlyph" Storyboard.TargetProperty="Opacity">
                                                <DiscreteObjectKeyFrame KeyTime="0" Value="1"/>
                                            </ObjectAnimationUsingKeyFrames>
                                            <ColorAnimation Storyboard.TargetName="NormalRectangle" Storyboard.TargetProperty="(Rectangle.Fill).(SolidColorBrush.Color)" 
                                                          To="#0066CC" Duration="0:0:0.1"/>
                                            <ColorAnimation Storyboard.TargetName="NormalRectangle" Storyboard.TargetProperty="(Rectangle.Stroke).(SolidColorBrush.Color)" 
                                                          To="#0066CC" Duration="0:0:0.1"/>
                                        </Storyboard>
                                    </VisualState>
                                    <VisualState x:Name="Unchecked">
                                        <Storyboard>
                                            <ObjectAnimationUsingKeyFrames Storyboard.TargetName="CheckGlyph" Storyboard.TargetProperty="Opacity">
                                                <DiscreteObjectKeyFrame KeyTime="0" Value="0"/>
                                            </ObjectAnimationUsingKeyFrames>
                                            <ColorAnimation Storyboard.TargetName="NormalRectangle" Storyboard.TargetProperty="(Rectangle.Fill).(SolidColorBrush.Color)" 
                                                          To="Transparent" Duration="0:0:0.1"/>
                                            <ColorAnimation Storyboard.TargetName="NormalRectangle" Storyboard.TargetProperty="(Rectangle.Stroke).(SolidColorBrush.Color)" 
                                                          To="#CCCCCC" Duration="0:0:0.1"/>
                                        </Storyboard>
                                    </VisualState>
                                    <VisualState x:Name="Indeterminate">
                                        <Storyboard>
                                            <ObjectAnimationUsingKeyFrames Storyboard.TargetName="IndeterminateGlyph" Storyboard.TargetProperty="Opacity">
                                                <DiscreteObjectKeyFrame KeyTime="0" Value="1"/>
                                            </ObjectAnimationUsingKeyFrames>
                                            <ObjectAnimationUsingKeyFrames Storyboard.TargetName="CheckGlyph" Storyboard.TargetProperty="Opacity">
                                                <DiscreteObjectKeyFrame KeyTime="0" Value="0"/>
                                            </ObjectAnimationUsingKeyFrames>
                                            <ColorAnimation Storyboard.TargetName="NormalRectangle" Storyboard.TargetProperty="(Rectangle.Fill).(SolidColorBrush.Color)" 
                                                          To="#0078D4" Duration="0:0:0.1"/>
                                            <ColorAnimation Storyboard.TargetName="NormalRectangle" Storyboard.TargetProperty="(Rectangle.Stroke).(SolidColorBrush.Color)" 
                                                          To="#0078D4" Duration="0:0:0.1"/>
                                        </Storyboard>
                                    </VisualState>
                                </VisualStateGroup>
                                <VisualStateGroup x:Name="CommonStates">
                                    <VisualState x:Name="Normal"/>
                                    <VisualState x:Name="PointerOver">
                                        <Storyboard>
                                            <ObjectAnimationUsingKeyFrames Storyboard.TargetName="NormalRectangle" Storyboard.TargetProperty="Opacity">
                                                <DiscreteObjectKeyFrame KeyTime="0" Value="0.8"/>
                                            </ObjectAnimationUsingKeyFrames>
                                        </Storyboard>
                                    </VisualState>
                                    <VisualState x:Name="Pressed">
                                        <Storyboard>
                                            <ObjectAnimationUsingKeyFrames Storyboard.TargetName="NormalRectangle" Storyboard.TargetProperty="Opacity">
                                                <DiscreteObjectKeyFrame KeyTime="0" Value="0.9"/>
                                            </ObjectAnimationUsingKeyFrames>
                                        </Storyboard>
                                    </VisualState>
                                </VisualStateGroup>
                            </VisualStateManager.VisualStateGroups>
                            
                            <Rectangle x:Name="NormalRectangle" 
                                     Width="20" Height="20" 
                                     Fill="Transparent" 
                                     Stroke="#CCCCCC" 
                                     StrokeThickness="2" 
                                     RadiusX="3" RadiusY="3"/>
                            
                            <FontIcon x:Name="CheckGlyph" 
                                    Glyph="&#xE73E;" 
                                    FontSize="12" 
                                    Foreground="White" 
                                    HorizontalAlignment="Center" 
                                    VerticalAlignment="Center" 
                                    Opacity="0"/>
                            
                            <Rectangle x:Name="IndeterminateGlyph" 
                                     Width="10" Height="2" 
                                     Fill="White" 
                                     HorizontalAlignment="Center" 
                                     VerticalAlignment="Center" 
                                     Opacity="0"/>
                        </Grid>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        
        <!-- 增强的ListView项目样式 -->
        <Style x:Key="EnhancedListViewItemStyle" TargetType="ListViewItem">
            <Setter Property="HorizontalContentAlignment" Value="Stretch"/>
            <Setter Property="Padding" Value="0"/>
            <Setter Property="MinHeight" Value="0"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="ListViewItem">
                        <Grid x:Name="LayoutRoot" Background="Transparent">
                            <VisualStateManager.VisualStateGroups>
                                <VisualStateGroup x:Name="CommonStates">
                                    <VisualState x:Name="Normal"/>
                                    <VisualState x:Name="PointerOver">
                                        <Storyboard>
                                            <ColorAnimation Storyboard.TargetName="LayoutRoot" 
                                                          Storyboard.TargetProperty="(Grid.Background).(SolidColorBrush.Color)" 
                                                          To="#F0F8FF" Duration="0:0:0.1"/>
                                        </Storyboard>
                                    </VisualState>
                                    <VisualState x:Name="Pressed">
                                        <Storyboard>
                                            <ColorAnimation Storyboard.TargetName="LayoutRoot" 
                                                          Storyboard.TargetProperty="(Grid.Background).(SolidColorBrush.Color)" 
                                                          To="#E6F3FF" Duration="0:0:0.1"/>
                                        </Storyboard>
                                    </VisualState>
                                </VisualStateGroup>
                                <VisualStateGroup x:Name="SelectionStates">
                                    <VisualState x:Name="Unselected"/>
                                    <VisualState x:Name="Selected">
                                        <Storyboard>
                                            <ColorAnimation Storyboard.TargetName="LayoutRoot" 
                                                          Storyboard.TargetProperty="(Grid.Background).(SolidColorBrush.Color)" 
                                                          To="#CCE7FF" Duration="0:0:0.1"/>
                                            <ColorAnimation Storyboard.TargetName="SelectionIndicator" 
                                                          Storyboard.TargetProperty="(Rectangle.Fill).(SolidColorBrush.Color)" 
                                                          To="#0066CC" Duration="0:0:0.1"/>
                                            <DoubleAnimation Storyboard.TargetName="SelectionIndicator" 
                                                           Storyboard.TargetProperty="Opacity" 
                                                           To="1" Duration="0:0:0.1"/>
                                        </Storyboard>
                                    </VisualState>
                                    <VisualState x:Name="SelectedUnfocused">
                                        <Storyboard>
                                            <ColorAnimation Storyboard.TargetName="LayoutRoot" 
                                                          Storyboard.TargetProperty="(Grid.Background).(SolidColorBrush.Color)" 
                                                          To="#E6F3FF" Duration="0:0:0.1"/>
                                            <ColorAnimation Storyboard.TargetName="SelectionIndicator" 
                                                          Storyboard.TargetProperty="(Rectangle.Fill).(SolidColorBrush.Color)" 
                                                          To="#0078D4" Duration="0:0:0.1"/>
                                            <DoubleAnimation Storyboard.TargetName="SelectionIndicator" 
                                                           Storyboard.TargetProperty="Opacity" 
                                                           To="1" Duration="0:0:0.1"/>
                                        </Storyboard>
                                    </VisualState>
                                    <VisualState x:Name="SelectedPointerOver">
                                        <Storyboard>
                                            <ColorAnimation Storyboard.TargetName="LayoutRoot" 
                                                          Storyboard.TargetProperty="(Grid.Background).(SolidColorBrush.Color)" 
                                                          To="#B3DAFF" Duration="0:0:0.1"/>
                                            <ColorAnimation Storyboard.TargetName="SelectionIndicator" 
                                                          Storyboard.TargetProperty="(Rectangle.Fill).(SolidColorBrush.Color)" 
                                                          To="#004C99" Duration="0:0:0.1"/>
                                            <DoubleAnimation Storyboard.TargetName="SelectionIndicator" 
                                                           Storyboard.TargetProperty="Opacity" 
                                                           To="1" Duration="0:0:0.1"/>
                                        </Storyboard>
                                    </VisualState>
                                </VisualStateGroup>
                            </VisualStateManager.VisualStateGroups>
                            
                            <Rectangle x:Name="SelectionIndicator" 
                                     Fill="Transparent" 
                                     Width="4" 
                                     HorizontalAlignment="Left" 
                                     Opacity="0"/>
                            
                            <ContentPresenter x:Name="ContentPresenter" 
                                            ContentTemplate="{TemplateBinding ContentTemplate}" 
                                            Content="{TemplateBinding Content}" 
                                            HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}" 
                                            VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                            Margin="{TemplateBinding Padding}"/>
                        </Grid>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        
        <!-- 简化的ListView项目样式 - 移除选中状态颜色 -->
        <Style x:Key="SimpleListViewItemStyle" TargetType="ListViewItem">
            <Setter Property="HorizontalContentAlignment" Value="Stretch"/>
            <Setter Property="Padding" Value="0"/>
            <Setter Property="MinHeight" Value="0"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="ListViewItem">
                        <Grid x:Name="LayoutRoot" Background="Transparent">
                            <VisualStateManager.VisualStateGroups>
                                <VisualStateGroup x:Name="CommonStates">
                                    <VisualState x:Name="Normal"/>
                                    <VisualState x:Name="PointerOver">
                                        <Storyboard>
                                            <ColorAnimation Storyboard.TargetName="LayoutRoot" 
                                                          Storyboard.TargetProperty="(Grid.Background).(SolidColorBrush.Color)" 
                                                          To="#F5F5F5" Duration="0:0:0.1"/>
                                        </Storyboard>
                                    </VisualState>
                                    <VisualState x:Name="Pressed">
                                        <Storyboard>
                                            <ColorAnimation Storyboard.TargetName="LayoutRoot" 
                                                          Storyboard.TargetProperty="(Grid.Background).(SolidColorBrush.Color)" 
                                                          To="#EEEEEE" Duration="0:0:0.1"/>
                                        </Storyboard>
                                    </VisualState>
                                </VisualStateGroup>
                                <!-- 移除SelectionStates组，不显示选中状态颜色 -->
                            </VisualStateManager.VisualStateGroups>
                            
                            <ContentPresenter x:Name="ContentPresenter" 
                                            ContentTemplate="{TemplateBinding ContentTemplate}" 
                                            Content="{TemplateBinding Content}" 
                                            HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}" 
                                            VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                            Margin="{TemplateBinding Padding}"/>
                        </Grid>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Page.Resources>

    <Grid Padding="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- 平台选择标签页 -->
        <TabView Grid.Row="0" 
                x:Name="PlatformTabView"
                SelectedIndex="0"
                IsAddTabButtonVisible="False"
                TabWidthMode="SizeToContent">
            <TabView.TabItems>
                <TabViewItem Header="Instagram" IsClosable="False"/>
                <TabViewItem Header="Tiktok" IsClosable="False"/>
                <TabViewItem Header="X(推特)" IsClosable="False"/>
            </TabView.TabItems>
        </TabView>

        <!-- 操作栏 -->
        <Grid Grid.Row="1" Margin="0,16,0,16">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <!-- 左侧筛选器 -->
            <ComboBox Grid.Column="0" 
                     x:Name="StatusFilterComboBox"
                     PlaceholderText="账号状态" 
                     Width="150"
                     SelectedIndex="0">
                <ComboBoxItem Content="所有状态"/>
                <ComboBoxItem Content="可用"/>
                <ComboBoxItem Content="不可用"/>
            </ComboBox>

            <!-- 右侧按钮 -->
            <StackPanel Grid.Column="2" 
                       Orientation="Horizontal" 
                       Spacing="8">
                <!-- 导入帮助按钮 -->
                <Button x:Name="ImportHelpButton" 
                       Content="?"
                       Width="36"
                       Height="36"
                       Background="#FF9500"
                       Foreground="White"
                       FontWeight="Bold"
                       FontSize="14"
                       CornerRadius="18"
                       ToolTipService.ToolTip="查看导入帮助"
                       Click="ImportHelpButton_Click">
                    <Button.Resources>
                        <ResourceDictionary>
                            <SolidColorBrush x:Key="ButtonBackgroundPointerOver" Color="#FFB03D"/>
                            <SolidColorBrush x:Key="ButtonBackgroundPressed" Color="#E6860A"/>
                            <SolidColorBrush x:Key="ButtonForegroundPointerOver" Color="White"/>
                            <SolidColorBrush x:Key="ButtonForegroundPressed" Color="White"/>
                        </ResourceDictionary>
                    </Button.Resources>
                </Button>

                <!-- 下载模板按钮 -->
                <Button x:Name="DownloadTemplateButton" 
                       Content="下载模板"
                       Background="#2E8B57"
                       Foreground="White"
                       ToolTipService.ToolTip="下载导入模板文件"
                       Click="DownloadTemplateButton_Click">
                    <Button.Resources>
                        <ResourceDictionary>
                            <SolidColorBrush x:Key="ButtonBackgroundPointerOver" Color="#3CB371"/>
                            <SolidColorBrush x:Key="ButtonBackgroundPressed" Color="#228B22"/>
                            <SolidColorBrush x:Key="ButtonForegroundPointerOver" Color="White"/>
                            <SolidColorBrush x:Key="ButtonForegroundPressed" Color="White"/>
                        </ResourceDictionary>
                    </Button.Resources>
                </Button>
                
                <Button x:Name="ImportButton" 
                       Content="导入账号"
                       Background="{ThemeResource AccentFillColorDefaultBrush}"
                       Foreground="White">
                </Button>
                <Button x:Name="BatchDeleteButton" 
                       Content="批量删除"
                       Background="#E74C3C"
                       Foreground="White"
                       IsEnabled="False">
                </Button>
            </StackPanel>
        </Grid>

        <!-- 账号列表 -->
        <Grid Grid.Row="2">
            <Grid.RowDefinitions>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>
            
            <Border Grid.Row="0"
                   Background="{ThemeResource CardBackgroundFillColorDefaultBrush}"
                   BorderBrush="{ThemeResource CardStrokeColorDefaultBrush}"
                   BorderThickness="1"
                   CornerRadius="8">
                
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    
                    <!-- 表头 -->
                    <Grid Grid.Row="0" 
                          Padding="16,12" 
                          Background="{ThemeResource CardBackgroundFillColorSecondaryBrush}">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="48"/>
                            <ColumnDefinition Width="2*"/>
                            <ColumnDefinition Width="3*"/>
                            <ColumnDefinition Width="2*"/>
                            <ColumnDefinition Width="2*"/>
                            <ColumnDefinition Width="2*"/>
                            <ColumnDefinition Width="2*"/>
                        </Grid.ColumnDefinitions>
                        
                        <CheckBox Grid.Column="0" 
                                 IsChecked="False"
                                 IsThreeState="True"
                                 x:Name="SelectAllCheckBox"
                                 Style="{StaticResource EnhancedCheckBoxStyle}"/>
                        <TextBlock Grid.Column="1" Text="名称" VerticalAlignment="Center" FontWeight="SemiBold"/>
                        <TextBlock Grid.Column="2" Text="登录账号" VerticalAlignment="Center" FontWeight="SemiBold"/>
                        <TextBlock Grid.Column="3" Text="模拟器ID" VerticalAlignment="Center" FontWeight="SemiBold"/>
                        <TextBlock Grid.Column="4" Text="账号状态" VerticalAlignment="Center" FontWeight="SemiBold"/>
                        <TextBlock Grid.Column="5" Text="更新时间" VerticalAlignment="Center" FontWeight="SemiBold"/>
                        <TextBlock Grid.Column="6" Text="操作" VerticalAlignment="Center" FontWeight="SemiBold"/>
                    </Grid>
                    
                    <!-- 表格内容 -->
                    <ListView Grid.Row="1" 
                             x:Name="AccountsListView"
                             SelectionMode="None"
                             IsItemClickEnabled="False">
                        <ListView.ItemContainerStyle>
                            <Style TargetType="ListViewItem" BasedOn="{StaticResource SimpleListViewItemStyle}">
                                <Setter Property="HorizontalContentAlignment" Value="Stretch"/>
                                <Setter Property="Padding" Value="0"/>
                                <Setter Property="MinHeight" Value="0"/>
                            </Style>
                        </ListView.ItemContainerStyle>
                        
                        <ListView.ItemTemplate>
                            <DataTemplate x:DataType="models:UserAccount">
                                <Grid Padding="16,12" BorderBrush="{ThemeResource CardStrokeColorDefaultBrush}" BorderThickness="0,0,0,1">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="48"/>
                                        <ColumnDefinition Width="2*"/>
                                        <ColumnDefinition Width="3*"/>
                                        <ColumnDefinition Width="2*"/>
                                        <ColumnDefinition Width="2*"/>
                                        <ColumnDefinition Width="2*"/>
                                        <ColumnDefinition Width="2*"/>
                                    </Grid.ColumnDefinitions>
                                    
                                    <CheckBox Grid.Column="0" 
                                             IsChecked="{x:Bind IsSelected, Mode=TwoWay}"
                                             Style="{StaticResource EnhancedCheckBoxStyle}"
                                             Click="ItemCheckBox_Click"/>
                                    <TextBlock Grid.Column="1" 
                                              Text="{x:Bind AccountName}" 
                                              VerticalAlignment="Center"
                                              FontWeight="Medium"/>
                                    <TextBlock Grid.Column="2" 
                                              Text="{x:Bind LoginAccount}" 
                                              VerticalAlignment="Center"
                                              FontWeight="Medium"/>
                                    
                                    <!-- 模拟器ID列 -->
                                    <StackPanel Grid.Column="3" Orientation="Horizontal" Spacing="8" VerticalAlignment="Center">
                                        <TextBlock VerticalAlignment="Center" FontWeight="Medium">
                                            <TextBlock.Text>
                                                <Binding Path="SimulatorId">
                                                    <Binding.Converter>
                                                        <converters:SimulatorIdConverter/>
                                                    </Binding.Converter>
                                                </Binding>
                                            </TextBlock.Text>
                                        </TextBlock>
                                        <Button Content="解绑" 
                                               Background="#FF6B35" 
                                               Foreground="White"
                                               FontSize="12"
                                               Padding="8,4"
                                               Tag="{Binding}"
                                               Click="UnbindSimulator_Click"
                                               CornerRadius="4"
                                               MinWidth="46"
                                               MinHeight="28"
                                               Visibility="{Binding SimulatorId, Converter={StaticResource SimulatorIdVisibilityConverter}}">
                                            <Button.Resources>
                                                <ResourceDictionary>
                                                    <SolidColorBrush x:Key="ButtonBackgroundPointerOver" Color="#FF8C5A"/>
                                                    <SolidColorBrush x:Key="ButtonBackgroundPressed" Color="#E55A28"/>
                                                    <SolidColorBrush x:Key="ButtonForegroundPointerOver" Color="White"/>
                                                    <SolidColorBrush x:Key="ButtonForegroundPressed" Color="White"/>
                                                </ResourceDictionary>
                                            </Button.Resources>
                                        </Button>
                                    </StackPanel>
                                    
                                    <TextBlock Grid.Column="4" 
                                              Text="{Binding AccountStatus, Converter={StaticResource AccountStatusConverter}}"
                                              Foreground="{Binding AccountStatus, Converter={StaticResource AccountStatusColorConverter}}"
                                              VerticalAlignment="Center"
                                              FontWeight="SemiBold"/>
                                    <TextBlock Grid.Column="5" 
                                              Text="{x:Bind UpdatedAt, Converter={StaticResource DateTimeToStringConverter}}"
                                              VerticalAlignment="Center"
                                              FontWeight="Medium"/>
                                    
                                    <StackPanel Grid.Column="6" Orientation="Horizontal" Spacing="8">
                                        <Button Content="编辑" 
                                               Style="{StaticResource AccentButtonStyle}"
                                               Tag="{Binding}"
                                               Click="EditButton_Click"
                                               FontSize="12"
                                               Padding="8,4"/>
                                        <Button Content="删除" 
                                               Background="#E74C3C" 
                                               Foreground="White"
                                               Tag="{Binding}"
                                               Click="DeleteButton_Click"
                                               FontSize="12"
                                               Padding="8,4"/>
                                    </StackPanel>
                                </Grid>
                            </DataTemplate>
                        </ListView.ItemTemplate>
                    </ListView>

                    <!-- 空状态提示 -->
                    <StackPanel Grid.Row="1"
                               VerticalAlignment="Center"
                               HorizontalAlignment="Center"
                               Visibility="{x:Bind IsEmptyList, Mode=OneWay, Converter={StaticResource BoolToVisibilityConverter}}">
                        <FontIcon FontFamily="{StaticResource SymbolThemeFontFamily}" 
                                 Glyph="&#xE1CE;" 
                                 FontSize="48" 
                                 Foreground="{ThemeResource TextFillColorSecondaryBrush}"/>
                        <TextBlock Text="暂无账号数据" 
                                  FontSize="16" 
                                  Foreground="{ThemeResource TextFillColorSecondaryBrush}"
                                  HorizontalAlignment="Center"
                                  Margin="0,8,0,0"/>
                        <TextBlock Text="点击'导入账号'按钮添加账号" 
                                   FontSize="12" 
                                   Foreground="{ThemeResource TextFillColorTertiaryBrush}"
                                   HorizontalAlignment="Center"
                                   Margin="0,4,0,0"/>
                    </StackPanel>
                </Grid>
            </Border>

            <!-- 分页控件 -->
            <Border Grid.Row="1" 
                   Background="{ThemeResource CardBackgroundFillColorDefaultBrush}"
                   BorderBrush="{ThemeResource CardStrokeColorDefaultBrush}"
                   BorderThickness="1,0,1,1"
                   CornerRadius="0,0,8,8"
                   Padding="16,12"
                   Margin="0,-1,0,0">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- 分页信息 -->
                    <TextBlock Grid.Column="0" 
                              Text="{x:Bind PageInfo, Mode=OneWay}"
                              VerticalAlignment="Center"
                              FontSize="14"
                              Foreground="{ThemeResource TextFillColorSecondaryBrush}"/>

                    <!-- 分页按钮 -->
                    <StackPanel Grid.Column="1" 
                               Orientation="Horizontal" 
                               Spacing="8">
                        <!-- 首页按钮 -->
                        <Button Content="首页"
                               IsEnabled="{x:Bind CanGoToPreviousPage, Mode=OneWay}"
                               Tag="First"
                               Click="PaginationButton_Click"
                               Style="{StaticResource DefaultButtonStyle}"
                               FontSize="12"
                               Padding="12,6"/>

                        <!-- 上一页按钮 -->
                        <Button IsEnabled="{x:Bind CanGoToPreviousPage, Mode=OneWay}"
                               Tag="Previous"
                               Click="PaginationButton_Click"
                               Style="{StaticResource DefaultButtonStyle}"
                               FontSize="12"
                               Padding="8,6">
                            <FontIcon FontFamily="{StaticResource SymbolThemeFontFamily}" 
                                     Glyph="&#xE76B;" 
                                     FontSize="12"/>
                        </Button>

                        <!-- 页码输入框 -->
                        <NumberBox Value="{x:Bind CurrentPage, Mode=OneWay}"
                                  Minimum="1"
                                  Maximum="{x:Bind TotalPages, Mode=OneWay}"
                                  Width="60"
                                  FontSize="12"
                                  SpinButtonPlacementMode="Hidden"/>

                        <TextBlock Text="/"
                                  VerticalAlignment="Center"
                                  FontSize="14"
                                  Foreground="{ThemeResource TextFillColorSecondaryBrush}"/>

                        <TextBlock Text="{x:Bind TotalPages, Mode=OneWay}"
                                  VerticalAlignment="Center"
                                  FontSize="14"
                                  Foreground="{ThemeResource TextFillColorSecondaryBrush}"/>

                        <!-- 下一页按钮 -->
                        <Button IsEnabled="{x:Bind CanGoToNextPage, Mode=OneWay}"
                               Tag="Next"
                               Click="PaginationButton_Click"
                               Style="{StaticResource DefaultButtonStyle}"
                               FontSize="12"
                               Padding="8,6">
                            <FontIcon FontFamily="{StaticResource SymbolThemeFontFamily}" 
                                     Glyph="&#xE76C;" 
                                     FontSize="12"/>
                        </Button>

                        <!-- 末页按钮 -->
                        <Button Content="末页"
                               IsEnabled="{x:Bind CanGoToNextPage, Mode=OneWay}"
                               Tag="Last"
                               Click="PaginationButton_Click"
                               Style="{StaticResource DefaultButtonStyle}"
                               FontSize="12"
                               Padding="12,6"/>
                    </StackPanel>
                </Grid>
            </Border>
            
            <!-- 提示气泡 -->
            <TeachingTip x:Name="ImportTip"
                        Title="支持 .txt / .csv 文件导入"
                        PreferredPlacement="Right"
                        IsOpen="False"
                        CloseButtonContent="我知道了"
                        Target="{x:Bind ImportButton}">
                <StackPanel Spacing="8">
                    <TextBlock TextWrapping="Wrap" FontSize="14">
                        📝 <Run FontWeight="SemiBold">TXT 格式：</Run><LineBreak/>
                        <Run Foreground="{ThemeResource TextFillColorSecondaryBrush}">账号名称,登录账号,密码</Run>
                    </TextBlock>
                    <TextBlock TextWrapping="Wrap" FontSize="14">
                        📊 <Run FontWeight="SemiBold">CSV 格式：</Run><LineBreak/>
                        <Run Foreground="{ThemeResource TextFillColorSecondaryBrush}">账号名称 登录账号 密码</Run>
                    </TextBlock>
                </StackPanel>
            </TeachingTip>
        </Grid>
    </Grid>
</Page>
