using App2.Models;
using App2.Services;
using App2.Utils;
using Microsoft.UI.Xaml;
using Microsoft.UI.Xaml.Controls;
using Microsoft.UI.Xaml.Navigation;
using System;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using Windows.Storage.Pickers;

namespace App2.Views.Pages
{
    public sealed partial class SimulatorPage : Page
    {
        private readonly SimulatorService _simulatorService;
        private ObservableCollection<Simulator> _simulators;
        private int _currentEditingId;

        /// <summary>
        /// 默认的雷电模拟器路径
        /// </summary>
        private const string DefaultSimulatorPath = @"C:\leidian\LDPlayer9\dnconsole.exe";

        /// <summary>
        /// 默认的ADB路径
        /// </summary>
        private const string DefaultAdbPath = @"C:\leidian\LDPlayer9\adb.exe";

        public SimulatorPage()
        {
            // 方法1：使用重写的ToString方法（简洁）
            System.Diagnostics.Debug.WriteLine($"### CurrentUser: {SessionService.CurrentUser?.Id}");
            
            this.InitializeComponent();
            _simulatorService = new SimulatorService();
            _simulators = new ObservableCollection<Simulator>();
            
            SimulatorListView.ItemsSource = _simulators;
        }

        protected override async void OnNavigatedTo(NavigationEventArgs e)
        {
            base.OnNavigatedTo(e);
            await LoadSimulatorsAsync();
            
            // 初始化模拟器资源按钮状态
            UpdateSimulatorResourceButton();
            // 初始化ADB状态
            await CheckAdbStatusAsync();
        }

        private async Task LoadSimulatorsAsync()
        {
            try
            {
                var simulators = await _simulatorService.GetAllSimulatorsAsync();
                _simulators.Clear();
                
                System.Diagnostics.Debug.WriteLine("-------------- 加载模拟器列表 --------------");
                foreach (var simulator in simulators)
                {
                    System.Diagnostics.Debug.WriteLine($"模拟器 ID:{simulator.Id}, 名称:{simulator.Title}, 状态值:{simulator.Status}, 状态文本:{simulator.StatusText}");
                    
                    // 强制触发属性更改通知测试
                    var tempStatus = simulator.Status;
                    simulator.Status = tempStatus;
                    
                    _simulators.Add(simulator);
                }
                System.Diagnostics.Debug.WriteLine($"共加载 {simulators.Count} 个模拟器");
                
                // 强制刷新UI
                if (SimulatorListView.ItemsSource != _simulators)
                {
                    SimulatorListView.ItemsSource = _simulators;
                }
            }
            catch (Exception ex)
            {
                // 错误处理
                var dialog = new ContentDialog
                {
                    Title = "错误",
                    Content = $"加载模拟器列表失败：{ex.Message}",
                    CloseButtonText = "确定",
                    XamlRoot = this.XamlRoot
                };
                await dialog.ShowAsync();
            }
        }

        /// <summary>
        /// 获取当前雷电模拟器路径
        /// </summary>
        private string? GetCurrentSimulatorPath()
        {
            // 先尝试从����置中获取
            string? simulatorPath = SettingsService.GetSimulatorPath();
            
            if (!string.IsNullOrEmpty(simulatorPath) && File.Exists(simulatorPath))
            {
                return simulatorPath;
            }

            // 尝试默认路径
            if (File.Exists(DefaultSimulatorPath))
            {
                SettingsService.SetSimulatorPath(DefaultSimulatorPath);
                return DefaultSimulatorPath;
            }

            return null;
        }

        /// <summary>
        /// 页面加载时检查ADB状态
        /// </summary>
        private async Task CheckAdbStatusAsync()
        {
            try
            {
                string? adbPath = SettingsService.GetAdbPath();
                bool adbFound = false;

                // 1. 检查���户配置的路径
                if (!string.IsNullOrEmpty(adbPath) && File.Exists(adbPath))
                {
                    adbFound = true;
                }
                
                // 2. 检查默认路径
                if (!adbFound && File.Exists(DefaultAdbPath))
                {
                    adbPath = DefaultAdbPath;
                    adbFound = true;
                    SettingsService.SetAdbPath(adbPath);
                }

                // 3. 检查模拟器自带的ADB
                if (!adbFound)
                {
                    string? simulatorPath = GetCurrentSimulatorPath();
                    if (!string.IsNullOrEmpty(simulatorPath))
                    {
                        string simulatorDir = Path.GetDirectoryName(simulatorPath) ?? "";
                        string simulatorAdbPath = Path.Combine(simulatorDir, "adb.exe");
                        if (File.Exists(simulatorAdbPath))
                        {
                            adbPath = simulatorAdbPath;
                            adbFound = true;
                            SettingsService.SetAdbPath(adbPath);
                        }
                    }
                }

                // 4. 检查系统PATH中的ADB
                if (!adbFound)
                {
                    try
                    {
                        var process = new System.Diagnostics.Process();
                        process.StartInfo.FileName = "where";
                        process.StartInfo.Arguments = "adb";
                        process.StartInfo.UseShellExecute = false;
                        process.StartInfo.RedirectStandardOutput = true;
                        process.StartInfo.CreateNoWindow = true;
                        process.Start();

                        string output = await process.StandardOutput.ReadToEndAsync();
                        await process.WaitForExitAsync();

                        if (process.ExitCode == 0 && !string.IsNullOrEmpty(output))
                        {
                            adbPath = output.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries)[0];
                            adbFound = true;
                            SettingsService.SetAdbPath(adbPath);
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"检查系统PATH中的ADB失败: {ex.Message}");
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"检查ADB状态失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 更新模拟器资源按钮状态
        /// </summary>
        private void UpdateSimulatorResourceButton()
        {
            try
            {
                string? simulatorPath = GetCurrentSimulatorPath();
                bool simulatorFound = !string.IsNullOrEmpty(simulatorPath) && File.Exists(simulatorPath);
                
                // 更新状态指示器颜色
                StatusIndicator.Fill = simulatorFound ? 
                    new Microsoft.UI.Xaml.Media.SolidColorBrush(Microsoft.UI.Colors.Green) : 
                    new Microsoft.UI.Xaml.Media.SolidColorBrush(Microsoft.UI.Colors.Red);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"更新模拟器资源按钮状态失败: {ex.Message}");
                StatusIndicator.Fill = new Microsoft.UI.Xaml.Media.SolidColorBrush(Microsoft.UI.Colors.Red);
            }
        }

        // 事件处理器方法

        private async void InitializeSimulatorButton_Click(object sender, RoutedEventArgs e)
        {
            // 模拟器初始化逻辑
            await ShowMessageDialog("提示", "模拟器初始化功能待实现");
        }

        private async void SimulatorResource_Click(object sender, RoutedEventArgs e)
        {
            // 选择模拟器路径
            var picker = new FileOpenPicker();
            picker.FileTypeFilter.Add(".exe");
            picker.SuggestedStartLocation = PickerLocationId.ComputerFolder;
            
            // 获取当前窗口句柄
            var window = (Application.Current as App)?.MainWindow;
            if (window != null)
            {
                var hwnd = WinRT.Interop.WindowNative.GetWindowHandle(window);
                WinRT.Interop.InitializeWithWindow.Initialize(picker, hwnd);
            }

            var file = await picker.PickSingleFileAsync();
            if (file != null)
            {
                SettingsService.SetSimulatorPath(file.Path);
                UpdateSimulatorResourceButton();
            }
        }

        private async void Adb_Click(object sender, RoutedEventArgs e)
        {
            // 选择ADB路径
            var picker = new FileOpenPicker();
            picker.FileTypeFilter.Add(".exe");
            picker.SuggestedStartLocation = PickerLocationId.ComputerFolder;
            
            // 获取当前窗口句柄
            var window = (Application.Current as App)?.MainWindow;
            if (window != null)
            {
                var hwnd = WinRT.Interop.WindowNative.GetWindowHandle(window);
                WinRT.Interop.InitializeWithWindow.Initialize(picker, hwnd);
            }

            var file = await picker.PickSingleFileAsync();
            if (file != null)
            {
                SettingsService.SetAdbPath(file.Path);
                await CheckAdbStatusAsync();
            }
        }

        private async void AddSimulator_Click(object sender, RoutedEventArgs e)
        {
            // 清空表单
            NameTextBox.Text = "";
            StatusComboBox.SelectedIndex = 0; // 默认选择启用
            TypeComboBox.SelectedIndex = 0;
            ConfigTextBox.Text = "";
            
            _currentEditingId = 0;
            SimulatorDialog.Title = "新建模拟器";
            
            System.Diagnostics.Debug.WriteLine("新建模拟器对话框，默认状态设置为启用(0)");
            
            await SimulatorDialog.ShowAsync();
        }

        private async void EditSimulator_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is int id)
            {
                var simulator = _simulators.FirstOrDefault(s => s.Id == id);
                if (simulator != null)
                {
                    NameTextBox.Text = simulator.Title;
                    
                    // 修复：根据Status值正确设置ComboBox选中项
                    // Status值：1=启用，2=关闭
                    // ComboBox项：0=启用(Tag="1")，1=关闭(Tag="2")
                    StatusComboBox.SelectedIndex = simulator.Status == 1 ? 0 : 1;
                    
                    TypeComboBox.SelectedIndex = 0; // 默认为leidian
                    ConfigTextBox.Text = simulator.Config ?? "";
                    
                    _currentEditingId = id;
                    SimulatorDialog.Title = "编辑模拟器";
                    
                    await SimulatorDialog.ShowAsync();
                }
            }
        }

        private async void DeleteSimulator_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is int id)
            {
                var dialog = new ContentDialog
                {
                    Title = "确认删除",
                    Content = "确定要删除这个模拟器吗？",
                    PrimaryButtonText = "删除",
                    SecondaryButtonText = "取消",
                    XamlRoot = this.XamlRoot
                };

                if (await dialog.ShowAsync() == ContentDialogResult.Primary)
                {
                    try
                    {
                        await _simulatorService.DeleteSimulatorAsync(id);
                        await LoadSimulatorsAsync();
                    }
                    catch (Exception ex)
                    {
                        await ShowMessageDialog("错误", $"删除模拟器失败：{ex.Message}");
                    }
                }
            }
        }

        private async void ConfigSimulator_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is int id)
            {
                var simulator = _simulators.FirstOrDefault(s => s.Id == id);
                if (simulator != null)
                {
                    _currentEditingId = id;
                    await LoadConfigDialog(simulator);
                    await ConfigDialog.ShowAsync();
                }
            }
        }

        private void ConfigButton_Loaded(object sender, RoutedEventArgs e)
        {
            // ConfigButton加载完成的处理
        }

        private async void SimulatorDialog_PrimaryButtonClick(ContentDialog sender, ContentDialogButtonClickEventArgs args)
        {
            try
            {
                // 优化状态解析逻辑
                int status = 1; // 默认为启用
                
                System.Diagnostics.Debug.WriteLine($"StatusComboBox.SelectedIndex: {StatusComboBox.SelectedIndex}");
                System.Diagnostics.Debug.WriteLine($"StatusComboBox.SelectedItem: {StatusComboBox.SelectedItem}");
                
                if (StatusComboBox.SelectedItem is ComboBoxItem item && item.Tag != null)
                {
                    if (int.TryParse(item.Tag.ToString(), out var tagValue))
                    {
                        status = tagValue;
                        System.Diagnostics.Debug.WriteLine($"从Tag解析状态值: {status}");
                    }
                }
                else
                {
                    // 备用逻辑：根据SelectedIndex设置状态
                    // 索引0=启用(状态值1)，索引1=关闭(状态值2)
                    status = StatusComboBox.SelectedIndex == 0 ? 1 : 2;
                    System.Diagnostics.Debug.WriteLine($"从SelectedIndex解析状态值: {status} (索引: {StatusComboBox.SelectedIndex})");
                }
                
                // 如果是编辑现有模拟器，先获取原有数据
                string marikName = NameTextBox.Text; // 默认使用Title作为MarikName
                string? config = ConfigTextBox.Text;
                
                if (_currentEditingId > 0)
                {
                    // 编辑现有模拟器时，保留原有的MarikName
                    var existingSimulator = _simulators.FirstOrDefault(s => s.Id == _currentEditingId);
                    if (existingSimulator != null)
                    {
                        marikName = existingSimulator.MarikName ?? NameTextBox.Text;
                    }
                }
                
                var simulator = new Simulator
                {
                    Title = NameTextBox.Text,
                    Status = status,
                    Type = "leidian",
                    Config = config,
                    MarikName = marikName
                };
                
                System.Diagnostics.Debug.WriteLine($"-------------- 保存模拟器信息 --------------");
                System.Diagnostics.Debug.WriteLine($"Title: {simulator.Title}");
                System.Diagnostics.Debug.WriteLine($"Status: {simulator.Status} ({simulator.StatusText})");
                System.Diagnostics.Debug.WriteLine($"Type: {simulator.Type}");
                System.Diagnostics.Debug.WriteLine($"MarikName: {simulator.MarikName}");
                System.Diagnostics.Debug.WriteLine($"Config: {simulator.Config}");

                if (_currentEditingId == 0)
                {
                    // 新建
                    System.Diagnostics.Debug.WriteLine("执行新建操作");
                    await _simulatorService.AddSimulatorAsync(simulator);
                }
                else
                {
                    // 编辑
                    System.Diagnostics.Debug.WriteLine($"执行编辑操作，ID: {_currentEditingId}");
                    simulator.Id = _currentEditingId;
                    await _simulatorService.UpdateSimulatorAsync(simulator);
                }

                await LoadSimulatorsAsync();
                System.Diagnostics.Debug.WriteLine("模拟器保存完成，列表已刷新");
            }
            catch (Exception ex)
            {
                args.Cancel = true;
                System.Diagnostics.Debug.WriteLine($"保存模拟器失败: {ex.Message}");
                await ShowMessageDialog("错误", $"保存模拟器失败：{ex.Message}");
            }
        }

        private void SimulatorDialog_SecondaryButtonClick(ContentDialog sender, ContentDialogButtonClickEventArgs args)
        {
            // 取消操作，不需要特殊处理
        }

        private async void ConfigDialog_PrimaryButtonClick(ContentDialog sender, ContentDialogButtonClickEventArgs args)
        {
            try
            {
                var config = BuildConfigFromDialog();
                var simulator = _simulators.FirstOrDefault(s => s.Id == _currentEditingId);
                if (simulator != null)
                {
                    simulator.Config = JsonSerializer.Serialize(config);
                    await _simulatorService.UpdateSimulatorAsync(simulator);
                    
                    // 如果有雷电模拟器路径，应用配置
                    string? simulatorPath = GetCurrentSimulatorPath();
                    if (!string.IsNullOrEmpty(simulatorPath) && !string.IsNullOrEmpty(simulator.MarikName))
                    {
                        await ApplyLeidianConfig(simulatorPath, simulator.MarikName, config);
                    }
                }
            }
            catch (Exception ex)
            {
                args.Cancel = true;
                await ShowMessageDialog("错误", $"保存配置失败：{ex.Message}");
            }
        }

        private void ConfigDialog_SecondaryButtonClick(ContentDialog sender, ContentDialogButtonClickEventArgs args)
        {
            // 取消操作，不需要特殊处理
        }

        // 辅助方法

        private async Task LoadConfigDialog(Simulator simulator)
        {
            // 清空所有字段
            ClearConfigDialog();
            
            try
            {
                // 从数据库重新获取最新的模拟器数据
                var latestSimulator = await _simulatorService.GetSimulatorByIdAsync(simulator.Id);
                if (latestSimulator == null)
                {
                    await ShowMessageDialog("错误", "模拟器数据不存在");
                    return;
                }
                
                // 如果模拟器有配置数据，解析并显示
                if (!string.IsNullOrEmpty(latestSimulator.Config))
                {
                    try
                    {
                        var config = JsonSerializer.Deserialize<SimulatorConfig>(latestSimulator.Config);
                        if (config != null)
                        {
                            // 性能设置
                            ResolutionWidthTextBox.Text = config.Width?.ToString() ?? "";
                            ResolutionHeightTextBox.Text = config.Height?.ToString() ?? "";
                            ResolutionDpiTextBox.Text = config.Dpi?.ToString() ?? "";
                            
                            // 设置CPU下拉框
                            if (config.Cpu.HasValue)
                            {
                                SetComboBoxByTag(CpuComboBox, config.Cpu.Value);
                            }
                            
                            // 设置内存下拉框
                            if (config.Memory.HasValue)
                            {
                                SetComboBoxByTag(MemoryComboBox, config.Memory.Value);
                            }
                            
                            // // 设备信息设置
                            // ManufacturerTextBox.Text = config.Manufacturer ?? "";
                            // ModelTextBox.Text = config.Model ?? "";
                            // PnumberTextBox.Text = config.PhoneNumber ?? "";
                            // ImeiTextBox.Text = config.Imei ?? "";
                            // ImsiTextBox.Text = config.Imsi ?? "";
                            // AndroidIdTextBox.Text = config.AndroidId ?? "";
                            //
                            // // 其他高级设置
                            // SimSerialTextBox.Text = config.SimSerial ?? "";
                            // MacTextBox.Text = config.Mac ?? "";
                            
                            // 功能开关设置
                            AutorotateCheckBox.IsChecked = config.AutoRotate ?? false;
                            LockWindowCheckBox.IsChecked = config.LockWindow ?? false;
                            RootCheckBox.IsChecked = config.Root ?? false;
                            
                            System.Diagnostics.Debug.WriteLine($"已加载模拟器ID {simulator.Id} 的配置数据");
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"解析模拟器配置失败: {ex.Message}");
                        await ShowMessageDialog("错误", $"解析模拟器配置失败：{ex.Message}");
                    }
                }
                else
                {
                    // 如果没有配置数据，显示空表单，让用户可以新建配置
                    System.Diagnostics.Debug.WriteLine($"模拟器ID {simulator.Id} 没有配置数据，显示空表单");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"加载模拟器配置失败: {ex.Message}");
                await ShowMessageDialog("错误", $"加载模拟器配置失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 清空配置对话框所有字段
        /// </summary>
        private void ClearConfigDialog()
        {
            // 性能设置
            ResolutionWidthTextBox.Text = "";
            ResolutionHeightTextBox.Text = "";
            ResolutionDpiTextBox.Text = "";
            CpuComboBox.SelectedIndex = -1;
            MemoryComboBox.SelectedIndex = -1;
            
            // 设备信息设置
            ManufacturerTextBox.Text = "";
            ModelTextBox.Text = "";
            PnumberTextBox.Text = "";
            ImeiTextBox.Text = "";
            ImsiTextBox.Text = "";
            AndroidIdTextBox.Text = "";
            
            // 其他高级设置
            SimSerialTextBox.Text = "";
            MacTextBox.Text = "";
            
            // 功能开关设置
            AutorotateCheckBox.IsChecked = false;
            LockWindowCheckBox.IsChecked = false;
            RootCheckBox.IsChecked = false;
        }

        /// <summary>
        /// 根据Tag值设置ComboBox的选中项
        /// </summary>
        /// <param name="comboBox">目标ComboBox</param>
        /// <param name="tagValue">要匹配的Tag值</param>
        private void SetComboBoxByTag(ComboBox comboBox, object tagValue)
        {
            for (int i = 0; i < comboBox.Items.Count; i++)
            {
                if (comboBox.Items[i] is ComboBoxItem item && 
                    item.Tag != null && 
                    item.Tag.ToString() == tagValue.ToString())
                {
                    comboBox.SelectedIndex = i;
                    break;
                }
            }
        }

        private SimulatorConfig BuildConfigFromDialog()
        {
            return new SimulatorConfig
            {
                // 性能设置
                Width = int.TryParse(ResolutionWidthTextBox.Text, out int w) ? w : null,
                Height = int.TryParse(ResolutionHeightTextBox.Text, out int h) ? h : null,
                Dpi = int.TryParse(ResolutionDpiTextBox.Text, out int d) ? d : null,
                Cpu = ((ComboBoxItem)CpuComboBox.SelectedItem)?.Tag as int?,
                Memory = ((ComboBoxItem)MemoryComboBox.SelectedItem)?.Tag as int?,
                
                // 设备信息设置
                Manufacturer = string.IsNullOrWhiteSpace(ManufacturerTextBox.Text) ? null : ManufacturerTextBox.Text,
                Model = string.IsNullOrWhiteSpace(ModelTextBox.Text) ? null : ModelTextBox.Text,
                PhoneNumber = string.IsNullOrWhiteSpace(PnumberTextBox.Text) ? null : PnumberTextBox.Text,
                Imei = string.IsNullOrWhiteSpace(ImeiTextBox.Text) ? null : ImeiTextBox.Text,
                Imsi = string.IsNullOrWhiteSpace(ImsiTextBox.Text) ? null : ImsiTextBox.Text,
                AndroidId = string.IsNullOrWhiteSpace(AndroidIdTextBox.Text) ? null : AndroidIdTextBox.Text,
                
                // 其他高级设置
                SimSerial = string.IsNullOrWhiteSpace(SimSerialTextBox.Text) ? null : SimSerialTextBox.Text,
                Mac = string.IsNullOrWhiteSpace(MacTextBox.Text) ? null : MacTextBox.Text,
                
                // 功能开关设置
                AutoRotate = AutorotateCheckBox.IsChecked,
                LockWindow = LockWindowCheckBox.IsChecked,
                Root = RootCheckBox.IsChecked
            };
        }

        private async Task ApplyLeidianConfig(string simulatorPath, string simulatorName, SimulatorConfig config)
        {
            try
            {
                var options = new ModifyOptions
                {
                    Cpu = config.Cpu,
                    Memory = config.Memory,
                    Manufacturer = config.Manufacturer,
                    Model = config.Model,
                    PhoneNumber = config.PhoneNumber,
                    Imei = config.Imei,
                    AndroidId = config.AndroidId,
                    AutoRotate = config.AutoRotate,
                    LockWindow = config.LockWindow,
                    Root = config.Root
                };

                if (config.Width.HasValue && config.Height.HasValue && config.Dpi.HasValue)
                {
                    options.Resolution = $"{config.Width},{config.Height},{config.Dpi}";
                }

                await LeidianTool.ModifySimulatorAsync(simulatorPath, simulatorName, options);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"应用雷电模拟器配置失败: {ex.Message}");
            }
        }

        private async Task ShowMessageDialog(string title, string content)
        {
            var dialog = new ContentDialog
            {
                Title = title,
                Content = content,
                CloseButtonText = "确定",
                XamlRoot = this.XamlRoot
            };
            await dialog.ShowAsync();
        }

        /// <summary>
        /// 模拟器创建设置按钮点击事件
        /// </summary>
        private async void SimulatorDefaultsButton_Click(object sender, RoutedEventArgs e)
        {
            await LoadDefaultsDialog();
            await DefaultsDialog.ShowAsync();
        }

        /// <summary>
        /// 默认配置弹窗保存按钮点击事件
        /// </summary>
        private async void DefaultsDialog_PrimaryButtonClick(ContentDialog sender, ContentDialogButtonClickEventArgs args)
        {
            // 暂时阻止对话框关闭，我们将在操作完成后手动关闭它
            args.Cancel = true;
            
            try
            {
                var defaults = BuildDefaultsFromDialog();
                bool saved = await SimulatorDefaultsService.SaveDefaultsAsync(defaults);
                
                // 先关闭当前对话框
                sender.Hide();
                
                if (saved)
                {
                    await ShowMessageDialog("成功", "模拟器创建设置已保存！");
                }
                else
                {
                    await ShowMessageDialog("错误", "保存设置失败，请重试。");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"保存设置时发生错误: {ex.Message}");
                await ShowMessageDialog("错误", $"保存设置时发生错误：{ex.Message}");
            }
        }

        /// <summary>
        /// 默认配置弹窗取消按钮点击事件
        /// </summary>
        private void DefaultsDialog_SecondaryButtonClick(ContentDialog sender, ContentDialogButtonClickEventArgs args)
        {
            // 取消操作，不需要特殊处理
        }

        /// <summary>
        /// 重置为系统默认按钮点击事件
        /// </summary>
        private async void ResetDefaultsButton_Click(object sender, RoutedEventArgs e)
        {
            // 先关闭当前的设置对话框，避免多个对话框同时存在
            DefaultsDialog.Hide();
            
            var confirmDialog = new ContentDialog
            {
                Title = "确认重置",
                Content = "确定要重置为系统默认设置吗？当前的自定义设置将丢失。",
                PrimaryButtonText = "确定",
                SecondaryButtonText = "取消",
                XamlRoot = this.XamlRoot
            };

            if (await confirmDialog.ShowAsync() == ContentDialogResult.Primary)
            {
                try
                {
                    bool reset = await SimulatorDefaultsService.ResetToSystemDefaultsAsync();
                    
                    if (reset)
                    {
                        await ShowMessageDialog("成功", "已重置为系统默认设置。");
                        // 重新加载对话框数据并显示
                        await LoadDefaultsDialog();
                        await DefaultsDialog.ShowAsync();
                    }
                    else
                    {
                        await ShowMessageDialog("错误", "重置失败，请重试。");
                        // 重新显示设置对话框
                        await DefaultsDialog.ShowAsync();
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"重置时发生错误: {ex.Message}");
                    await ShowMessageDialog("错误", $"重置时发生错误：{ex.Message}");
                    // 重新显示设置对话框
                    await DefaultsDialog.ShowAsync();
                }
            }
            else
            {
                // 用户取消了重置操作，重新显示设置对话框
                await DefaultsDialog.ShowAsync();
            }
        }

        /// <summary>
        /// 加载默认配置到对话框
        /// </summary>
        private async Task LoadDefaultsDialog()
        {
            try
            {
                var defaults = await SimulatorDefaultsService.GetDefaultsAsync();

                // 性能设置
                DefaultResolutionWidthTextBox.Text = defaults.Performance.Width.ToString();
                DefaultResolutionHeightTextBox.Text = defaults.Performance.Height.ToString();
                DefaultResolutionDpiTextBox.Text = defaults.Performance.Dpi.ToString();
                
                // 设置CPU和内存下拉框
                SetComboBoxByTag(DefaultCpuComboBox, defaults.Performance.CpuInt);
                SetComboBoxByTag(DefaultMemoryComboBox, defaults.Performance.MemoryInt);
                
                // 游戏设置
                DefaultFpsSlider.Value = defaults.Game.FpsInt;
                
                // 其他设置
                DefaultAutorotateCheckBox.IsChecked = defaults.Other.AutoRotateBool;
                DefaultLockWindowCheckBox.IsChecked = defaults.Other.LockWindowBool;
                DefaultRootCheckBox.IsChecked = defaults.Other.RootBool;

                System.Diagnostics.Debug.WriteLine("已成功加载默认配置到对话框");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"加载默认配置失败: {ex.Message}");
                await ShowMessageDialog("错误", $"加载默认配置失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 从对话框构建默认配置
        /// </summary>
        private SimulatorDefaultConfig BuildDefaultsFromDialog()
        {
            var config = new SimulatorDefaultConfig();

            // 性能设置
            if (int.TryParse(DefaultResolutionWidthTextBox.Text, out int width) &&
                int.TryParse(DefaultResolutionHeightTextBox.Text, out int height) &&
                int.TryParse(DefaultResolutionDpiTextBox.Text, out int dpi))
            {
                config.Performance.Resolution = $"{width},{height},{dpi}";
            }

            if (DefaultCpuComboBox.SelectedItem is ComboBoxItem cpuItem && cpuItem.Tag != null)
            {
                config.Performance.Cpu = cpuItem.Tag.ToString() ?? "2";
            }

            if (DefaultMemoryComboBox.SelectedItem is ComboBoxItem memoryItem && memoryItem.Tag != null)
            {
                config.Performance.Memory = memoryItem.Tag.ToString() ?? "2048";
            }

            // 游戏设置
            config.Game.Fps = ((int)DefaultFpsSlider.Value).ToString();

            // 其他设置
            config.Other.AutoRotate = DefaultAutorotateCheckBox.IsChecked == true ? "1" : "0";
            config.Other.LockWindow = DefaultLockWindowCheckBox.IsChecked == true ? "1" : "0";
            config.Other.Root = DefaultRootCheckBox.IsChecked == true ? "1" : "0";

            return config;
        }

        /// <summary>
        /// 新建模拟器，应用默认配置
        /// </summary>
        private async void AddSimulatorWithDefaults_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // 获取默认配置
                var defaults = await SimulatorDefaultsService.GetDefaultsAsync();
                var defaultConfig = SimulatorDefaultsService.ApplyDefaults(defaults);

                // 清空表单
                NameTextBox.Text = "";
                StatusComboBox.SelectedIndex = 0;
                TypeComboBox.SelectedIndex = 0;
                
                // 将默认配置序列化到配置字段（隐藏字段）
                var options = new JsonSerializerOptions
                {
                    WriteIndented = true
                    // 移除不兼容的 TypeInfoResolver 设置
                };
                ConfigTextBox.Text = JsonSerializer.Serialize(defaultConfig, options);
                
                _currentEditingId = 0;
                SimulatorDialog.Title = "新建模拟器（将应用默认配置）";
                
                await SimulatorDialog.ShowAsync();
            }
            catch (Exception ex)
            {
                await ShowMessageDialog("错误", $"应用默认配置失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 检测用户模拟器按钮点击事件
        /// </summary>
        private async void CheckUserSimulator_Click(object sender, RoutedEventArgs e)
        {
            if (!int.TryParse(UserIdTextBox.Text, out int userId) || userId <= 0)
            {
                SimulatorStatusText.Text = "请输入有效的用户ID";
                SimulatorStatusText.Foreground = new Microsoft.UI.Xaml.Media.SolidColorBrush(Microsoft.UI.Colors.Red);
                SimulatorStatusText.Visibility = Visibility.Visible;
                return;
            }
            
            try
            {
                string? simulatorPath = GetCurrentSimulatorPath();
                if (string.IsNullOrEmpty(simulatorPath))
                {
                    SimulatorStatusText.Text = "未找到雷电模拟器，请先在设置中配置雷电模拟器路径";
                    SimulatorStatusText.Foreground = new Microsoft.UI.Xaml.Media.SolidColorBrush(Microsoft.UI.Colors.Red);
                    SimulatorStatusText.Visibility = Visibility.Visible;
                    return;
                }
                
                // 创建模拟器管理服务实例
                var simulatorManager = new SimulatorManagerService(simulatorPath);
                
                // 检测模拟器是否存在
                var result = await simulatorManager.CheckUserSimulatorAsync(userId);
                
                // 显示检测结果
                if (result.Code == 200)
                {
                    if (result.Data)
                    {
                        SimulatorStatusText.Text = $"模拟器已存在: simulator_{userId}";
                        SimulatorStatusText.Foreground = new Microsoft.UI.Xaml.Media.SolidColorBrush(Microsoft.UI.Colors.Green);
                    }
                    else
                    {
                        SimulatorStatusText.Text = $"模拟器不存在: simulator_{userId}";
                        SimulatorStatusText.Foreground = new Microsoft.UI.Xaml.Media.SolidColorBrush(Microsoft.UI.Colors.Orange);
                    }
                }
                else
                {
                    SimulatorStatusText.Text = $"检测失败: {result.Message}";
                    SimulatorStatusText.Foreground = new Microsoft.UI.Xaml.Media.SolidColorBrush(Microsoft.UI.Colors.Red);
                }
                
                SimulatorStatusText.Visibility = Visibility.Visible;
            }
            catch (Exception ex)
            {
                SimulatorStatusText.Text = $"检测模拟器时发生错误: {ex.Message}";
                SimulatorStatusText.Foreground = new Microsoft.UI.Xaml.Media.SolidColorBrush(Microsoft.UI.Colors.Red);
                SimulatorStatusText.Visibility = Visibility.Visible;
            }
        }

        /// <summary>
        /// 创建用户模拟器按钮点击事件
        /// </summary>
        private async void CreateUserSimulator_Click(object sender, RoutedEventArgs e)
        {
            // var defaultConfig = await SimulatorDefaultsService.GetDefaultsAsync();
            // // 使用适当的 TypeInfoResolver 配置
            // string jsonString = JsonSerializer.Serialize(defaultConfig, new JsonSerializerOptions 
            // { 
            //     WriteIndented = true,
            //     TypeInfoResolver = AppJsonSerializerContext.Default 
            // });
            // System.Diagnostics.Debug.WriteLine($"## CreateUserSimulator_Click@defaultConfig JSON: {jsonString}");
            // return;


            if (!int.TryParse(UserIdTextBox.Text, out int userId) || userId <= 0)
            {
                SimulatorStatusText.Text = "请输入有效的用户ID";
                SimulatorStatusText.Foreground = new Microsoft.UI.Xaml.Media.SolidColorBrush(Microsoft.UI.Colors.Red);
                SimulatorStatusText.Visibility = Visibility.Visible;
                return;
            }

            try
            {
                string? simulatorPath = GetCurrentSimulatorPath();
                if (string.IsNullOrEmpty(simulatorPath))
                {
                    SimulatorStatusText.Text = "未找到雷电模拟器，请先在设置中配置雷电模拟器路径";
                    SimulatorStatusText.Foreground = new Microsoft.UI.Xaml.Media.SolidColorBrush(Microsoft.UI.Colors.Red);
                    SimulatorStatusText.Visibility = Visibility.Visible;
                    return;
                }
                
                // 先显示正在处理的状态
                SimulatorStatusText.Text = "正在创建模拟器，请稍候...";
                SimulatorStatusText.Foreground = new Microsoft.UI.Xaml.Media.SolidColorBrush(Microsoft.UI.Colors.Blue);
                SimulatorStatusText.Visibility = Visibility.Visible;
                
                // 创建模拟器管理服务实例
                var simulatorManager = new SimulatorManagerService(simulatorPath);
                
                // 创建模拟器
                var result = await simulatorManager.CreateUserSimulatorAsync(userId);
                
                // 显示创建结果
                if (result.Code == 200)
                {
                    SimulatorStatusText.Text = "模拟器创建成功: "+result.Data;
                    SimulatorStatusText.Foreground = new Microsoft.UI.Xaml.Media.SolidColorBrush(Microsoft.UI.Colors.Green);
                    
                    // 刷新模拟器列表
                    await LoadSimulatorsAsync();
                }
                else
                {
                    SimulatorStatusText.Text = $"创建失败: {result.Message}";
                    SimulatorStatusText.Foreground = new Microsoft.UI.Xaml.Media.SolidColorBrush(Microsoft.UI.Colors.Red);
                }
                
                SimulatorStatusText.Visibility = Visibility.Visible;
            }
            catch (Exception ex)
            {
                SimulatorStatusText.Text = $"创建模拟器时发生错误: {ex.Message}";
                SimulatorStatusText.Foreground = new Microsoft.UI.Xaml.Media.SolidColorBrush(Microsoft.UI.Colors.Red);
                SimulatorStatusText.Visibility = Visibility.Visible;
            }
        }
    }
}
