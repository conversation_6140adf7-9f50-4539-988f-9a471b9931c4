
using App2.Helpers;
using App2.Models;
using App2.Services;
using Microsoft.UI.Xaml;
using Microsoft.UI.Xaml.Controls;
using Microsoft.UI.Xaml.Media;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Data;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using System.Threading.Tasks; // 添加异步支持
using Windows.Storage; // 添加Windows.Storage命名空间
using Windows.Storage.Pickers;



namespace App2.Views.Pages
{
   

    public sealed partial class ConfigPage : Page
    {

        private readonly RemoteDatabaseService _remoteDbService = new RemoteDatabaseService();
        private readonly ProxySocketTest _socket = new ProxySocketTest();
        //所有的proxy
        public ObservableCollection<DataItem> DataItems { get; } = new ObservableCollection<DataItem>();
        //测试可用的proxy
        public ObservableCollection<DataItem> DataItemsWithSuc { get; } = new ObservableCollection<DataItem>();
        //测试不可用的proxy
        public ObservableCollection<DataItem> DataItemsWithFail { get; } = new ObservableCollection<DataItem>();

                                    
        public ConfigPage()
        {
                InitializeComponent();
                 Loaded += ConfigPage_Loaded; // 添加页面加载事件
 
        }
        // 添加 DispatcherQueue 属性

        private async void ConfigPage_Loaded(object sender, RoutedEventArgs e)

        {
            await LoadProxiesAsync(); // 页面加载时读取保存的代理
        }
            // 从远程数据库加载代理
        public async Task LoadProxiesAsync()
         {
            DataItems.Clear(); // 清空现有集合
            var proxies = await _remoteDbService.GetProxyAsync();
            foreach (var proxy in proxies)
            {
                DataItems.Add(proxy); // 添加到现有集合
            }
            RefreshListView();
        }

            // 在数据变更时调用保存
        private async void SaveData( List<DataItem> date)
        {
            await SaveProxiesAsync(date);
        }
      
        private async Task SaveProxiesAsync(List<DataItem> date)
        {
            try
            {
                foreach (var item in date)
                {
                   await _remoteDbService.SaveOrUpdateProxyAsync(item);
                }
            }
            catch (Exception ex)
            {
                 Debug.WriteLine($"保存到远程数据库失败: {ex.Message}");
            }
        }

        private async void AddButton_Click(object sender, RoutedEventArgs e)
        {
            // 创建多行输入控件
            var inputPanel = new StackPanel { Spacing = 15 };
            var newDate = new List<DataItem>();

            // 标题
            var titleBlock = new TextBlock
            {
                Text = "输入数据（每行一项）:",
                FontSize = 16,
                FontWeight = Microsoft.UI.Text.FontWeights.SemiBold
            };

            // 多行文本框
            var inputTextBox = new TextBox
            {
                PlaceholderText = "在此输入数据，每行一项...",
                AcceptsReturn = true,
                TextWrapping = TextWrapping.Wrap,
                Height = 200,
                Width = 400,
                MaxLength = 500,
                FontFamily = new FontFamily("Consolas"),
                BorderThickness = new Thickness(1),
                CornerRadius = new CornerRadius(4),
                BorderBrush = (Brush)Application.Current.Resources["SystemControlBackgroundBaseMediumBrush"]
            };

            // 示例提示
            var exampleBlock = new TextBlock
            {
                Text = "示例:IP:HOST:USERNAME:PASSWORD",
                FontSize = 12,
                Foreground = (Brush)Application.Current.Resources["SystemControlForegroundBaseMediumBrush"],
               
            };

            inputPanel.Children.Add(titleBlock);
            inputPanel.Children.Add(inputTextBox);
            inputPanel.Children.Add(exampleBlock);

            var inputDialog = new ContentDialog
            {
                Title = "添加多条数据",
                PrimaryButtonText = "添加",
                CloseButtonText = "取消",
                DefaultButton = ContentDialogButton.Primary,
                XamlRoot = this.Content.XamlRoot,
                Content = inputPanel,
                RequestedTheme = this.ActualTheme
            };

            // 显示弹窗并等待结果
            var result = await inputDialog.ShowAsync();

            // 处理确认操作
            // 处理确认操作
            if (result == ContentDialogResult.Primary)
            {
                if (!string.IsNullOrWhiteSpace(inputTextBox.Text))
                {
                    var newItems = inputTextBox.Text
                        .Split(new[] { "\r\n", "\r", "\n" }, StringSplitOptions.None)
                        .Select(line => line.Trim())
                        .Where(line => !string.IsNullOrWhiteSpace(line))
                        .ToList();

                    if (newItems.Any())
                    {
                        foreach (var item in newItems)
                        {
                            // 解析代理信息格式
                            var parts = item.Split(':');

                            // 验证格式
                            if (parts.Length < 2)
                            {
                                // 无效格式，跳过
                                continue;
                            }

                            var newItem = new DataItem
                            {
                                Type = "Socket",
                                Host = parts[0].Trim(),
                                Port = parts[1].Trim(),
                                Status = "未测试",
                                LastUpdated = DateTime.Now
                            };

                            // 如果有用户名和密码
                            if (parts.Length >= 4)
                            {
                                newItem.Username = parts[2].Trim();
                                newItem.Password = parts[3].Trim();
                            }
                            newDate.Add(newItem);
                            DataItems.Add(newItem);
                            
                        }
                        SaveData(newDate);
                        RefreshListView();
                        //$"已添加 {newItems.Count} 项数据";
                    }
                    else
                    {
                       //"没有检测到有效数据";
                    }
                }
                else
                {
                   // "输入不能为空！";
                }
            }
            else
            {
               //"添加操作已取消";
            }
        }
           

        private void DeleteButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is DataItem item)
            {
                Debug.Write("删除proxy{item.id}");
                _remoteDbService.DeleteProxyAsyncc(item.Id);
                DataItems.Remove(item);
                RefreshListView(); 
            }
        }
        private async void TestOneButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is DataItem item)
            {
                await UiThreadDispatcher.RunAsync(() =>
                {
                    MaskLayer.Visibility = Visibility.Visible;
                });

                var throttler = new SemaphoreSlim(5);
                var tasks = new List<Task>();

                
                tasks.Add(_socket.TestProxyWithThrottle(item, throttler,"socket"));
               
                await Task.WhenAll(tasks);

                await _remoteDbService.UpdateProxyAsyncc(item);
                RefreshListView();
            }

        }
        
        private async void TestAllButton_Click(object sender, RoutedEventArgs e)
        {
            if (DataItems.Count == 0) return;


            // 禁用按钮防止重复点击
            if (sender is Button button) button.IsEnabled = false;

            try
            {
                await TestAllProxiesAsync();

            }
            finally
            {
                if (sender is Button btn) btn.IsEnabled = true;
            }
        }
        private async Task TestAllProxiesAsync()
        {

            await UiThreadDispatcher.RunAsync(() =>
            {
                MaskLayer.Visibility = Visibility.Visible;
            });
            await _socket.TestProxyBatchAsync(DataItems,"socket");

            foreach (var item in DataItems)
            {

                await _remoteDbService.UpdateProxyAsyncc(item);

            }
            RefreshListView();
           
        }
        private async void InputProxy_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // 创建文件选择器
                var openPicker = new FileOpenPicker();
                var newDate = new List<DataItem>();

                // 获取当前窗口句柄
                IntPtr hwnd = GetWindowHandleForCurrentWindow();
                WinRT.Interop.InitializeWithWindow.Initialize(openPicker, hwnd);

                openPicker.ViewMode = PickerViewMode.List;
                openPicker.SuggestedStartLocation = PickerLocationId.DocumentsLibrary;
                openPicker.FileTypeFilter.Add(".txt");
              //  openPicker.FileTypeFilter.Add("*"); // 允许所有文件类型

                // 显示文件选择器
                StorageFile file = await openPicker.PickSingleFileAsync();

                if (file != null)
                {
                    // 读取文件内容
                    var content = await FileIO.ReadTextAsync(file);

                    // 解析并添加代理
                    var lines = content.Split(new[] { "\r\n", "\r", "\n" },
                                             StringSplitOptions.RemoveEmptyEntries);

                    int addedCount = 0;
                    int errorCount = 0;

                    foreach (var line in lines)
                    {
                        var trimmedLine = line.Trim();
                        if (string.IsNullOrWhiteSpace(trimmedLine))
                            continue;

                        // 只使用冒号作为分隔符（因为您的格式是冒号分隔）
                        var parts = trimmedLine.Split(new[] { ':' }, StringSplitOptions.RemoveEmptyEntries);

                        // 验证格式 - 必须有至少4部分（主机:端口:用户名:密码）
                        if (parts.Length < 4)
                        {
                            errorCount++;
                            Debug.WriteLine($"格式错误: {trimmedLine}");
                            continue;
                        }

                        var newItem = new DataItem
                        {
                            Type = "Socket",
                            Host = parts[0].Trim(),
                            Port = parts[1].Trim(),
                            Username = parts[2].Trim(),
                            Password = parts[3].Trim(),
                            Status = "未测试",
                            LastUpdated = DateTime.Now
                        };

                        // 检查是否已存在相同的代理（比较所有关键字段）
                        bool isDuplicate = DataItems.Any(item =>
                            item.Host == newItem.Host &&
                            item.Port == newItem.Port &&
                            item.Username == newItem.Username &&
                            item.Password == newItem.Password);

                        if (!isDuplicate)
                        {
                            newDate.Add(newItem);
                            DataItems.Add(newItem);
                            
                        }
                        else
                        {
                         
                            Debug.WriteLine($"跳过重复代理: {trimmedLine}");
                        }
                    }



                    // 显示结果
                    // 保存并刷新UI
                    SaveData(newDate);
                    RefreshListView();
                }
              
            }
            catch (Exception ex)
            {
               
            }
        }

        // 获取当前窗口句柄的兼容方法
        private IntPtr GetWindowHandleForCurrentWindow()
        {
            // 方法1：尝试通过 App 类获取主窗口
            if (App.Current is App app && app.MainWindow != null)
            {
                return WinRT.Interop.WindowNative.GetWindowHandle(app.MainWindow);
            }

            // 方法2：尝试通过 Window.Current 获取
            if (Window.Current != null)
            {
                return WinRT.Interop.WindowNative.GetWindowHandle(Window.Current);
            }

            // 最后尝试：使用进程主窗口
            return Process.GetCurrentProcess().MainWindowHandle;
        }
      
        private void ComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (sender is ComboBox comboBox && comboBox.SelectedItem is ComboBoxItem selectedItem)
            {
                string selectedText = selectedItem.Content.ToString();
                // 处理选中的文本，例如：
                // MessageBox.Show($"您选择了: {selectedText}");
                System.Diagnostics.Debug.WriteLine($"您选择了: {selectedText}"); // 打印到输出窗口
                if (selectedText == "可用")
                {

                    selcetSuccessProxy();
                }
                else if (selectedText == "不可用") {
                    selcetFailProxy();
                }
                else {
                    RefreshListView();
                }
            }
        }

        private void selcetSuccessProxy() {
            DataItemsWithSuc.Clear();

            foreach (var item in DataItems) {

                if (item.Status == "可用") {

                    DataItemsWithSuc.Add(item);
                
                }
            
            }
            ProxiesListView.ItemsSource = null;
            ProxiesListView.ItemsSource = DataItemsWithSuc;
       
          

        }
        private void selcetFailProxy()
        {
            DataItemsWithFail.Clear();
            foreach (var item in DataItems)
            {

                if (item.Status != "可用")
                {
                    DataItemsWithFail.Add(item);
                   

                }

            }
            ProxiesListView.ItemsSource = null;
            ProxiesListView.ItemsSource = DataItemsWithFail;

        }
        private async void RefreshListView()
        {

            if (DataItems.Count == 0) return;

            ProxiesListView.ItemsSource = null;
            ProxiesListView.ItemsSource = DataItems;
            MaskLayer.Visibility = Visibility.Collapsed;


        }

    }
}

