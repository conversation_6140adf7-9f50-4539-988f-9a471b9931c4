<?xml version="1.0" encoding="utf-8"?>
<Page
    x:Class="App2.Views.Pages.SimulatorPage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:helpers="using:App2.Helpers"
    xmlns:models="using:App2.Models"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    mc:Ignorable="d"
    Background="{ThemeResource ApplicationPageBackgroundThemeBrush}">

    <!-- 需要确认App2.Helpers命名空间下是否存在这两个转换器 -->
    <Page.Resources>
        <!-- 修复建议1: 确认转换器实现 -->
        <helpers:DateTimeToStringConverter x:Key="DateTimeToStringConverter"/>
        <helpers:BoolToVisibilityConverter x:Key="BoolToVisibilityConverter"/>
    </Page.Resources>

    <Grid Padding="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- 顶部新增按钮 -->
        <StackPanel Grid.Row="0" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,0,0,10">
            <!-- 左侧添加模拟器创建设置按钮 -->
            <Button x:Name="SimulatorDefaultsButton" Content="模拟器创建设置" Click="SimulatorDefaultsButton_Click" 
                    Background="{ThemeResource AccentButtonBackground}" 
                    Foreground="{ThemeResource AccentButtonForeground}"
                    Margin="0,0,20,0"/>
            
            <!-- <Button x:Name="InitializeSimulatorButton" Content="模拟器初始化(创建模拟器)" Click="InitializeSimulatorButton_Click"/> -->
            
            <Button x:Name="SimulatorResourceButton" Click="SimulatorResource_Click" Margin="0,0,10,0">
                <StackPanel Orientation="Horizontal">
                    <Ellipse x:Name="StatusIndicator" Width="8" Height="8" Margin="0,0,5,0" VerticalAlignment="Center"/>
                    <TextBlock Text="雷电模拟器(状态)"/>
                </StackPanel>
            </Button>
            <!-- <Button Content="新建模拟器" Click="AddSimulatorWithDefaults_Click"/> -->
        </StackPanel>

        <!-- 模拟器列表 -->
        <Border Grid.Row="1"
               Background="{ThemeResource CardBackgroundFillColorDefaultBrush}"
               BorderBrush="{ThemeResource CardStrokeColorDefaultBrush}"
               BorderThickness="1"
               CornerRadius="8"
               Margin="0,0,0,20">
            <ScrollViewer>
                <StackPanel>
                    <!-- 列表头部 -->
                    <Grid Background="{ThemeResource SystemFillColorSolidNeutralBackgroundBrush}" Padding="10,8">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="50"/>
                            <ColumnDefinition Width="200"/>
                            <ColumnDefinition Width="200"/>
                            <ColumnDefinition Width="100"/>
                            <ColumnDefinition Width="150"/>
                            <ColumnDefinition Width="150"/>
                            <ColumnDefinition Width="120"/>
                            <ColumnDefinition Width="200"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Grid.Column="0" Text="ID" FontWeight="SemiBold"/>
                        <TextBlock Grid.Column="1" Text="模拟器" FontWeight="SemiBold"/>
                        <TextBlock Grid.Column="2" Text="模拟器标识" FontWeight="SemiBold"/>
                        <TextBlock Grid.Column="3" Text="状态" FontWeight="SemiBold"/>
                        <TextBlock Grid.Column="4" Text="创建时间" FontWeight="SemiBold"/>
                        <TextBlock Grid.Column="5" Text="修改时间" FontWeight="SemiBold"/>
                        <TextBlock Grid.Column="6" Text="模拟器配置" FontWeight="SemiBold"/>
                        <TextBlock Grid.Column="7" Text="操作" FontWeight="SemiBold"/>
                    </Grid>
                    
                    <!-- 模拟器列表 -->
                    <ListView x:Name="SimulatorListView" SelectionMode="None">
                        <ListView.ItemTemplate>
                            <DataTemplate>
                                <Grid Padding="10,8" BorderBrush="{ThemeResource DividerStrokeColorDefaultBrush}" BorderThickness="0,0,0,1">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="50"/>
                                        <ColumnDefinition Width="200"/>
                                        <ColumnDefinition Width="200"/>
                                        <ColumnDefinition Width="100"/>
                                        <ColumnDefinition Width="150"/>
                                        <ColumnDefinition Width="150"/>
                                        <ColumnDefinition Width="120"/>
                                        <ColumnDefinition Width="200"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="{Binding Id}" VerticalAlignment="Center"/>
                                    <TextBlock Grid.Column="1" Text="{Binding Title}" VerticalAlignment="Center"/>
                                    <TextBlock Grid.Column="2" Text="{Binding MarikName}" VerticalAlignment="Center"/>
                                    <TextBlock Grid.Column="3" Text="{Binding StatusText}" VerticalAlignment="Center"/>
                                    <TextBlock Grid.Column="4" Text="{Binding CreatedAt, Converter={StaticResource DateTimeToStringConverter}}" VerticalAlignment="Center"/>
                                    <TextBlock Grid.Column="5" Text="{Binding UpdatedAt, Converter={StaticResource DateTimeToStringConverter}}" VerticalAlignment="Center"/>
                                    <StackPanel Grid.Column="6" Orientation="Horizontal" VerticalAlignment="Center">
                                        <TextBlock Text="无"/>
                                        <Button Content="配置" 
                                                Click="ConfigSimulator_Click" 
                                                Tag="{Binding Id}" 
                                                FontSize="12" 
                                                Padding="8,4" Visibility="Collapsed"/>
                                    </StackPanel>
                                    <StackPanel Grid.Column="7" Orientation="Horizontal" VerticalAlignment="Center">
                                        <Button Content="编辑" Click="EditSimulator_Click" Tag="{Binding Id}" Margin="0,0,5,0" FontSize="12" Padding="8,4"/>
                                        <Button Content="删除" Click="DeleteSimulator_Click" Tag="{Binding Id}" FontSize="12" Padding="8,4" 
                                                Background="{ThemeResource SystemFillColorCriticalBrush}" Visibility="Collapsed"/>
                                    </StackPanel>
                                </Grid>
                            </DataTemplate>
                        </ListView.ItemTemplate>
                    </ListView>
                </StackPanel>
            </ScrollViewer>
        </Border>

        <!-- 底部用户ID测试区域 -->
        <Border Grid.Row="2"
                Background="{ThemeResource CardBackgroundFillColorDefaultBrush}"
                BorderBrush="{ThemeResource CardStrokeColorDefaultBrush}"
                BorderThickness="1"
                CornerRadius="8"
                Padding="15"
                Visibility="Collapsed">
            <StackPanel>
                <TextBlock Text="根据用户ID测试模拟器" FontWeight="SemiBold" Margin="0,0,0,10"/>
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <TextBox x:Name="UserIdTextBox" PlaceholderText="输入用户ID" Grid.Column="0" Margin="0,0,10,0"/>
                    <Button x:Name="CheckSimulatorButton" Content="检测模拟器" Grid.Column="1" Margin="0,0,10,0" Click="CheckUserSimulator_Click"/>
                    <Button x:Name="CreateSimulatorButton" Content="创建模拟器" Grid.Column="2" Click="CreateUserSimulator_Click"/>
                </Grid>
                
                <TextBlock x:Name="SimulatorStatusText" Margin="0,10,0,0" Visibility="Collapsed"/>
            </StackPanel>
        </Border>

        <!-- 新增/编辑弹窗 -->
        <ContentDialog x:Name="SimulatorDialog" Title="模拟器信息" PrimaryButtonText="保存" SecondaryButtonText="取消" PrimaryButtonClick="SimulatorDialog_PrimaryButtonClick" SecondaryButtonClick="SimulatorDialog_SecondaryButtonClick" >
            <StackPanel>
                <TextBox x:Name="NameTextBox" Header="名字" Margin="0,0,0,10"/>
                <ComboBox x:Name="StatusComboBox" Header="状态" Margin="0,0,0,10">
                    <ComboBoxItem Content="启用" Tag="1"/>
                    <ComboBoxItem Content="关闭" Tag="2"/>
                </ComboBox>
                <!-- 类型和配置内容字段已隐藏，因为这些是单独操作 -->
                <ComboBox x:Name="TypeComboBox" Header="类型" Margin="0,0,0,10" Visibility="Collapsed">
                    <ComboBoxItem Content="leidian"/>
                </ComboBox>
                <TextBlock x:Name="SimulatorConfigText" Margin="0,10,0,0"/>
                
                <TextBox x:Name="ConfigTextBox" Header="配置内容(JSON)" Margin="0,0,0,10" Visibility="Collapsed"/>
            </StackPanel>
        </ContentDialog>

        <!-- 配置弹窗 -->
        <ContentDialog x:Name="ConfigDialog" Title="模拟器配置" PrimaryButtonText="保存" SecondaryButtonText="取消" PrimaryButtonClick="ConfigDialog_PrimaryButtonClick" SecondaryButtonClick="ConfigDialog_SecondaryButtonClick">
            <ScrollViewer>
                <StackPanel Width="600">
                    <!-- 性能设置 -->
                    <TextBlock Text="性能设置" FontWeight="Bold" Margin="0,0,0,10"/>
                    <Grid Margin="0,0,0,15">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        
                        <TextBox x:Name="ResolutionWidthTextBox" Header="宽度" Grid.Row="0" Grid.Column="0" Margin="0,0,5,5"/>
                        <TextBox x:Name="ResolutionHeightTextBox" Header="高度" Grid.Row="0" Grid.Column="1" Margin="5,0,5,5"/>
                        <TextBox x:Name="ResolutionDpiTextBox" Header="DPI" Grid.Row="0" Grid.Column="2" Margin="5,0,0,5"/>
                        
                        <ComboBox x:Name="CpuComboBox" Header="CPU核心数" Grid.Row="1" Grid.Column="0" Margin="0,0,5,5">
                            <ComboBoxItem Content="1" Tag="1"/>
                            <ComboBoxItem Content="2" Tag="2"/>
                            <ComboBoxItem Content="3" Tag="3"/>
                            <ComboBoxItem Content="4" Tag="4"/>
                        </ComboBox>
                        
                        <ComboBox x:Name="MemoryComboBox" Header="内存(MB)" Grid.Row="1" Grid.Column="1" Margin="5,0,5,5">
                            <ComboBoxItem Content="256" Tag="256"/>
                            <ComboBoxItem Content="512" Tag="512"/>
                            <ComboBoxItem Content="768" Tag="768"/>
                            <ComboBoxItem Content="1024" Tag="1024"/>
                            <ComboBoxItem Content="1536" Tag="1536"/>
                            <ComboBoxItem Content="2048" Tag="2048"/>
                            <ComboBoxItem Content="4096" Tag="4096"/>
                            <ComboBoxItem Content="8192" Tag="8192"/>
                        </ComboBox>
                    </Grid>

                    <!-- 设备信息设置 -->
                    <TextBlock Text="设备信息设置" FontWeight="Bold" Margin="0,15,0,10" Visibility="Collapsed"/>
                    <Grid Margin="0,0,0,15" Visibility="Collapsed">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        
                        <TextBox x:Name="ManufacturerTextBox" Header="制造商" Grid.Row="0" Grid.Column="0" Margin="0,0,5,5" PlaceholderText="如: asus"/>
                        <TextBox x:Name="ModelTextBox" Header="型号" Grid.Row="0" Grid.Column="1" Margin="5,0,0,5" PlaceholderText="如: ASUS_Z00DUO"/>
                        
                        <TextBox x:Name="PnumberTextBox" Header="手机号" Grid.Row="1" Grid.Column="0" Margin="0,0,5,5" PlaceholderText="如: 13800000000"/>
                        <TextBox x:Name="ImeiTextBox" Header="IMEI" Grid.Row="1" Grid.Column="1" Margin="5,0,0,5" PlaceholderText="输入auto或15位数字"/>
                        
                        <TextBox x:Name="ImsiTextBox" Header="IMSI" Grid.Row="2" Grid.Column="0" Margin="0,0,5,5" PlaceholderText="输入auto或15位数字"/>
                        <TextBox x:Name="AndroidIdTextBox" Header="Android ID" Grid.Row="2" Grid.Column="1" Margin="5,0,0,5" PlaceholderText="输入auto或16位字符"/>
                    </Grid>

                    <!-- 其他高级设置 -->
                    <TextBlock Text="其他高级设置" FontWeight="Bold" Margin="0,15,0,10" Visibility="Collapsed"/>
                    <Grid Margin="0,0,0,15" Visibility="Collapsed">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        
                        <TextBox x:Name="SimSerialTextBox" Header="SIM卡序列号" Grid.Row="0" Grid.Column="0" Margin="0,0,5,5" PlaceholderText="输入auto或20位数字"/>
                        <TextBox x:Name="MacTextBox" Header="MAC地址" Grid.Row="0" Grid.Column="1" Margin="5,0,0,5" PlaceholderText="输入auto或MAC地址"/>
                    </Grid>

                    <!-- 功能开关设置 -->
                    <TextBlock Text="功能开关设置" FontWeight="Bold" Margin="0,15,0,10"/>
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                        <CheckBox x:Name="AutorotateCheckBox" Content="自动旋转" Margin="0,0,20,0"/>
                        <CheckBox x:Name="LockWindowCheckBox" Content="锁定窗口" Margin="0,0,20,0"/>
                        <CheckBox x:Name="RootCheckBox" Content="ROOT权限" Margin="0,0,0,0"/>
                    </StackPanel>
                </StackPanel>
            </ScrollViewer>
        </ContentDialog>

        <!-- 模拟器创建设置弹窗 -->
        <ContentDialog x:Name="DefaultsDialog" Title="模拟器创建设置" PrimaryButtonText="保存" SecondaryButtonText="取消" PrimaryButtonClick="DefaultsDialog_PrimaryButtonClick" SecondaryButtonClick="DefaultsDialog_SecondaryButtonClick">
            <ScrollViewer>
                <StackPanel Width="600">
                    <!-- 性能设置 -->
                    <TextBlock Text="性能设置" FontWeight="Bold" Margin="0,0,0,10"/>
                    <Grid Margin="0,0,0,15">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        
                        <TextBox x:Name="DefaultResolutionWidthTextBox" Header="宽度" Grid.Row="0" Grid.Column="0" Margin="0,0,5,5" PlaceholderText="900"/>
                        <TextBox x:Name="DefaultResolutionHeightTextBox" Header="高度" Grid.Row="0" Grid.Column="1" Margin="5,0,5,5" PlaceholderText="1600"/>
                        <TextBox x:Name="DefaultResolutionDpiTextBox" Header="DPI" Grid.Row="0" Grid.Column="2" Margin="5,0,0,5" PlaceholderText="320"/>
                        
                        <ComboBox x:Name="DefaultCpuComboBox" Header="CPU核心数" Grid.Row="1" Grid.Column="0" Margin="0,0,5,5">
                            <ComboBoxItem Content="1" Tag="1"/>
                            <ComboBoxItem Content="2" Tag="2"/>
                            <ComboBoxItem Content="3" Tag="3"/>
                            <ComboBoxItem Content="4" Tag="4"/>
                        </ComboBox>
                        
                        <ComboBox x:Name="DefaultMemoryComboBox" Header="内存(MB)" Grid.Row="1" Grid.Column="1" Margin="5,0,5,5">
                            <ComboBoxItem Content="256" Tag="256"/>
                            <ComboBoxItem Content="512" Tag="512"/>
                            <ComboBoxItem Content="768" Tag="768"/>
                            <ComboBoxItem Content="1024" Tag="1024"/>
                            <ComboBoxItem Content="1536" Tag="1536"/>
                            <ComboBoxItem Content="2048" Tag="2048"/>
                            <ComboBoxItem Content="4096" Tag="4096"/>
                            <ComboBoxItem Content="8192" Tag="8192"/>
                        </ComboBox>
                    </Grid>

                    <!-- 游戏设置 -->
                    <TextBlock Text="游戏设置" FontWeight="Bold" Margin="0,15,0,10"/>
                    <Grid Margin="0,0,0,15">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        
                        <Slider x:Name="DefaultFpsSlider" Header="帧率 (FPS)" Grid.Column="0" 
                                Minimum="0" Maximum="60" Value="60" 
                                TickFrequency="10" TickPlacement="BottomRight"
                                Margin="0,0,0,5"/>
                    </Grid>

                    <!-- 其他设置 -->
                    <TextBlock Text="其他设置" FontWeight="Bold" Margin="0,15,0,10"/>
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,15">
                        <CheckBox x:Name="DefaultAutorotateCheckBox" Content="自动旋转" Margin="0,0,20,0"/>
                        <CheckBox x:Name="DefaultLockWindowCheckBox" Content="锁定窗口" Margin="0,0,20,0"/>
                        <CheckBox x:Name="DefaultRootCheckBox" Content="ROOT权限" Margin="0,0,0,0"/>
                    </StackPanel>

                    <!-- 操作按钮 -->
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,10,0,0">
                        <Button x:Name="ResetDefaultsButton" Content="重置为系统默认" Click="ResetDefaultsButton_Click" 
                                Background="{ThemeResource SystemFillColorCautionBrush}" 
                                Margin="0,0,10,0"/>
                        <TextBlock Text="当前配置会立即应用到新建的模拟器" VerticalAlignment="Center" 
                                   Foreground="{ThemeResource SystemFillColorSolidNeutralBrush}" 
                                   FontSize="12"/>
                    </StackPanel>
                </StackPanel>
            </ScrollViewer>
        </ContentDialog>
    </Grid>
</Page>
