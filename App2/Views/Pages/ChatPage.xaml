<?xml version="1.0" encoding="utf-8"?>
<Page
    x:Class="App2.Views.Pages.ChatPage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:local="using:App2.Views.Pages"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    mc:Ignorable="d"
    Background="{ThemeResource ApplicationPageBackgroundThemeBrush}">

    <Grid Padding="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- 页面标题 -->
        <TextBlock Grid.Row="0" 
                  Text="聊天消息" 
                  FontSize="24" 
                  FontWeight="SemiBold"
                  Margin="0,0,0,20"/>

        <!-- 聊天界面 -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="280"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- 聊天列表 -->
            <Border Grid.Column="0" 
                   Background="{ThemeResource CardBackgroundFillColorDefaultBrush}"
                   BorderBrush="{ThemeResource CardStrokeColorDefaultBrush}"
                   BorderThickness="1"
                   CornerRadius="8"
                   Margin="0,0,10,0">
                
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    
                    <!-- 搜索框 -->
                    <AutoSuggestBox Grid.Row="0"
                                   PlaceholderText="搜索联系人"
                                   Margin="12"
                                   QueryIcon="Find"/>
                    
                    <!-- 联系人列表 -->
                    <ListView Grid.Row="1"
                             x:Name="ContactsListView"
                             SelectionMode="Single"
                             IsItemClickEnabled="True">
                        <ListView.ItemContainerStyle>
                            <Style TargetType="ListViewItem">
                                <Setter Property="HorizontalContentAlignment" Value="Stretch"/>
                                <Setter Property="Padding" Value="12"/>
                            </Style>
                        </ListView.ItemContainerStyle>
                        
                        <ListView.ItemTemplate>
                            <DataTemplate>
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    
                                    <PersonPicture Grid.Column="0"
                                                  Width="40"
                                                  Height="40"
                                                  Margin="0,0,12,0"/>
                                    
                                    <StackPanel Grid.Column="1">
                                        <TextBlock Text="用户名称"
                                                  FontWeight="SemiBold"/>
                                        <TextBlock Text="最近消息内容"
                                                  Opacity="0.7"
                                                  TextTrimming="CharacterEllipsis"/>
                                    </StackPanel>
                                </Grid>
                            </DataTemplate>
                        </ListView.ItemTemplate>
                        
                        <ListViewItem/>
                        <ListViewItem/>
                        <ListViewItem/>
                        <ListViewItem/>
                    </ListView>
                </Grid>
            </Border>
            
            <!-- 聊天内容 -->
            <Border Grid.Column="1"
                   Background="{ThemeResource CardBackgroundFillColorDefaultBrush}"
                   BorderBrush="{ThemeResource CardStrokeColorDefaultBrush}"
                   BorderThickness="1"
                   CornerRadius="8"
                   Margin="10,0,0,0">
                
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    
                    <!-- 聊天标题 -->
                    <Grid Grid.Row="0"
                          Padding="16"
                          BorderBrush="{ThemeResource CardStrokeColorDefaultBrush}"
                          BorderThickness="0,0,0,1">
                        <TextBlock Text="聊天对象名称"
                                  FontSize="16"
                                  FontWeight="SemiBold"/>
                    </Grid>
                    
                    <!-- 消息列表 -->
                    <ScrollViewer Grid.Row="1"
                                 VerticalScrollBarVisibility="Auto">
                        <ItemsControl x:Name="MessagesItemsControl"
                                     Margin="16">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <Grid Margin="0,8">
                                        <!-- 示例消息气泡 -->
                                        <Border Background="{ThemeResource AccentFillColorDefaultBrush}"
                                               CornerRadius="8"
                                               Padding="12,8"
                                               HorizontalAlignment="Right"
                                               MaxWidth="400">
                                            <TextBlock Text="这是一条示例消息内容"
                                                      Foreground="White"
                                                      TextWrapping="Wrap"/>
                                        </Border>
                                    </Grid>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                            
                            <Grid/>
                            <Grid/>
                            <Grid/>
                        </ItemsControl>
                    </ScrollViewer>
                    
                    <!-- 输入区域 -->
                    <Grid Grid.Row="2"
                          Padding="16"
                          BorderBrush="{ThemeResource CardStrokeColorDefaultBrush}"
                          BorderThickness="0,1,0,0">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        
                        <TextBox Grid.Column="0"
                                PlaceholderText="输入消息..."
                                TextWrapping="Wrap"
                                AcceptsReturn="True"
                                MinHeight="40"
                                MaxHeight="120"
                                Margin="0,0,8,0"/>
                        
                        <Button Grid.Column="1"
                               Content="发送"
                               Style="{StaticResource AccentButtonStyle}"/>
                    </Grid>
                </Grid>
            </Border>
        </Grid>
    </Grid>
</Page>
