
<Page
    x:Class="App2.Views.Pages.ConfigPage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:local="using:App2.Views.Pages"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:controls="using:Microsoft.UI.Xaml.Controls"
    mc:Ignorable="d"
    Background="{ThemeResource ApplicationPageBackgroundThemeBrush}">

    <Grid Padding="20" x:Name="RootGrid">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
     
        <!-- 操作栏 -->
        <Grid Grid.Row="1" Margin="0,0,0,16">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <!-- 左侧筛选器 -->
            <StackPanel
                Grid.Column="0" 
                       Orientation="Horizontal" 
                       Spacing="8">
                <ComboBox PlaceholderText="Socket" 
                         Width="150"
                         SelectedIndex="0">
                    <ComboBoxItem Content="Socket" />
                </ComboBox>
                
                <ComboBox PlaceholderText="代理状态" 
                         Width="150"
                         SelectedIndex="0"
                       SelectionChanged ="ComboBox_SelectionChanged"  >
                    <ComboBoxItem Content="所有状态"/>
                    <ComboBoxItem Content="可用"/>
                    <ComboBoxItem Content="不可用"/>
                </ComboBox>
            </StackPanel>

            <!-- 右侧按钮 -->
            <StackPanel Grid.Column="2" 
                       Orientation="Horizontal" 
                       Spacing="8">
                <Button Content="添加代理"
                       Background="{ThemeResource AccentFillColorDefaultBrush}"
                       Click="AddButton_Click"
                       Foreground="White">
                    
                </Button>
                <Button Content="批量导入"
                       Background="{ThemeResource AccentFillColorDefaultBrush}"
                        Click="InputProxy_Click"
                       Foreground="White">
                </Button>
                <Button Content="测试全部"
                       Background="{ThemeResource AccentFillColorDefaultBrush}"
                       Click="TestAllButton_Click"   
                       Foreground="White">
                </Button>
            </StackPanel>
        </Grid>

        <!-- 代理列表 -->
        <Grid Grid.Row="2">
            <Border Background="{ThemeResource CardBackgroundFillColorDefaultBrush}"
                   BorderBrush="{ThemeResource CardStrokeColorDefaultBrush}"
                   BorderThickness="1"
                   CornerRadius="8">
                
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    
                    <!-- 表头 -->
                    <Grid Grid.Row="0" 
                          Padding="16,12" 
                          Background="{ThemeResource CardBackgroundFillColorSecondaryBrush}">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="48"/>
                            <ColumnDefinition Width="5*"/>
                            <ColumnDefinition Width="1*"/>
                            <ColumnDefinition Width="2*"/>
                            <ColumnDefinition Width="1*"/>
                            <ColumnDefinition Width="1*"/>
                            <ColumnDefinition Width="2*"/>
                            <ColumnDefinition Width="2*"/>
                        </Grid.ColumnDefinitions>

                        <!--<CheckBox Grid.Column="0" IsChecked="False"/>-->    
                            <TextBlock Grid.Column="1" Text="IP地址" VerticalAlignment="Center" FontWeight="SemiBold"/>
                        <TextBlock Grid.Column="4" Text="类型" VerticalAlignment="Center" FontWeight="SemiBold"/>
                        <TextBlock Grid.Column="5" Text="状态" VerticalAlignment="Center" FontWeight="SemiBold"/>
                        <TextBlock Grid.Column="6" Text="最后测试" VerticalAlignment="Center" FontWeight="SemiBold"/>
                        <TextBlock Grid.Column="7" Text="操作" VerticalAlignment="Center" FontWeight="SemiBold"/>
                    </Grid>
                    
                    <!-- 表格内容 -->
                    <ListView Grid.Row="1" 
                             x:Name="ProxiesListView"
                             SelectionMode="Multiple"
                             IsItemClickEnabled="True">
                        <ListView.ItemContainerStyle>
                            <Style TargetType="ListViewItem">
                                <Setter Property="HorizontalContentAlignment" Value="Stretch"/>
                                <Setter Property="Padding" Value="0"/>
                                <Setter Property="MinHeight" Value="0"/>
                            </Style>
                        </ListView.ItemContainerStyle>
                        
                        <ListView.ItemTemplate>
                            <DataTemplate >
                                <Grid Padding="16,12" BorderBrush="{ThemeResource CardStrokeColorDefaultBrush}" BorderThickness="0,0,0,1">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="48"/>
                                        <ColumnDefinition Width="5*"/>
                                        <ColumnDefinition Width="1*"/>
                                        <ColumnDefinition Width="2*"/>
                                        <ColumnDefinition Width="1*"/>
                                        <ColumnDefinition Width="1*"/>
                                        <ColumnDefinition Width="2*"/>
                                        <ColumnDefinition Width="2*"/>
                                    </Grid.ColumnDefinitions>

                                    <!--<CheckBox Grid.Column="0" IsChecked="False"/>-->

                                    <TextBlock Grid.Column="1" Text="{Binding FullProxyInfo}" VerticalAlignment="Center"/>
                                    <TextBlock Grid.Column="4" Text="{Binding Type}" VerticalAlignment="Center"/>
                                    <TextBlock Grid.Column="5" Text="{Binding Status}" Foreground="#2ECC71" VerticalAlignment="Center"/>
                                    <TextBlock Grid.Column="6" Text="{Binding LastUpdated}" VerticalAlignment="Center"/>
                                    
                                    <StackPanel Grid.Column="7" Orientation="Horizontal" Spacing="8">
                                        <Button Content="测试" Style="{StaticResource DefaultButtonStyle}" Tag="{Binding}" Click="TestOneButton_Click"/>
                                        <Button Content="删除" Background="#E74C3C" Foreground="White"  Tag="{Binding}" Click="DeleteButton_Click"/>
                                    </StackPanel>
                                </Grid>
                            </DataTemplate>
                        </ListView.ItemTemplate>
                        
                        <ListViewItem/>
                        <ListViewItem/>
                        <ListViewItem/>
                    </ListView>
                </Grid>
            </Border>
        </Grid>
        <Grid Grid.Row="3" x:Name="MaskLayer"
               Visibility="Collapsed"
               Background="#80000000" 
                Opacity="0.7">
            <Border Width="300" Height="100" Background="White">
                <TextBlock Text="       代理测试中.....     " VerticalAlignment="Center"/>
            </Border>
        </Grid>
    </Grid>
</Page>
