<?xml version="1.0" encoding="utf-8"?>
<Page
    x:Class="App2.Views.Pages.UserGroupPage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:local="using:App2.Views.Pages"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    mc:Ignorable="d"
    Background="{ThemeResource ApplicationPageBackgroundThemeBrush}">

    <Grid Padding="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 操作栏 -->
        <Grid Grid.Row="0" Margin="0,0,0,16">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <!-- 左侧筛选器 -->
            <ComboBox Grid.Column="0" 
                     PlaceholderText="社交平台" 
                     Width="120"
                     Margin="0,0,8,0"
                     x:Name="PlatformComboBox">
                <ComboBoxItem Content="社交平台"/>
                <ComboBoxItem Content="X"/>
                <ComboBoxItem Content="Facebook"/>
                <ComboBoxItem Content="TikTok"/>
                <ComboBoxItem Content="Instagram"/>
            </ComboBox>
            
            <ComboBox Grid.Column="1" 
                     PlaceholderText="任务来源" 
                     Width="120"
                     Margin="0,0,8,0"
                     x:Name="TaskSourceComboBox">
                <ComboBoxItem Content="任务来源"/>
                <ComboBoxItem Content="关注私信" Tag="关注私信"/>
                <ComboBoxItem Content="关注" Tag="关注"/>
                <ComboBoxItem Content="私信" Tag="私信"/>
                <ComboBoxItem Content="未知" Tag="未知"/>
            </ComboBox>
            
            <!-- 搜索框 -->
            <StackPanel Grid.Column="2" Orientation="Horizontal" Spacing="8">
                <AutoSuggestBox Width="240" 
                               PlaceholderText="用户昵称" 
                               QueryIcon="Find"
                               x:Name="UserNameSearchBox"/>
                
                <AutoSuggestBox Width="240" 
                               PlaceholderText="输入标签关键词搜索" 
                               QueryIcon="Find"
                               x:Name="KeywordSearchBox"/>
            </StackPanel>
            
            <!-- 批量删除按钮 -->
            <Button Grid.Column="3" 
                   Content="批量删除" 
                   Background="#E74C3C" 
                   Foreground="White"
                   CornerRadius="4"
                   Padding="12,8"
                   x:Name="BatchDeleteButton"/>

        </Grid>

        <!-- 用户列表 -->
        <Grid Grid.Row="1">
            <ListView x:Name="UserListView"
                     SelectionMode="None"
                     IsItemClickEnabled="False">
                <ListView.ItemContainerStyle>
                    <Style TargetType="ListViewItem">
                        <Setter Property="HorizontalContentAlignment" Value="Stretch"/>
                        <Setter Property="Padding" Value="0"/>
                        <Setter Property="MinHeight" Value="0"/>
                        <Setter Property="BorderThickness" Value="0,0,0,1"/>
                        <Setter Property="BorderBrush" Value="{ThemeResource CardStrokeColorDefaultBrush}"/>
                    </Style>
                </ListView.ItemContainerStyle>
                
                <ListView.Header>
                    <Grid Padding="12,8" Background="#F8F9FA">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="40"/>
                            <ColumnDefinition Width="100"/>
                            <ColumnDefinition Width="120"/>
                            <ColumnDefinition Width="120"/>
                            <ColumnDefinition Width="120"/>
                            <ColumnDefinition Width="80"/>
                            <ColumnDefinition Width="120"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="80"/>
                        </Grid.ColumnDefinitions>
                        
                        <CheckBox Grid.Column="0" 
                                 IsChecked="False" 
                                 x:Name="SelectAllCheckBox"
                                 Checked="SelectAllCheckBox_Checked"
                                 Unchecked="SelectAllCheckBox_Unchecked"/>
                        <TextBlock Grid.Column="1" Text="昵称" VerticalAlignment="Center" FontWeight="SemiBold"/>
                        <TextBlock Grid.Column="2" Text="用户ID" VerticalAlignment="Center" FontWeight="SemiBold"/>
                        <TextBlock Grid.Column="3" Text="所属平台" VerticalAlignment="Center" FontWeight="SemiBold"/>
                        <TextBlock Grid.Column="4" Text="搜索标签来源" VerticalAlignment="Center" FontWeight="SemiBold"/>
                        <TextBlock Grid.Column="5" Text="任务ID" VerticalAlignment="Center" FontWeight="SemiBold"/>
                        <TextBlock Grid.Column="6" Text="任务来源" VerticalAlignment="Center" FontWeight="SemiBold"/>
                        <TextBlock Grid.Column="7" Text="内容" VerticalAlignment="Center" FontWeight="SemiBold"/>
                        <TextBlock Grid.Column="8" Text="操作" VerticalAlignment="Center" FontWeight="SemiBold"/>
                    </Grid>
                </ListView.Header>
                
                <ListView.ItemTemplate>
                    <DataTemplate>
                        <Grid Padding="12,8">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="40"/>
                                <ColumnDefinition Width="100"/>
                                <ColumnDefinition Width="120"/>
                                <ColumnDefinition Width="120"/>
                                <ColumnDefinition Width="120"/>
                                <ColumnDefinition Width="80"/>
                                <ColumnDefinition Width="120"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="80"/>
                            </Grid.ColumnDefinitions>
                            
                            <CheckBox Grid.Column="0" IsChecked="{Binding IsSelected, Mode=TwoWay}"/>
                            <TextBlock Grid.Column="1" Text="{Binding Nickname}" VerticalAlignment="Center"/>
                            <TextBlock Grid.Column="2" Text="{Binding UserId}" VerticalAlignment="Center"/>
                            <TextBlock Grid.Column="3" Text="{Binding Platform}" VerticalAlignment="Center"/>
                            <TextBlock Grid.Column="4" Text="{Binding Source}" VerticalAlignment="Center"/>
                            <TextBlock Grid.Column="5" Text="{Binding TaskId}" VerticalAlignment="Center"/>
                            <TextBlock Grid.Column="6" Text="{Binding TaskSource}" VerticalAlignment="Center"/>
                            <TextBlock Grid.Column="7" Text="{Binding Content}" VerticalAlignment="Center" TextTrimming="CharacterEllipsis"/>
                            <Button Grid.Column="8" Content="删除" Foreground="White" Background="#E74C3C" 
                                   CornerRadius="4" Padding="8,4" Command="{Binding DeleteCommand}"/>
                        </Grid>
                    </DataTemplate>
                </ListView.ItemTemplate>
            </ListView>

            <!-- 空状态提示 -->
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center" 
                        Visibility="{x:Bind HasNoData, Mode=OneWay}">
                <FontIcon FontFamily="Segoe UI Symbol" Glyph="&#xE14B;" FontSize="48" Foreground="Gray" HorizontalAlignment="Center"/>
                <TextBlock Text="暂无数据" FontSize="16" Foreground="Gray" HorizontalAlignment="Center" Margin="0,8,0,0"/>
                <TextBlock Text="请检查筛选条件或稍后再试" FontSize="12" Foreground="Gray" HorizontalAlignment="Center"/>
            </StackPanel>
        </Grid>

        <!-- 分页控件 -->
        <Grid Grid.Row="2" Margin="0,16,0,0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <!-- 总记录数 -->
            <TextBlock Grid.Column="0" x:Name="TotalCountText" Text="共 0 条" VerticalAlignment="Center" Margin="0,0,16,0"/>

            <!-- 页码按钮 -->
            <StackPanel Grid.Column="1" Orientation="Horizontal" HorizontalAlignment="Center" x:Name="PageNumbersPanel"/>

            <!-- 每页显示数量 -->
            <StackPanel Grid.Column="2" Orientation="Horizontal" VerticalAlignment="Center" Margin="16,0">
                <TextBlock Text="每页显示" VerticalAlignment="Center" Margin="0,0,8,0"/>
                <ComboBox x:Name="PageSizeComboBox" SelectedIndex="1" SelectionChanged="PageSizeComboBox_SelectionChanged">
                    <ComboBoxItem Content="5条" Tag="5"/>
                    <ComboBoxItem Content="10条" Tag="10"/>
                    <ComboBoxItem Content="20条" Tag="20"/>
                    <ComboBoxItem Content="50条" Tag="50"/>
                </ComboBox>
            </StackPanel>

            <!-- 分页按钮 -->
            <StackPanel Grid.Column="3" Orientation="Horizontal" Spacing="8" VerticalAlignment="Center" Margin="16,0">
                <Button x:Name="PreviousPageButton" Content="上一页" Click="PreviousPage_Click" IsEnabled="False"/>
                <Button x:Name="NextPageButton" Content="下一页" Click="NextPage_Click" IsEnabled="False"/>
            </StackPanel>
        </Grid>
    </Grid>
</Page>
