using Microsoft.UI.Xaml;
using Microsoft.UI.Xaml.Controls;
using Microsoft.UI.Xaml.Navigation;
using System;
using System.Threading.Tasks;
using App2.Services;
using System.Reflection;
using Windows.Storage;

namespace App2.Views.Pages
{
    public sealed partial class SettingsPage : Page
    {
        public SettingsPage()
        {
            this.InitializeComponent();
            LoadSettings();
        }

        protected override void OnNavigatedTo(NavigationEventArgs e)
        {
            base.OnNavigatedTo(e);
            LoadSystemInfo();
            TestDatabaseConnection();
        }

        /// <summary>
        /// 加载设置
        /// </summary>
        private void LoadSettings()
        {
            try
            {
                // 加载主题设置
                var theme = GetSetting("AppTheme", "Default");
                foreach (ComboBoxItem item in ThemeComboBox.Items)
                {
                    if (item.Tag?.ToString() == theme)
                    {
                        ThemeComboBox.SelectedItem = item;
                        break;
                    }
                }

                // 加载其他设置
                AutoStartToggle.IsOn = GetBoolSetting("AutoStart", false);
            }
            catch (Exception ex)
            {
                ShowMessage("加载设置失败", ex.Message);
            }
        }

        /// <summary>
        /// 加载系统信息
        /// </summary>
        private void LoadSystemInfo()
        {
            try
            {
                // 获取应用版本
                var version = Assembly.GetExecutingAssembly().GetName().Version;
                AppVersionText.Text = $"{version?.Major}.{version?.Minor}.{version?.Build}";

                // 获取最后登录时间
                var lastLogin = SettingsService.GetLastLoginTime();
                LastLoginText.Text = lastLogin?.ToString("yyyy-MM-dd HH:mm:ss") ?? "从未登录";
            }
            catch (Exception ex)
            {
                ShowMessage("加载系统信息失败", ex.Message);
            }
        }

        /// <summary>
        /// 测试数据库连接
        /// </summary>
        private async void TestDatabaseConnection()
        {
            try
            {
                ConnectionStatusText.Text = "检测中...";
                var isConnected = await DatabaseService.TestConnectionAsync();
                
                if (isConnected)
                {
                    ConnectionStatusText.Text = "连接正常";
                    ConnectionStatusText.Foreground = new Microsoft.UI.Xaml.Media.SolidColorBrush(Microsoft.UI.Colors.Green);
                }
                else
                {
                    ConnectionStatusText.Text = "连接失败";
                    ConnectionStatusText.Foreground = new Microsoft.UI.Xaml.Media.SolidColorBrush(Microsoft.UI.Colors.Red);
                }
            }
            catch (Exception ex)
            {
                ConnectionStatusText.Text = $"连接错误: {ex.Message}";
                ConnectionStatusText.Foreground = new Microsoft.UI.Xaml.Media.SolidColorBrush(Microsoft.UI.Colors.Red);
            }
        }

        #region 事件处理

        /// <summary>
        /// 主题切换
        /// </summary>
        private void ThemeComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (ThemeComboBox.SelectedItem is ComboBoxItem item)
            {
                var theme = item.Tag?.ToString() ?? "Default";
                SaveSetting("AppTheme", theme);
                
                // 应用主题
                ApplyTheme(theme);
                ShowMessage("主题设置", "主题已更改，重启应用后完全生效");
            }
        }

        /// <summary>
        /// 自启动切换
        /// </summary>
        private void AutoStartToggle_Toggled(object sender, RoutedEventArgs e)
        {
            SaveBoolSetting("AutoStart", AutoStartToggle.IsOn);
            ShowMessage("启动设置", AutoStartToggle.IsOn ? "已启用开机自启动" : "已禁用开机自启动");
        }

        /// <summary>
        /// 测试数据库连接
        /// </summary>
        private async void TestConnectionButton_Click(object sender, RoutedEventArgs e)
        {
            TestConnectionButton.IsEnabled = false;
            TestDatabaseConnection();
            await Task.Delay(1000);
            TestConnectionButton.IsEnabled = true;
        }

        /// <summary>
        /// 清理数据
        /// </summary>
        private async void CleanDataButton_Click(object sender, RoutedEventArgs e)
        {
            var dialog = new ContentDialog
            {
                Title = "确认清理",
                Content = "确定要清理过期的会话和日志数据吗？此操作不可撤销。",
                PrimaryButtonText = "确定",
                CloseButtonText = "取消",
                DefaultButton = ContentDialogButton.Close,
                XamlRoot = this.XamlRoot
            };

            var result = await dialog.ShowAsync();
            if (result == ContentDialogResult.Primary)
            {
                try
                {
                    CleanDataButton.IsEnabled = false;
                    
                    var sessionCount = await DataCleanupService.CleanExpiredSessionsAsync();
                    var logCount = await DataCleanupService.CleanOldLoginLogsAsync();
                    
                    ShowMessage("数据清理", $"清理完成：{sessionCount} 个过期会话，{logCount} 条旧日志");
                }
                catch (Exception ex)
                {
                    ShowMessage("清理失败", ex.Message);
                }
                finally
                {
                    CleanDataButton.IsEnabled = true;
                }
            }
        }

        /// <summary>
        /// 重置应用
        /// </summary>
        private async void ResetAppButton_Click(object sender, RoutedEventArgs e)
        {
            var dialog = new ContentDialog
            {
                Title = "确认重置",
                Content = "重置应用将清除所有本地设置和缓存，需要重新配置。确定继续吗？",
                PrimaryButtonText = "确定",
                CloseButtonText = "取消",
                DefaultButton = ContentDialogButton.Close,
                XamlRoot = this.XamlRoot
            };

            var result = await dialog.ShowAsync();
            if (result == ContentDialogResult.Primary)
            {
                try
                {
                    SettingsService.ClearAllSettings();
                    await SessionService.ClearSessionAsync();
                    ShowMessage("重置完成", "应用设置已重置，建议重启应用");
                }
                catch (Exception ex)
                {
                    ShowMessage("重置失败", ex.Message);
                }
            }
        }

        #endregion

        #region 辅助方法

        /// <summary>
        /// 应用主题
        /// </summary>
        private void ApplyTheme(string theme)
        {
            try
            {
                ElementTheme elementTheme = theme switch
                {
                    "Light" => ElementTheme.Light,
                    "Dark" => ElementTheme.Dark,
                    _ => ElementTheme.Default
                };

                var window = (Application.Current as App)?.MainWindow;
                if (window?.Content is FrameworkElement rootElement)
                {
                    rootElement.RequestedTheme = elementTheme;
                }
            }
            catch
            {
                // 主题应用失败不影响主流程
            }
        }

        /// <summary>
        /// 显示消息
        /// </summary>
        private async void ShowMessage(string title, string message)
        {
            try
            {
                var dialog = new ContentDialog
                {
                    Title = title,
                    Content = message,
                    CloseButtonText = "确定",
                    XamlRoot = this.XamlRoot
                };
                
                await dialog.ShowAsync();
            }
            catch
            {
                // 显示消息失败不影响主流程
            }
        }

        /// <summary>
        /// 获取设置值
        /// </summary>
        private static string GetSetting(string key, string defaultValue)
        {
            try
            {
                // 使用SettingsService中对应的方法
                return key switch
                {
                    "AppTheme" => SettingsService.GetAppTheme(),
                    _ => defaultValue
                };
            }
            catch
            {
                return defaultValue;
            }
        }

        /// <summary>
        /// 保存设置值
        /// </summary>
        private static void SaveSetting(string key, string value)
        {
            try
            {
                // 使用SettingsService中对应的方法
                switch (key)
                {
                    case "AppTheme":
                        SettingsService.SetAppTheme(value);
                        break;
                }
            }
            catch
            {
                // 保存失败不影响主流程
            }
        }

        /// <summary>
        /// 获取布尔设置值
        /// </summary>
        private static bool GetBoolSetting(string key, bool defaultValue)
        {
            try
            {
                // 使用SettingsService中对应的方法
                return key switch
                {
                    "AutoStart" => SettingsService.GetAutoStart(),
                    _ => defaultValue
                };
            }
            catch
            {
                return defaultValue;
            }
        }

        /// <summary>
        /// 保存布尔设置值
        /// </summary>
        private static void SaveBoolSetting(string key, bool value)
        {
            try
            {
                // 使用SettingsService中对应的方法
                switch (key)
                {
                    case "AutoStart":
                        SettingsService.SetAutoStart(value);
                        break;
                }
            }
            catch
            {
                // 保存失败不影响主流程
            }
        }

        #endregion
    }
}