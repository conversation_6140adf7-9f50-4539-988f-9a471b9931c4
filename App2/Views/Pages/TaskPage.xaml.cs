using App2.Helpers;
using App2.Models;
using App2.Services;
using Google.Protobuf.Reflection;
using Microsoft.UI.Xaml;
using Microsoft.UI.Xaml.Controls;
using MySqlX.XDevAPI.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using static App2.Models.DataItem;

namespace App2.Views.Pages
{
    public sealed partial class TaskPage : Page
    {
        private readonly RemoteDatabaseService _remoteDbService = new RemoteDatabaseService();
        private readonly ProxySocketTest _socket = new ProxySocketTest();
        // 分页相关字段
        private int _currentPage = 1;
        private int _pageSize = 20;
        private int _totalCount = 0;
        private int _totalPages = 1;

        private string _keywordsOriginalText;
        private string _keywordsTranslatedText;
        private string _contentOriginalText;
        private string _contentTranslatedText;

        // 搜索参数缓存
        private int? _searchTid;
        private string _searchPlatform;
        private string _searchStatus;
        private string _searchKeywords;
        private int? _searchId;

        // 分页信息属性
        public string PageInfoText => $"第 {_currentPage} 页 / 共 {_totalPages} 页";
        public string TotalCountText => $"总条数：{_totalCount}";
        public bool IsPrevEnabled => _currentPage > 1;
        public bool IsNextEnabled => _currentPage < _totalPages;

        // 用于绑定任务类型列表
        private List<TaskType> _taskTypes = new();
        private List<TaskTypeRadio> _typeList = new();
        private List<AccountSource> _platformList = new();
        private AccountSource _selectedPlatform = AccountSource.Instagram;

        private int _selectedTaskTypeId;
        private string _searchUrl = "帖子下留言的用户"; // 默认值

        // 支持的目标语种
        public class LanguageItem
        {
            public string Name { get; set; }
            public string Code { get; set; }
            public override string ToString() => Name;
        }
        private readonly List<LanguageItem> _languages = new()
        {
             new LanguageItem { Name = "英文", Code = "en" },
            new LanguageItem { Name = "中文", Code = "zh" },
            new LanguageItem { Name = "日文", Code = "jp" },
            new LanguageItem { Name = "韩文", Code = "kor" },
            new LanguageItem { Name = "法文", Code = "fra" },
            new LanguageItem { Name = "德文", Code = "de" },
            new LanguageItem { Name = "俄文", Code = "ru" },
            new LanguageItem { Name = "西班牙文", Code = "spa" },
            new LanguageItem { Name = "葡萄牙文", Code = "pt" },
            new LanguageItem { Name = "意大利文", Code = "it" },
            new LanguageItem { Name = "阿拉伯文", Code = "ara" },
        };

        private TextBox _currentTextBox; // 记录当前要回填的文本框

        public TaskPage()
        {
            InitializeComponent();
            LoadTaskTypeRadios();
            LoadPlatformRadios();
            LoadAccountsByPlatform(_selectedPlatform);

            // 初始化时绑定数据源
            BindTaskTypeComboBox();
            LoadPlatformComboBox();
            GlobalLoadingOverlay = FindName("GlobalLoadingOverlay") as Grid;
        }

        private void TaskTabView_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (sender is TabView tabView)
            {
                switch (tabView.SelectedIndex)
                {
                    case 0: // 执行任务
                        ExecuteTaskContent.Visibility = Visibility.Visible;
                        ExecuteRecordContent.Visibility = Visibility.Collapsed;
                        ExecuteConfigContent.Visibility = Visibility.Collapsed;

                        break;
                    case 1: // 执行记录
                        ExecuteTaskContent.Visibility = Visibility.Collapsed;
                        ExecuteRecordContent.Visibility = Visibility.Visible;
                        ExecuteConfigContent.Visibility = Visibility.Collapsed;
                        RefreshTaskList();
                        break;
                    case 2: // 执行参数配置
                        ExecuteTaskContent.Visibility = Visibility.Collapsed;
                        ExecuteRecordContent.Visibility = Visibility.Collapsed;
                        ExecuteConfigContent.Visibility = Visibility.Visible;

                        // 查询任务类型列表并绑定到 ListView
                        _taskTypes = TaskTypeService.GetAll();
                        TaskTypeListView.ItemsSource = _taskTypes;
                        break;
                }
            }
        }

        private async void SaveTaskType_Click(object sender, RoutedEventArgs e)
        {

            if (sender is Button btn && btn.DataContext is TaskType taskType)
            {
                // 提交参数（如保存到数据库或调用服务）
                TaskTypeService.Update(taskType);

                // 可选：弹窗提示
                var dialog = new ContentDialog
                {
                    Title = "保存成功",
                    Content = $"任务类型 {taskType.Name} 已保存。",
                    CloseButtonText = "确定",
                    XamlRoot = this.XamlRoot // WinUI3 必须设置 XamlRoot，否则会崩溃
                };
                dialog.XamlRoot = this.XamlRoot;
                dialog.RequestedTheme = ElementTheme.Default;
                await dialog.ShowAsync();
            }
        }

        private void LoadTaskTypeRadios()
        {
            _typeList = TaskTypeService.GetAllSelect();
            TaskTypeRadioPanel.Children.Clear();

            if (_typeList.Count > 0)
                _selectedTaskTypeId = _typeList[0].Id;
            foreach (var type in _typeList)
            {
                var radio = new RadioButton
                {
                    Content = type.Name,
                    Tag = type.Id,
                    GroupName = "TaskTypeGroup",
                    IsChecked = type.Id == _selectedTaskTypeId
                };
                radio.Checked += TaskTypeRadio_Checked;
                TaskTypeRadioPanel.Children.Add(radio);
            }
        }

        private void TaskTypeRadio_Checked(object sender, RoutedEventArgs e)
        {
            if (sender is RadioButton rb && rb.Tag is int id)
            {
                _selectedTaskTypeId = id;
                // 此处可同步到 tasks 表的 tid 字段
            }
        }

        private void LoadPlatformRadios()
        {
            _platformList = Enum.GetValues<AccountSource>().ToList();
            PlatformRadioPanel.Children.Clear();

            for (int i = 0; i < _platformList.Count; i++)
            {
                var source = _platformList[i];
                var radio = new RadioButton
                {
                    Content = source.ToString(),
                    Tag = source,
                    GroupName = "PlatformGroup",
                    IsChecked = i == 0 // 默认选中第一个
                };
                radio.Checked += PlatformRadio_Checked;
                PlatformRadioPanel.Children.Add(radio);
            }
        }

        private void PlatformRadio_Checked(object sender, RoutedEventArgs e)
        {
            if (sender is RadioButton rb && rb.Tag is AccountSource source)
            {
                _selectedPlatform = source;
                // 查询并刷新账号列表
                var accounts = AccountService.GetBySource(_selectedPlatform);
                AccountListView.ItemsSource = accounts;
            }
        }

        private void LoadAccountsByPlatform(AccountSource source)
        {
            var accounts = AccountService.GetBySource(source);
            AccountListView.ItemsSource = accounts;
        }

        private void SearchUrlRadio_Checked(object sender, RoutedEventArgs e)
        {
            if (sender is RadioButton rb)
                _searchUrl = rb.Content?.ToString() ?? "";
        }

        private async void ExecuteTaskButton_Click(object sender, RoutedEventArgs e)
        {
            GlobalLoadingOverlay.Visibility = Visibility.Visible;
            var dialog = new ContentDialog
            {
                Title = "任务提交",
                Content = "成功",
                CloseButtonText = "确定",
                XamlRoot = this.XamlRoot
            };

            // 获取选择任务类型
            int tid = _selectedTaskTypeId;

            // 获取平台账号
            var selectedPlatformRadio = PlatformRadioPanel.Children
                .OfType<RadioButton>()
                .FirstOrDefault(r => r.IsChecked == true);
            string platform = selectedPlatformRadio?.Content?.ToString() ?? "";

            // 获取“搜索路径”选中的值
            // 使用 _searchUrl 直接提交

            // 获取帖子关键词
            var keywordsBox = FindName("KeywordsTextBox") as TextBox;
            string keywords = keywordsBox?.Text ?? "";

            // 获取私信文案
            var contentBox = FindName("ContentTextBox") as TextBox;
            string content = contentBox?.Text ?? "";

            // 获取选中的账号ID（更简洁）
            //var selectedAccountIds = AccountListView.SelectedItems
            //    .OfType<UserAccount>()
            //    .Select(a => a.LoginAccount)
            //    .ToList();

            //var selectedAccountIds = new List<string>();
            if (AccountListView.SelectedItems.Count == 0)
            {
                dialog.Content = "请选择账号";
                await dialog.ShowAsync();
                GlobalLoadingOverlay.Visibility = Visibility.Collapsed;
                return;
            }
            var accountList = new List<ExecTaskAccount>();
            var taskExec = new TasksExec();
            foreach (var item in AccountListView.SelectedItems)
            {
                if (item is UserAccount account)
                {
                    bool proxyAvailable = false;
                    ProxyTaskData proxyData = null;

                    // 1. 判断 ProxyId 是否等于 0
                    if (account.ProxyId != 0)
                    {
                        proxyData = await _remoteDbService.GetById(account.ProxyId);
                        if (proxyData != null)
                        {
                            if (proxyData.Type?.ToLower() == "socks5")
                            {
                                proxyAvailable = await _socket.TestSocks5ProxyAvailable(proxyData.Host, proxyData.Port, proxyData.UserName, proxyData.Password, 10);
                            }
                            else if (proxyData.Type?.ToLower() == "http")
                            {
                                proxyAvailable = await _socket.TestHttpProxyAvailable(proxyData.Host, proxyData.Port, proxyData.UserName, proxyData.Password, "http://example.com/", 10);
                            }
                        }
                    }

                    // 2. 如果代理不可用 或 ProxyId == 0，遍历所有代理检测
                    if (!proxyAvailable || account.ProxyId == 0)
                    {
                        var allProxies = await _remoteDbService.GetProxyAll();
                        foreach (var proxy in allProxies)
                        {
                            bool available = false;
                            if (proxy.Type?.ToLower() == "socket")
                            {
                                available = await _socket.TestSocks5ProxyAvailable(proxy.Host, proxy.Port, proxy.UserName, proxy.Password, 10);
                            }
                            else if (proxy.Type?.ToLower() == "http")
                            {
                                available = await _socket.TestHttpProxyAvailable(proxy.Host, proxy.Port, proxy.UserName, proxy.Password, "http://example.com/", 10);
                            }
                            if (available)
                            {
                                // 找到可用代理，更新账号 ProxyId
                                account.ProxyId = proxy.Id;

                                var accountRes = await AccountService.UpdateAccountAsync(account);
                                if (!accountRes.Success)
                                {
                                    dialog.Content = accountRes.Message;
                                    await dialog.ShowAsync();
                                    GlobalLoadingOverlay.Visibility = Visibility.Collapsed;
                                    return;
                                }

                                taskExec.Proxy = proxy;
                                proxyAvailable = true;
                                break;
                            }
                        }
                    }

                    // 如果最终代理还是不可用，弹窗提示并跳过该账号
                    if (!proxyAvailable)
                    {
                        dialog.Content = $"账号 {account.LoginAccount} 未找到可用代理，请检查代理配置。";
                        await dialog.ShowAsync();
                        GlobalLoadingOverlay.Visibility = Visibility.Collapsed;
                        return;
                    }


                    string? simulatorPath = SettingsService.GetSimulatorPath();
                    var simulatorManager = new SimulatorManagerService(simulatorPath);
                    if (string.IsNullOrEmpty(simulatorPath))
                    {
                        dialog.Content = "获取模拟器路径失败";
                        await dialog.ShowAsync();
                        GlobalLoadingOverlay.Visibility = Visibility.Collapsed;
                        return;
                    }
                    ////判断模拟器是否正常或者创建模拟器
                    if (account.SimulatorId != 0)
                    {
                        var res = await simulatorManager.CheckUserSimulatorAsync(account.Id);
                        if (res.Code < 0)
                        {
                            // dialog.Content = account.LoginAccount + ": 模拟器不可用";
                            // await dialog.ShowAsync();
                            // GlobalLoadingOverlay.Visibility = Visibility.Collapsed;
                            // return;

                            var result1 = await simulatorManager.CreateUserSimulatorAsync(account.Id, false);
                            if (result1.Code < 0)
                            {
                                dialog.Content = account.LoginAccount + ": 模拟器重建失败";
                                await dialog.ShowAsync();
                                GlobalLoadingOverlay.Visibility = Visibility.Collapsed;
                                return;
                            }
                        }
                        //检测是否可用 不可用打回去

                    }
                    else
                    {
                        var res = await simulatorManager.CreateUserSimulatorAsync(account.Id);
                        if (res.Code == 200)
                        {
                            account.SimulatorId = Convert.ToInt32(res.Data);
                        }
                        else
                        {
                            //去创建模拟器
                            //创建成功写入数据库
                            dialog.Content = account.LoginAccount + "模拟器不可用";

                            await dialog.ShowAsync();
                            GlobalLoadingOverlay.Visibility = Visibility.Collapsed;
                            return;
                        }

                    }

                    taskExec.TaskType = TaskTypeService.GetById(tid);

                    taskExec.Account = new ExecTaskAccount
                    {
                        LoginAccount = account.LoginAccount,
                        Password = account.Password,
                        SimulatorId = account.SimulatorId,
                    };
                    taskExec.Platform = platform;
                    taskExec.Keywords = keywords;
                    taskExec.SearchUrl = _searchUrl;
                    taskExec.Content = content;
                    // 构造任务对象
                    var task = new Tasks
                    {
                        Tid = tid,
                        Platform = platform,
                        Uids = account.LoginAccount,
                        SearchUrl = _searchUrl,
                        Keywords = keywords,
                        Content = content,
                        CreatedAt = DateTime.Now,
                    };
                    int resultId = TasksService.AddTask(task);
                    taskExec.Id = resultId;
                    //调起模拟器脚本

                    // 使用源生成器API将 taskExec 序列化为 JSON 字符串
                    // var options = new JsonSerializerOptions { WriteIndented = true };
                    var taskExecJson = JsonSerializer.Serialize(taskExec, typeof(TasksExec), AppJsonContext.Default);
                    System.Diagnostics.Debug.WriteLine($"## TaskPage@taskExec JSON: {taskExecJson}");

                    var checkResult = await simulatorManager.InitializeTaskAsync(taskExecJson, account.Id);
                    System.Diagnostics.Debug.WriteLine($"## TaskPage@checkResult -------- 任务调用模拟器状态---- JSON: {checkResult.Message}");

                    //调起失败的话修改状态
                    if (checkResult.Code < 0)
                    {
                        dialog.Content = account.LoginAccount + "模拟器不可用";
                        await dialog.ShowAsync();
                        GlobalLoadingOverlay.Visibility = Visibility.Collapsed;
                        return;
                    }

                }
            }
            await dialog.ShowAsync();
            GlobalLoadingOverlay.Visibility = Visibility.Collapsed;
            ResetTaskPage();
            
        }

        // 重置页面并恢复默认选项
        private void ResetTaskPage()
        {
            // 选择任务单选按钮恢复默认（假设第一个为默认）
            foreach (var radio in TaskTypeRadioPanel.Children.OfType<RadioButton>())
            {
                radio.IsChecked = false;
            }
            if (TaskTypeRadioPanel.Children.Count > 0)
            {
                ((RadioButton)TaskTypeRadioPanel.Children[0]).IsChecked = true;
            }

            // 平台账号单选按钮恢复默认（假设第一个为默认）
            foreach (var radio in PlatformRadioPanel.Children.OfType<RadioButton>())
            {
                radio.IsChecked = false;
            }
            if (PlatformRadioPanel.Children.Count > 0)
            {
                ((RadioButton)PlatformRadioPanel.Children[0]).IsChecked = true;
            }

            // 搜索选择恢复默认（第一个RadioButton IsChecked=true）
            foreach (var radio in SearchUrlPanel.Children.OfType<RadioButton>())
            {
                radio.IsChecked = false;
            }
            if (SearchUrlPanel.Children.Count > 0)
            {
                ((RadioButton)SearchUrlPanel.Children[0]).IsChecked = true;
            }

            // 清空账号列表选中项
            AccountListView.SelectedItems.Clear();

            // 清空帖子关键词及私信文案
            KeywordsTextBox.Text = string.Empty;
            ContentTextBox.Text = string.Empty;
        }

        private void BindTaskTypeComboBox()
        {
            // 构造“所有类型”对象
            var allTypes = new List<TaskTypeRadio>
            {
                new TaskTypeRadio { Id = 0, Name = "所有类型" }
            };
            allTypes.AddRange(_typeList); // _typeList为你的任务类型数据源
            TaskTypeComboBox.ItemsSource = allTypes;
            TaskTypeComboBox.SelectedIndex = 0; // 默认选中“所有类型”
        }

        private void LoadPlatformComboBox()
        {
            // 构建平台下拉框数据，首项为“所有平台”
            var platformItems = new List<ComboBoxItem>
            {
                new ComboBoxItem { Content = "所有平台", Tag = null }
            };
            _platformList = Enum.GetValues<AccountSource>().ToList();
            foreach (var source in _platformList)
            {
                platformItems.Add(new ComboBoxItem
                {
                    Content = source.ToString(),
                    Tag = source
                });
            }
            // 假设 PlatformComboBox 已在 XAML 中定义
            PlatformComboBox.ItemsSource = platformItems;
            PlatformComboBox.SelectedIndex = 0; // 默认选中“所有平台”
        }

        private void SearchButton_Click(object sender, RoutedEventArgs e)
        {
            // 获取任务类型id
            int? tid = null;
            System.Diagnostics.Debug.WriteLine(TaskTypeComboBox.SelectedItem);
            if (TaskTypeComboBox.SelectedItem is TaskTypeRadio type && type.Id != 0)
                tid = type.Id; // 0 表示“所有类型”，不参与查询

            // 获取平台
            string platform = null;
            if (PlatformComboBox.SelectedItem is ComboBoxItem platformItem && platformItem.Tag != null)
                platform = platformItem.Tag.ToString();
            else if (PlatformComboBox.SelectedItem is AccountSource source)
                platform = source.ToString();

            // 获取状态
            string status = null;
            if (SearchStatus.SelectedItem is ComboBoxItem statusItem && statusItem.Tag != null && statusItem.Tag.ToString() != "0")
                status = statusItem.Tag.ToString();

            // 关键词
            string keywords = string.IsNullOrWhiteSpace(SearchKeyWords.Text) ? null : SearchKeyWords.Text.Trim();

            // 任务ID
            int? id = null;
            if (int.TryParse(SearchId.Text, out int val))
                id = val;

            _searchTid = tid;
            _searchPlatform = platform;
            _searchStatus = status;
            _searchKeywords = keywords;
            _searchId = id;

            // 重置页码
            _currentPage = 1;

            // 查询并更新列表
            RefreshTaskList();
            //var list = TasksService.QueryTasks(tid, platform, status, keywords, id, page: 1, pageSize: 20);
            //TaskRecordListView.ItemsSource = list;
        }

        private void RefreshTaskList()
        {
            // 查询数据
            var list = TasksService.QueryTasks(_searchTid, _searchPlatform, _searchStatus, _searchKeywords, _searchId, page: _currentPage, pageSize: _pageSize, out int totalCount);
            TaskRecordListView.ItemsSource = list;
            System.Diagnostics.Debug.WriteLine($"AccountPage 初始化 - {totalCount}");
            // 更新总条数和总页数
            _totalCount = totalCount;
            _totalPages = Math.Max(1, (int)Math.Ceiling(_totalCount / (double)_pageSize));

            // 刷新分页信息（如用INotifyPropertyChanged则调用OnPropertyChanged）
            // 如果没有绑定，直接刷新界面相关控件
            PageInfoTextBlock.Text = PageInfoText;
            TotalCountTextBlock.Text = TotalCountText;
            PrevButton.IsEnabled = IsPrevEnabled;
            NextButton.IsEnabled = IsNextEnabled;
        }

        private void PrevPage_Click(object sender, RoutedEventArgs e)
        {
            if (_currentPage > 1)
            {
                _currentPage--;
                RefreshTaskList();
            }
        }

        private void NextPage_Click(object sender, RoutedEventArgs e)
        {
            if (_currentPage < _totalPages)
            {
                _currentPage++;
                RefreshTaskList();
            }
        }

        private async void AccountNumTextBlock_Tapped(object sender, Microsoft.UI.Xaml.Input.TappedRoutedEventArgs e)
        {
            if (sender is TextBlock tb && tb.DataContext is TasksList task)
            {
                var dialog = new ContentDialog
                {
                    Title = "登录账号",
                    CloseButtonText = "确定",
                    XamlRoot = this.XamlRoot,
                    RequestedTheme = ElementTheme.Default,
                    Content = new ScrollViewer
                    {
                        HorizontalScrollBarVisibility = ScrollBarVisibility.Disabled,
                        VerticalScrollBarVisibility = ScrollBarVisibility.Auto,
                        Content = new TextBlock
                        {
                            Text = task.Uids,
                            TextWrapping = TextWrapping.Wrap,
                            Margin = new Thickness(16)
                        }
                    }
                };
                await dialog.ShowAsync();
            }
        }

        private async void AbortTaskButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button btn && btn.DataContext is TasksList task)
            {
                // 修改数据库状态为2
                bool success = TasksService.UpdateStatus(task.Id, 2);
                if (success)
                {
                    var dialog = new ContentDialog
                    {
                        Title = "操作成功",
                        Content = "任务已中止。",
                        CloseButtonText = "确定",
                        XamlRoot = this.XamlRoot
                    };
                    await dialog.ShowAsync();
                    RefreshTaskList();
                }
                else
                {
                    var dialog = new ContentDialog
                    {
                        Title = "操作失败",
                        Content = "任务中止失败，请重试。",
                        CloseButtonText = "确定",
                        XamlRoot = this.XamlRoot
                    };
                    await dialog.ShowAsync();
                }
            }
        }

        private async void ExecutionResultButton_Click(object sender, RoutedEventArgs e)
        {
            // 获取按钮的 DataContext，即当前任务项
            var button = sender as Button;
            if (button == null)
                return;

            var taskItem = button.DataContext as TasksList;
            if (taskItem == null)
                return;

            // 构建对话框，分为执行参数和执行结果两个部分
            ContentDialog dialog = new ContentDialog
            {
                Title = "执行详细信息",
                CloseButtonText = "关闭",
                XamlRoot = this.XamlRoot
            };

            // 创建包含上部分执行参数和下部分执行结果的面板
            StackPanel panel = new StackPanel
            {
                Orientation = Orientation.Vertical
            };
            //查询执行参数配置
            var taskType = TaskTypeService.GetById(taskItem.Tid);


            // 上部分：执行参数
            TextBlock parametersLabel = new TextBlock
            {
                Text = "执行参数：",
                //FontWeight = Windows.UI.Text.FontWeights.SemiBold,
                Margin = new Thickness(0, 0, 0, 8)
            };

            // 示例：展示任务ID、名称、平台、账号、搜索路径等参数
            string parametersText = $"任务ID: {taskItem.Id}\n" +
                                    $"任务类型: {taskItem.Name}\n" +
                                    $"执行平台: {taskItem.Platform}\n" +
                                    $"执行账号: {taskItem.Uids}\n" +
                                    $"搜索路径: {taskItem.SearchUrl}\n" +
                                    $"动作间隔: {taskType.IntervalTimeMin} - {taskType.IntervalTimeMax} \n";
            if (taskType.Id == 4)
            {
                parametersText += $"养号总时长: {taskType.CountTimeMin} - {taskType.CountTimeMax} \n";
            }
            else
            {
                parametersText += $"用户数量: {taskType.UserNum}\n";
                parametersText += $"大V筛选: {taskType.FansNum}\n";
            }

            TextBlock parametersTextBlock = new TextBlock
            {
                Text = parametersText
            };

            // 下部分：执行结果，假定 results 是一个属性
            TextBlock resultsLabel = new TextBlock
            {
                Text = "执行结果：",
                //FontWeight = Windows.UI.Text.FontWeights.SemiBold,
                Margin = new Thickness(0, 12, 0, 8)
            };

            string resultsText = taskItem.Results != null ? taskItem.Results.ToString() : "无结果";
            TextBlock resultsTextBlock = new TextBlock
            {
                Text = resultsText,
                TextWrapping = TextWrapping.Wrap
            };

            panel.Children.Add(parametersLabel);
            panel.Children.Add(parametersTextBlock);
            panel.Children.Add(resultsLabel);
            panel.Children.Add(resultsTextBlock);

            dialog.Content = panel;

            await dialog.ShowAsync();
        }

        // 2. 修改 TranslateButton_Click，传递来源
        private async void TranslateButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button btn)
            {
                if (btn.Parent is Grid grid)
                {
                    var textBox = grid.Children.OfType<TextBox>().FirstOrDefault();
                    if (textBox != null)
                    {
                        _currentTextBox = textBox;
                        // 判断是哪个文本框
                        string source = textBox.Name == "KeywordsTextBox" ? "keywords" : "content";
                        await ShowTranslateDialogAsync(textBox.Text, source);
                    }
                }
            }
        }

        private async Task ShowTranslateDialogAsync(string originalText, string source)
        {
            var dialog = new ContentDialog
            {
                Title = "翻译",
                CloseButtonText = "取消",
                PrimaryButtonText = "确定",
                XamlRoot = this.XamlRoot,
                RequestedTheme = ElementTheme.Default,
                MinWidth = 500,
                MinHeight = 400
            };

            var comboBox = new ComboBox
            {
                ItemsSource = _languages,
                DisplayMemberPath = "Name",
                SelectedIndex = 0,
                Margin = new Thickness(0, 0, 0, 8)
            };

            var originalTextBox = new TextBox
            {
                Text = originalText,
                IsReadOnly = true,
                AcceptsReturn = true,
                Height = 100,
                TextWrapping = TextWrapping.Wrap,
                Margin = new Thickness(0, 0, 0, 8)
            };

            var translatedTextBox = new TextBox
            {
                AcceptsReturn = true,
                Height = 100,
                TextWrapping = TextWrapping.Wrap,
                Margin = new Thickness(0, 0, 0, 8)
            };

            var translateBtn = new Button
            {
                Content = "翻译",
                HorizontalAlignment = HorizontalAlignment.Left,
                Margin = new Thickness(0, 0, 0, 8)
            };

            translateBtn.Click += async (s, e) =>
            {
                var lang = (comboBox.SelectedItem as LanguageItem)?.Code ?? "zh";
                translatedTextBox.Text = await BaiduTrans.TranslateAsync(originalText, "auto", lang);
            };

            var panel = new StackPanel();
            panel.Children.Add(new TextBlock { Text = "目标语种：" });
            panel.Children.Add(comboBox);
            panel.Children.Add(new TextBlock { Text = "原文：" });
            panel.Children.Add(originalTextBox);
            panel.Children.Add(translateBtn);
            panel.Children.Add(new TextBlock { Text = "翻译结果：" });
            panel.Children.Add(translatedTextBox);

            dialog.Content = panel;

            var result = await dialog.ShowAsync();
            if (result == ContentDialogResult.Primary)
            {
                // 4. 根据来源保存原文和译文
                if (source == "keywords")
                {
                    _keywordsOriginalText = originalTextBox.Text;
                    _keywordsTranslatedText = translatedTextBox.Text;
                }
                else if (source == "content")
                {
                    _contentOriginalText = originalTextBox.Text;
                    _contentTranslatedText = translatedTextBox.Text;
                }

                // 回填翻译结果
                if (_currentTextBox != null)
                {
                    _currentTextBox.Text = translatedTextBox.Text;
                }
            }
            //else if (result == ContentDialogResult.None)
            //{
            //    // 取消时清空译文
            //    if (source == "keywords")
            //    {
            //        _keywordsTranslatedText = string.Empty;
            //    }
            //    else if (source == "content")
            //    {
            //        _contentTranslatedText = string.Empty;
            //    }
            //}
        }
    }
}
