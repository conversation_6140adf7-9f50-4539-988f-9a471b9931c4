<?xml version="1.0" encoding="utf-8"?>
<Page
    x:Class="App2.Views.Pages.TaskPage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:local="using:App2.Views.Pages"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    mc:Ignorable="d"
    Background="{ThemeResource ApplicationPageBackgroundThemeBrush}">
    

    <Grid>
       
        <!-- 顶部选项卡 -->
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- 选项卡标题 -->
        <TabView Grid.Row="0" 
                x:Name="TaskTabView"
                SelectedIndex="0"
                IsAddTabButtonVisible="False"
                TabWidthMode="SizeToContent"
                SelectionChanged="TaskTabView_SelectionChanged">
            <TabView.TabItems>
                <TabViewItem Header="执行任务" IsClosable="False"/>
                <TabViewItem Header="执行记录" IsClosable="False"/>
                <TabViewItem Header="执行参数配置" IsClosable="False"/>
            </TabView.TabItems>
        </TabView>

        <!-- 执行任务页面 -->
        <Grid x:Name="ExecuteTaskContent" Grid.Row="1" Margin="20">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- 选择任务 -->
            <Grid Grid.Row="0" Margin="0,0,0,16">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock Text="选择任务" VerticalAlignment="Center" MinWidth="100"/>
                <StackPanel Grid.Column="1" Orientation="Horizontal" Spacing="16" x:Name="TaskTypeRadioPanel">
                    <RadioButton Content="{Binding name}"  />
                </StackPanel>
                
            </Grid>

            <!-- 平台账号 -->
            <Grid Grid.Row="1" Margin="0,0,0,16">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock Text="平台账号" VerticalAlignment="Center" MinWidth="100"/>
                <StackPanel Grid.Column="1" Orientation="Vertical" Spacing="8">
                    <StackPanel Orientation="Horizontal" Spacing="16" x:Name="PlatformRadioPanel"/>

                    <!-- 账号列表 -->
                    <Border BorderBrush="LightGray" BorderThickness="1" Margin="0,8,0,0" MaxHeight="300">
                        <ListView x:Name="AccountListView" SelectionMode="Multiple" Padding="0">
                            <ListView.ItemTemplate>
                                <DataTemplate>
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="40"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>
                                        
                                        <TextBlock Grid.Column="1" Text="{Binding AccountName}" Margin="8,0"/>
                                        <TextBlock Grid.Column="2" Text="{Binding LoginAccount}" Margin="8,0"/>
                                    </Grid>
                                </DataTemplate>
                            </ListView.ItemTemplate>
                        </ListView>
                    </Border>
                </StackPanel>
            </Grid>

            <!-- 搜索选择 -->
            <Grid Grid.Row="2" Margin="0,0,0,16">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock Text="搜索选择" VerticalAlignment="Center" MinWidth="100"/>
                <StackPanel Grid.Column="1" Orientation="Horizontal" Spacing="16" x:Name="SearchUrlPanel">
                    <RadioButton Content="帖子下留言的用户" GroupName="SearchUrlGroup" Checked="SearchUrlRadio_Checked" IsChecked="True"/>
                    <RadioButton Content="发帖大V的粉丝" GroupName="SearchUrlGroup" Checked="SearchUrlRadio_Checked"/>
                    <RadioButton Content="大V的粉丝" GroupName="SearchUrlGroup" Checked="SearchUrlRadio_Checked"/>
                    <RadioButton Content="直接搜索用户" GroupName="SearchUrlGroup" Checked="SearchUrlRadio_Checked"/>
                    
                </StackPanel>
            </Grid>

            <!-- 帖子关键词 -->
            <Grid Grid.Row="3" Margin="0,0,0,16">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock Text="搜索关键词" VerticalAlignment="Center" MinWidth="100"/>
                <TextBox Grid.Column="1" x:Name="KeywordsTextBox" PlaceholderText="输入关键词搜索帖子，如手游 三国杀（多个关键词使用空格隔开）"/>
                <Button Grid.Column="2" Content="翻译" Click="TranslateButton_Click" Foreground="#0078D7" Background="Transparent" BorderThickness="0"/>
            </Grid>

            <!-- 私信文案 -->
            <Grid Grid.Row="4" Margin="0,0,0,16">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock Text="发送文案" VerticalAlignment="Center" MinWidth="100"/>
                <TextBox Grid.Column="1" x:Name="ContentTextBox" PlaceholderText="请输入私信内容，确保发送内容合规" Height="80" TextWrapping="Wrap"/>
                <Button Grid.Column="2" Content="翻译" Click="TranslateButton_Click" Foreground="#0078D7" Background="Transparent" BorderThickness="0"/>
            </Grid>

            <!-- 执行按钮 -->
            <Button Grid.Row="6" Content="立即执行任务" Background="#4285F4" Foreground="White" 
                    HorizontalAlignment="Stretch" Padding="16" CornerRadius="4" Click="ExecuteTaskButton_Click" />
        </Grid>

        <!-- 执行记录页面 -->
        <Grid x:Name="ExecuteRecordContent" Grid.Row="1" Margin="20" Visibility="Collapsed">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- 筛选器 -->
            <Grid Grid.Row="0" Margin="0,0,0,16">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <ComboBox Grid.Column="0" x:Name="TaskTypeComboBox" PlaceholderText="任务类型" Width="120" Margin="0,0,8,0" DisplayMemberPath="Name"/>


                <ComboBox Grid.Column="1" PlaceholderText="社交平台" x:Name="PlatformComboBox" Width="120" Margin="0,0,8,0" />
                


                <ComboBox Grid.Column="2" PlaceholderText="任务状态" x:Name="SearchStatus" Width="120" Margin="0,0,8,0">
                    <ComboBoxItem Content="所有状态" Tag="0"/>
                    <ComboBoxItem Content="进行中" Tag="1"/>
                    <ComboBoxItem Content="已完成" Tag="2"/>
                </ComboBox>

                <TextBox Grid.Column="3" PlaceholderText="输入关键词搜索" x:Name="SearchKeyWords" Width="200" Margin="0,0,8,0"/>
                <TextBox Grid.Column="4" PlaceholderText="任务ID" Width="120" x:Name="SearchId" Margin="0,0,8,0"/>
                <Grid Grid.Column="5"/>
                <Button Grid.Column="6" Content="搜索" Foreground="#0078D7" Background="Transparent" BorderThickness="0" Click="SearchButton_Click"/>
            </Grid>

            <!-- 数据表格 -->
            <Border Grid.Row="1" BorderBrush="LightGray" BorderThickness="1" CornerRadius="4">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- 表头 -->
                    <Grid Grid.Row="0" Background="#F5F5F5" Padding="12,8">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="80"/>
                            <ColumnDefinition Width="120"/>
                            <ColumnDefinition Width="100"/>
                            <ColumnDefinition Width="80"/>
                            <ColumnDefinition Width="130"/>
                            <ColumnDefinition Width="140"/>
                            <ColumnDefinition Width="130"/>
                            <ColumnDefinition Width="130"/>
                            <ColumnDefinition Width="130"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <TextBlock Grid.Column="0" Text="任务ID" FontWeight="SemiBold"/>
                        <TextBlock Grid.Column="1" Text="任务类型" FontWeight="SemiBold"/>
                        <TextBlock Grid.Column="2" Text="执行平台" FontWeight="SemiBold"/>
                        <TextBlock Grid.Column="3" Text="执行账号" FontWeight="SemiBold"/>
                        <TextBlock Grid.Column="4" Text="路径" FontWeight="SemiBold"/>
                        <TextBlock Grid.Column="5" Text="关键词" FontWeight="SemiBold"/>
                        <TextBlock Grid.Column="6" Text="任务状态" FontWeight="SemiBold"/>
                        <TextBlock Grid.Column="7" Text="开始时间" FontWeight="SemiBold"/>
                        <TextBlock Grid.Column="8" Text="结束时间" FontWeight="SemiBold"/>
                        <TextBlock Grid.Column="9" Text="操作" FontWeight="SemiBold"/>
                    </Grid>

                    <!-- 表格内容 -->
                    <ListView x:Name="TaskRecordListView" Grid.Row="1" SelectionMode="None">
                        <ListView.ItemContainerStyle>
                            <Style TargetType="ListViewItem">
                                <Setter Property="HorizontalContentAlignment" Value="Stretch"/>
                                <Setter Property="Padding" Value="0"/>
                                <Setter Property="MinHeight" Value="0"/>
                            </Style>
                        </ListView.ItemContainerStyle>
                        <ListView.ItemTemplate>
                            <DataTemplate>
                                <Grid Padding="12,8" BorderBrush="LightGray" BorderThickness="0,0,0,1">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="80"/>
                                        <ColumnDefinition Width="120"/>
                                        <ColumnDefinition Width="100"/>
                                        <ColumnDefinition Width="80"/>
                                        <ColumnDefinition Width="130"/>
                                        <ColumnDefinition Width="140"/>
                                        <ColumnDefinition Width="130"/>
                                        <ColumnDefinition Width="130"/>
                                        <ColumnDefinition Width="130"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="{Binding Id}" VerticalAlignment="Center"/>
                                    <TextBlock Grid.Column="1" Text="{Binding Name}" VerticalAlignment="Center"/>
                                    <TextBlock Grid.Column="2" Text="{Binding Platform}" VerticalAlignment="Center"/>
                                    <TextBlock Grid.Column="3" Text="{Binding AccountNum}" VerticalAlignment="Center" Foreground="#0078D7" 
Tapped="AccountNumTextBlock_Tapped" />
                                    <TextBlock Grid.Column="4" Text="{Binding SearchUrl}" VerticalAlignment="Center"/>
                                    <TextBlock Grid.Column="5" Text="{Binding Keywords}" VerticalAlignment="Center" Foreground="#0078D7"/>
                                    <TextBlock Grid.Column="6" Text="{Binding StatusName}" VerticalAlignment="Center" Foreground="Green"/>
                                    <TextBlock Grid.Column="7" Text="{Binding CreatedAt}" VerticalAlignment="Center"/>
                                    <TextBlock Grid.Column="8" Text="{Binding UpdatedAt}" VerticalAlignment="Center"/>
                                    <StackPanel Grid.Column="9" Orientation="Horizontal" Spacing="8">
                                        <Button Content="执行结果" Background="Gray" Click="ExecutionResultButton_Click" Foreground="White" Padding="8,4"/>
                                        <Button Content="中止" Background="Red" IsEnabled="{Binding StatusAble}"  Click="AbortTaskButton_Click"  Foreground="White" Padding="8,4"/>
                                    </StackPanel>
                                </Grid>
                            </DataTemplate>
                        </ListView.ItemTemplate>
                    </ListView>
                </Grid>
            </Border>

            <!-- 分页样式固定在窗口底部 -->
            <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,16,0,0">
                <Button x:Name="PrevButton" Content="上一页" Margin="8" Padding="8,4" Click="PrevPage_Click" IsEnabled="{x:Bind IsPrevEnabled, Mode=OneWay}" />
                <TextBlock x:Name="PageInfoTextBlock" Text="{x:Bind PageInfoText, Mode=OneWay}" VerticalAlignment="Center" Margin="8,0"/>
                <Button x:Name="NextButton" Content="下一页" Margin="8" Padding="8,4" Click="NextPage_Click" IsEnabled="{x:Bind IsNextEnabled, Mode=OneWay}" />
                <TextBlock x:Name="TotalCountTextBlock" Text="{x:Bind TotalCountText, Mode=OneWay}" VerticalAlignment="Center" Margin="8,0"/>
            </StackPanel>
        </Grid>

        <!-- 执行参数配置页面 -->
        <ScrollViewer x:Name="ExecuteConfigContent" Grid.Row="1" Margin="20" Visibility="Collapsed">
            <StackPanel Spacing="16">

                <ListView x:Name="TaskTypeListView" Margin="0,0,0,16"
                         >
                    <ListView.ItemTemplate>
                        <DataTemplate>
                            <Border BorderBrush="LightGray" BorderThickness="1" CornerRadius="8" Padding="16" Margin="5" >
                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>
                                    <Grid Grid.Row="0">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Grid.Column="0" Text="{Binding Name}" FontSize="16" FontWeight="SemiBold"/>
                                        <Button Grid.Column="1" Content="保存" Margin="12,0,0,0" Click="SaveTaskType_Click" HorizontalAlignment="Right"/>
                                    </Grid>



                                    <!-- 配置内容1 -->
                                    <Grid  Grid.Row="1" Visibility="{Binding Id, Converter={StaticResource IdToVisibilityConverter}, ConverterParameter=4}">

                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>

                                        <!-- 左侧配置 -->
                                        <StackPanel Grid.Column="0" Spacing="12" Margin="0,0,16,0">
                                            <Grid>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="Auto"/>
                                                    <ColumnDefinition Width="*"/>
                                                </Grid.ColumnDefinitions>
                                                <TextBlock Grid.Column="0" Text="用户数量" VerticalAlignment="Center" MinWidth="80"/>
                                                <TextBox Grid.Column="1"  Text="{Binding UserNum, Mode=TwoWay}" PlaceholderText="每次任务关注并私信的用户数量（建议100以下）" Margin="8,0,0,0"/>
                                            </Grid>

                                            <Grid>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="Auto"/>
                                                    <ColumnDefinition Width="Auto"/>
                                                    <ColumnDefinition Width="Auto"/>
                                                    <ColumnDefinition Width="Auto"/>
                                                    <ColumnDefinition Width="Auto"/>
                                                </Grid.ColumnDefinitions>
                                                <TextBlock Grid.Column="0" Text="动作间隔时长" VerticalAlignment="Center" MinWidth="80"/>
                                                <TextBox Grid.Column="1"  Text="{Binding IntervalTimeMin, Mode=TwoWay}" Width="50" Margin="8,0,4,0"/>
                                                <TextBlock Grid.Column="2" Text="至" VerticalAlignment="Center" Margin="4,0"/>
                                                <TextBox Grid.Column="3" Text="{Binding IntervalTimeMax, Mode=TwoWay}" Width="50" Margin="4,0"/>
                                                <TextBlock Grid.Column="4" Text="秒" VerticalAlignment="Center" Margin="4,0,0,0"/>
                                            </Grid>
                                        </StackPanel>

                                        <!-- 右侧配置 -->
                                        <StackPanel Grid.Column="1" Spacing="12">
                                            <TextBlock Text="搜索路径参数" FontWeight="SemiBold"/>
                                            <Grid>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="Auto"/>
                                                    <ColumnDefinition Width="Auto"/>
                                                    <ColumnDefinition Width="*"/>
                                                </Grid.ColumnDefinitions>
                                                <TextBlock Grid.Column="0" Text="大V筛选" VerticalAlignment="Center" MinWidth="60"/>
                                                <TextBlock Grid.Column="1" Text="粉丝大于" VerticalAlignment="Center" Margin="8,0,4,0"/>
                                                <TextBox Grid.Column="2" Text="{Binding FansNum, Mode=TwoWay}" Width="80"/>
                                            </Grid>
                                        </StackPanel>
                                    </Grid>
                                    <!-- 配置内容2 -->
                                    <Grid  Grid.Row="1" Visibility="{Binding Id, Converter={StaticResource IdToVisibilityConverter}, ConverterParameter=5}">

                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>

                                        <!-- 左侧配置 -->
                                        <StackPanel Grid.Column="0" Spacing="12" Margin="0,0,16,0">
                                            <Grid>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="Auto"/>
                                                    <ColumnDefinition Width="*"/>
                                                </Grid.ColumnDefinitions>
                                                <TextBlock Grid.Column="0" Text="执行内容" VerticalAlignment="Center" MinWidth="80"/>
                                                <StackPanel Grid.Column="1" Orientation="Horizontal" Spacing="8" Margin="8,0,0,0">
                                                    <CheckBox Content="浏览" IsChecked="{Binding IsViewChecked, Mode=TwoWay}" />
                                                    <CheckBox Content="评论" IsChecked="{Binding IsCommentChecked, Mode=TwoWay}" />
                                                    <CheckBox Content="点赞" IsChecked="{Binding IsLikeChecked, Mode=TwoWay}" />
                                                </StackPanel>
                                            </Grid>

                                            <Grid>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="Auto"/>
                                                    <ColumnDefinition Width="Auto"/>
                                                    <ColumnDefinition Width="Auto"/>
                                                    <ColumnDefinition Width="Auto"/>
                                                    <ColumnDefinition Width="Auto"/>
                                                </Grid.ColumnDefinitions>
                                                <TextBlock Grid.Column="0" Text="养号总时长" VerticalAlignment="Center" MinWidth="80"/>
                                                <TextBox Grid.Column="1" Text="{Binding CountTimeMin, Mode=TwoWay}" Width="50" Margin="8,0,4,0"/>
                                                <TextBlock Grid.Column="2" Text="至" VerticalAlignment="Center" Margin="4,0"/>
                                                <TextBox Grid.Column="3" Text="{Binding CountTimeMax, Mode=TwoWay}" Width="50" Margin="4,0"/>
                                                <TextBlock Grid.Column="4" Text="分钟" VerticalAlignment="Center" Margin="4,0,0,0"/>
                                            </Grid>
                                        </StackPanel>

                                        <!-- 右侧配置 -->
                                        <StackPanel Grid.Column="1" Spacing="12">
                                            <Grid>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="Auto"/>
                                                    <ColumnDefinition Width="Auto"/>
                                                    <ColumnDefinition Width="Auto"/>
                                                    <ColumnDefinition Width="Auto"/>
                                                    <ColumnDefinition Width="Auto"/>
                                                </Grid.ColumnDefinitions>
                                                <TextBlock Grid.Column="0" Text="动作间隔时长" VerticalAlignment="Center" MinWidth="80"/>
                                                <TextBox Grid.Column="1" Text="{Binding IntervalTimeMin, Mode=TwoWay}" Width="50" Margin="8,0,4,0"/>
                                                <TextBlock Grid.Column="2" Text="至" VerticalAlignment="Center" Margin="4,0"/>
                                                <TextBox Grid.Column="3" Text="{Binding IntervalTimeMax, Mode=TwoWay}" Width="50" Margin="4,0"/>
                                                <TextBlock Grid.Column="4" Text="秒" VerticalAlignment="Center" Margin="4,0,0,0"/>
                                            </Grid>
                                        </StackPanel>
                                    </Grid>
                                </Grid>
                            </Border>
                        </DataTemplate>
                    </ListView.ItemTemplate>
                </ListView>




                <!-- 关注>私信任务 -->

                <!-- 关注任务 -->

                <!-- 私信任务 -->

                <!-- 养号任务 -->
            </StackPanel>
        </ScrollViewer>

        <!-- 全局加载遮罩层，放在Grid最后，确保覆盖所有内容 -->
        <Grid x:Name="GlobalLoadingOverlay"
      Background="#80000000"
      Visibility="Collapsed"
      HorizontalAlignment="Stretch"
      VerticalAlignment="Stretch"
      Grid.RowSpan="2">
            <ProgressRing IsActive="True" HorizontalAlignment="Center" VerticalAlignment="Center"/>
        </Grid>
    </Grid>
    
</Page>
