<?xml version="1.0" encoding="utf-8"?>
<Page
    x:Class="App2.Views.Pages.SettingsPage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:local="using:App2.Views.Pages"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    mc:Ignorable="d">

    <Grid Background="{ThemeResource ApplicationPageBackgroundThemeBrush}">
        <ScrollViewer>
            <StackPanel Margin="24" Spacing="24">
                <!-- 页面标题 -->
                <TextBlock Text="设置" 
                          FontSize="28" 
                          FontWeight="SemiBold"
                          Margin="0,0,0,8"/>

                <!-- 应用程序设置 -->
                <Border Background="{ThemeResource CardBackgroundFillColorDefaultBrush}" 
                       CornerRadius="8" 
                       Padding="16">
                    <StackPanel Spacing="16">
                        <TextBlock Text="应用程序设置" 
                                  FontSize="18" 
                                  FontWeight="SemiBold"/>
                        
                        <!-- 主题设置 -->
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            
                            <StackPanel Grid.Column="0">
                                <TextBlock Text="应用主题" FontWeight="Medium"/>
                                <TextBlock Text="选择应用程序的外观主题" 
                                          FontSize="12" 
                                          Foreground="{ThemeResource TextFillColorSecondaryBrush}"/>
                            </StackPanel>
                            
                            <ComboBox x:Name="ThemeComboBox" 
                                     Grid.Column="1" 
                                     MinWidth="120"
                                     SelectionChanged="ThemeComboBox_SelectionChanged">
                                <ComboBoxItem Content="跟随系统" Tag="Default"/>
                                <ComboBoxItem Content="浅色主题" Tag="Light"/>
                                <ComboBoxItem Content="深色主题" Tag="Dark"/>
                            </ComboBox>
                        </Grid>

                        <!-- 自启动设置 -->
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            
                            <StackPanel Grid.Column="0">
                                <TextBlock Text="开机自启动" FontWeight="Medium"/>
                                <TextBlock Text="程序随系统启动时自动运行" 
                                          FontSize="12" 
                                          Foreground="{ThemeResource TextFillColorSecondaryBrush}"/>
                            </StackPanel>
                            
                            <ToggleSwitch x:Name="AutoStartToggle" 
                                         Grid.Column="1"
                                         Toggled="AutoStartToggle_Toggled"/>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- 数据库设置 -->
                <Border Background="{ThemeResource CardBackgroundFillColorDefaultBrush}" 
                       CornerRadius="8" 
                       Padding="16">
                    <StackPanel Spacing="16">
                        <TextBlock Text="数据库设置" 
                                  FontSize="18" 
                                  FontWeight="SemiBold"/>
                        
                        <!-- 连接状态 -->
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            
                            <StackPanel Grid.Column="0">
                                <TextBlock Text="数据库连接状态" FontWeight="Medium"/>
                                <TextBlock x:Name="ConnectionStatusText" 
                                          Text="检测中..." 
                                          FontSize="12" 
                                          Foreground="{ThemeResource TextFillColorSecondaryBrush}"/>
                            </StackPanel>
                            
                            <Button x:Name="TestConnectionButton" 
                                   Grid.Column="1" 
                                   Content="测试连接"
                                   Click="TestConnectionButton_Click"/>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- 系统信息 -->
                <Border Background="{ThemeResource CardBackgroundFillColorDefaultBrush}" 
                       CornerRadius="8" 
                       Padding="16">
                    <StackPanel Spacing="16">
                        <TextBlock Text="系统信息" 
                                  FontSize="18" 
                                  FontWeight="SemiBold"/>
                        
                        <!-- 版本信息 -->
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="120"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <TextBlock Grid.Column="0" Text="应用版本：" FontWeight="Medium"/>
                            <TextBlock x:Name="AppVersionText" Grid.Column="1" Text="1.0.0"/>
                        </Grid>

                        <!-- 最后登录时间 -->
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="120"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <TextBlock Grid.Column="0" Text="最后登录：" FontWeight="Medium"/>
                            <TextBlock x:Name="LastLoginText" Grid.Column="1" Text="--"/>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- 操作按钮 -->
                <Border Background="{ThemeResource CardBackgroundFillColorDefaultBrush}" 
                       CornerRadius="8" 
                       Padding="16">
                    <StackPanel Spacing="16">
                        <TextBlock Text="系统操作" 
                                  FontSize="18" 
                                  FontWeight="SemiBold"/>
                        
                        <StackPanel Orientation="Horizontal" Spacing="12">
                            <Button x:Name="CleanDataButton" 
                                   Content="清理数据"
                                   Click="CleanDataButton_Click"/>
                            
                            <Button x:Name="ResetAppButton" 
                                   Content="重置应用"
                                   Style="{StaticResource AccentButtonStyle}"
                                   Click="ResetAppButton_Click"/>
                        </StackPanel>
                    </StackPanel>
                </Border>
            </StackPanel>
        </ScrollViewer>
    </Grid>
</Page>