<?xml version="1.0" encoding="utf-8"?>
<Application
    x:Class="App2.App"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:local="using:App2"
    xmlns:helpers="using:App2.Helpers">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <XamlControlsResources xmlns="using:Microsoft.UI.Xaml.Controls" />
                <!-- Other merged dictionaries here -->
            </ResourceDictionary.MergedDictionaries>
            
            <!-- Converters -->
            <helpers:BoolToVisibilityConverter x:Key="BoolToVisibilityConverter"/>
            
            <!-- Other app resources here -->
            <helpers:IdToVisibilityConverter x:Key="IdToVisibilityConverter"/>
            <helpers:IdToIsCheckedConverter x:Key="IdToIsCheckedConverter"/>
        </ResourceDictionary>
    </Application.Resources>
</Application>
