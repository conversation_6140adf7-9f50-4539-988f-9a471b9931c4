<?xml version="1.0" encoding="utf-8"?>
<Package xmlns="http://schemas.microsoft.com/appx/manifest/foundation/windows10" xmlns:mp="http://schemas.microsoft.com/appx/2014/phone/manifest" xmlns:uap="http://schemas.microsoft.com/appx/manifest/uap/windows10" xmlns:rescap="http://schemas.microsoft.com/appx/manifest/foundation/windows10/restrictedcapabilities" IgnorableNamespaces="uap rescap build" xmlns:build="http://schemas.microsoft.com/developer/appx/2015/build">
	<!--
    THIS PACKAGE MANIFEST FILE IS GENERATED BY THE BUILD PROCESS.

    Changes to this file will be lost when it is regenerated. To correct errors in this file, edit the source .appxmanifest file.

    For more information on package manifest files, see http://go.microsoft.com/fwlink/?LinkID=241727
  -->
	<Identity Name="840d83ed-445d-4184-9db4-3fb69e16ccda" Publisher="CN=Administrator" Version="1.0.0.0" ProcessorArchitecture="x64" />
	<mp:PhoneIdentity PhoneProductId="840d83ed-445d-4184-9db4-3fb69e16ccda" PhonePublisherId="00000000-0000-0000-0000-000000000000" />
	<Properties>
		<DisplayName>App2</DisplayName>
		<PublisherDisplayName>Administrator</PublisherDisplayName>
		<Logo>Assets\StoreLogo.png</Logo>
	</Properties>
	<Dependencies>
		<TargetDeviceFamily Name="Windows.Universal" MinVersion="10.0.17763.0" MaxVersionTested="10.0.19041.0" />
		<TargetDeviceFamily Name="Windows.Desktop" MinVersion="10.0.17763.0" MaxVersionTested="10.0.19041.0" />
		<PackageDependency Name="Microsoft.WindowsAppRuntime.1.7" MinVersion="7000.522.1444.0" Publisher="CN=Microsoft Corporation, O=Microsoft Corporation, L=Redmond, S=Washington, C=US" />
	</Dependencies>
	<Resources>
		<Resource Language="EN-US" />
	</Resources>
	<Applications>
		<Application Id="App" Executable="promotion.exe" EntryPoint="Windows.FullTrustApplication">
			<uap:VisualElements DisplayName="App2" Description="App2" BackgroundColor="transparent" Square150x150Logo="Assets\Square150x150Logo.png" Square44x44Logo="Assets\Square44x44Logo.png">
				<uap:DefaultTile Wide310x150Logo="Assets\Wide310x150Logo.png" />
				<uap:SplashScreen Image="Assets\SplashScreen.png" />
			</uap:VisualElements>
		</Application>
	</Applications>
	<Capabilities>
		<rescap:Capability Name="runFullTrust" />
	</Capabilities>
	<build:Metadata>
		<build:Item Name="Microsoft.UI.Xaml.Markup.Compiler.dll" Version="3.0.0.2506" />
		<build:Item Name="makepri.exe" Version="10.0.26100.4654 (WinBuild.160101.0800)" />
	</build:Metadata>
</Package>