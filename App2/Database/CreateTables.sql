-- 推广系统数据库建表语句
-- 创建数据库
CREATE DATABASE IF NOT EXISTS promotion
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

USE promotion;

-- 1. 用户表
CREATE TABLE IF NOT EXISTS users (
    id INT PRIMARY KEY AUTO_INCREMENT COMMENT '用户ID',
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希',
    salt VARCHAR(32) NOT NULL COMMENT '密码盐值',
    email VARCHAR(100) COMMENT '邮箱',
    phone VARCHAR(20) COMMENT '手机号',
    real_name VARCHAR(50) COMMENT '真实姓名',
    role ENUM('admin', 'manager', 'operator') DEFAULT 'operator' COMMENT '用户角色',
    status ENUM('active', 'inactive', 'locked') DEFAULT 'active' COMMENT '用户状态',
    last_login_time DATETIME COMMENT '最后登录时间',
    last_login_ip VARCHAR(45) COMMENT '最后登录IP',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 2. 用户会话表
CREATE TABLE IF NOT EXISTS user_sessions (
    id INT PRIMARY KEY AUTO_INCREMENT COMMENT '会话ID',
    user_id INT NOT NULL COMMENT '用户ID',
    session_token VARCHAR(255) NOT NULL UNIQUE COMMENT '会话令牌',
    expires_at DATETIME NOT NULL COMMENT '过期时间',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_session_token (session_token),
    INDEX idx_user_id (user_id),
    INDEX idx_expires_at (expires_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户会话表';

-- 3. 登录日志表
CREATE TABLE IF NOT EXISTS login_logs (
    id INT PRIMARY KEY AUTO_INCREMENT COMMENT '日志ID',
    username VARCHAR(50) NOT NULL COMMENT '用户名',
    user_id INT COMMENT '用户ID（登录成功时记录）',
    ip_address VARCHAR(45) NOT NULL COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    login_result ENUM('success', 'failed') NOT NULL COMMENT '登录结果',
    failure_reason VARCHAR(100) COMMENT '失败原因',
    login_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '登录时间',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_username (username),
    INDEX idx_user_id (user_id),
    INDEX idx_login_time (login_time),
    INDEX idx_login_result (login_result)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='登录日志表';

-- 插入默认管理员账户
INSERT INTO users (username, password_hash, salt, real_name, role, status) 
VALUES (
    'admin', 
    SHA2(CONCAT('123456', 'default_salt'), 256), 
    'default_salt', 
    '系统管理员', 
    'admin', 
    'active'
) ON DUPLICATE KEY UPDATE username = username;

-- 插入测试用户
INSERT INTO users (username, password_hash, salt, real_name, role, status) 
VALUES (
    'test', 
    SHA2(CONCAT('123456', 'test_salt'), 256), 
    'test_salt', 
    '测试用户', 
    'operator', 
    'active'
) ON DUPLICATE KEY UPDATE username = username;


-- 4. 用户群体表
CREATE TABLE IF NOT EXISTS user_group (
    id INT PRIMARY KEY AUTO_INCREMENT COMMENT '记录ID',
    nickname VARCHAR(100) NOT NULL COMMENT '用户昵称',
    user_id VARCHAR(50) NOT NULL COMMENT '用户ID',
    platform VARCHAR(50) NOT NULL COMMENT '所属平台',
    content TEXT COMMENT '内容',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_nickname (nickname),
    INDEX idx_user_id (user_id),
    INDEX idx_platform (platform)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户群体表';

-- 插入测试数据
INSERT INTO user_group (nickname, user_id, platform, content) VALUES 
('whssa', '*********', 'X', 'hi, I have a game to recommend...'),
('Johnson', '*********', 'Facebook', 'hi, I have a game to recommend...'),
('Miller', '**********', 'Tiktok', '-'),
('test', '123568', 'Tiktok', 'hello world 123')
ON DUPLICATE KEY UPDATE nickname = VALUES(nickname);

-- 账号表
CREATE TABLE `user_account` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '账号ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `simulator_id` int(11) DEFAULT '0' COMMENT '模拟器ID',
  `proxy_id` int(11) DEFAULT '0' COMMENT '代理ID',
  `account` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '登录账号',
  `password` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '登录密码',
  `account_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '账号名称',
  `account_status` enum('yes','no') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'yes' COMMENT '账号状态 yes-可用 no-不可用',
  `source` enum('Instagram','Tiktok','X') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'Instagram' COMMENT '账号来源',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`account_status`),
  KEY `idx_source` (`source`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='账号表';


-- 模拟器表
CREATE TABLE `simulator` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '模拟器名字',
  `marik_name` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '模拟器标识名(用于注册模拟设备时使用)',
  `type` enum('leidian') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '类型<leidian=雷电>',
  `status` tinyint(1) NOT NULL COMMENT '状态<1=启用|2=关闭>',
  `created_at` datetime NOT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '创建��间',
  `updated_at` datetime DEFAULT NULL COMMENT '修改时间',
  `config` json DEFAULT NULL COMMENT '模拟器配置内容',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='模拟器表';

