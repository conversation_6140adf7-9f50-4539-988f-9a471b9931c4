using System;

namespace App2.Config
{
    /// <summary>
    /// 数据库配置类
    /// </summary>
    public static class DatabaseConfig
    {
        /// <summary>
        /// MySQL连接字符串
        /// </summary>
        public static string ConnectionString =>
            "Server=121.40.125.184;Database=promotion;Uid=promotion;Pwd=*******************************;CharSet=utf8mb4;SslMode=None;";

        /// <summary>
        /// 连接超时时间（秒）
        /// </summary>
        public static int ConnectionTimeout => 30;

        /// <summary>
        /// 命令超时时间（秒）
        /// </summary>
        public static int CommandTimeout => 60;
    }
}