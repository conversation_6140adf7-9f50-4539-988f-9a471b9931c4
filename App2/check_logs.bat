@echo off
echo 检查应用程序日志...
echo.

set LOG_DIR=%LOCALAPPDATA%\App2

if not exist "%LOG_DIR%" (
    echo 日志目录不存在: %LOG_DIR%
    echo 应用程序可能从未启动过。
    pause
    exit /b 1
)

echo 日志目录: %LOG_DIR%
echo.

if exist "%LOG_DIR%\startup.log" (
    echo === 启动日志 ===
    type "%LOG_DIR%\startup.log"
    echo.
) else (
    echo 启动日志不存在
)

if exist "%LOG_DIR%\startup_error.log" (
    echo === 启动错误日志 ===
    type "%LOG_DIR%\startup_error.log"
    echo.
) else (
    echo 启动错误日志不存在
)

if exist "%LOG_DIR%\error.log" (
    echo === 应用程序错误日志 ===
    type "%LOG_DIR%\error.log"
    echo.
) else (
    echo 应用程序错误日志不存在
)

echo.
echo 按任意键打开日志目录...
pause > nul
explorer "%LOG_DIR%"