using System;
using System.Threading.Tasks;
using MySql.Data.MySqlClient;
using App2.Config;
using System.Diagnostics;

namespace App2.Utils
{
    /// <summary>
    /// ֱ���޸��û�Ⱥ������ת������
    /// </summary>
    public static class DirectUserGroupFixer
    {
        /// <summary>
        /// ֱ���޸��û�Ⱥ��������
        /// </summary>
        public static async Task<string> FixNowAsync()
        {
            var log = new System.Text.StringBuilder();
            log.AppendLine("=== ��ʼֱ���޸��û�Ⱥ��� ===");
            
            try
            {
                using var connection = new MySqlConnection(DatabaseConfig.ConnectionString);
                await connection.OpenAsync();
                log.AppendLine("���ݿ����ӳɹ�");

                // 1. ɾ�������´�����
                log.AppendLine("\n--- ����1: ���´����� ---");
                await DropAndCreateTableAsync(connection, log);

                // 2. �����������
                log.AppendLine("\n--- ����2: ����������� ---");
                await InsertTestDataAsync(connection, log);

                // 3. ��֤��ṹ
                log.AppendLine("\n--- ����3: ��֤��ṹ ---");
                await VerifyTableStructureAsync(connection, log);

                // 4. ���Բ�ѯ
                log.AppendLine("\n--- ����4: ���Բ�ѯ ---");
                await TestQueriesAsync(connection, log);

                log.AppendLine("\n=== �޸���� ===");
            }
            catch (Exception ex)
            {
                log.AppendLine($"\n����: {ex.Message}");
                log.AppendLine($"��ջ: {ex.StackTrace}");
            }

            var result = log.ToString();
            Debug.WriteLine(result);
            return result;
        }

        private static async Task DropAndCreateTableAsync(MySqlConnection connection, System.Text.StringBuilder log)
        {
            // ɾ�����б�
            var dropQuery = "DROP TABLE IF EXISTS user_group";
            using var dropCommand = new MySqlCommand(dropQuery, connection);
            await dropCommand.ExecuteNonQueryAsync();
            log.AppendLine("��ɾ�����б�");

            // �����±�
            var createQuery = @"
                CREATE TABLE user_group (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    nickname VARCHAR(100) NOT NULL,
                    user_id VARCHAR(50) NOT NULL,
                    platform VARCHAR(50) NOT NULL,
                    content TEXT,
                    source VARCHAR(100) DEFAULT '',
                    task_source VARCHAR(50) DEFAULT '',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

            using var createCommand = new MySqlCommand(createQuery, connection);
            await createCommand.ExecuteNonQueryAsync();
            log.AppendLine("�Ѵ����±�");
        }

        private static async Task InsertTestDataAsync(MySqlConnection connection, System.Text.StringBuilder log)
        {
            var insertQuery = @"
                INSERT INTO user_group (nickname, user_id, platform, content, source, task_source) VALUES
                ('Alice', 'user001', 'X', 'Hello world!', 'X�ƹ�', '��ע˽��'),
                ('Bob', 'user002', 'Facebook', 'Great content', 'Facebook�ƹ�', '��ע'),
                ('Charlie', 'user003', 'TikTok', 'Amazing video', 'TikTok�ƹ�', '˽��'),
                ('David', 'user004', 'Instagram', 'Beautiful photo', 'Instagram�ƹ�', '��ע˽��')";

            using var insertCommand = new MySqlCommand(insertQuery, connection);
            var rowsAffected = await insertCommand.ExecuteNonQueryAsync();
            log.AppendLine($"������ {rowsAffected} ����������");
        }

        private static async Task VerifyTableStructureAsync(MySqlConnection connection, System.Text.StringBuilder log)
        {
            var describeQuery = "DESCRIBE user_group";
            using var describeCommand = new MySqlCommand(describeQuery, connection);
            using var reader = await describeCommand.ExecuteReaderAsync();

            log.AppendLine("��ṹ:");
            while (await reader.ReadAsync())
            {
                var field = reader.GetString(0);
                var type = reader.GetString(1);
                var nullable = reader.GetString(2);
                log.AppendLine($"  {field}: {type} (�ɿ�: {nullable})");
            }
        }

        private static async Task TestQueriesAsync(MySqlConnection connection, System.Text.StringBuilder log)
        {
            try
            {
                // ���Ի�����ѯ
                var query = "SELECT id, nickname, user_id, platform, content, source, task_source FROM user_group LIMIT 2";
                using var command = new MySqlCommand(query, connection);
                using var reader = await command.ExecuteReaderAsync();

                log.AppendLine("��ѯ���:");
                while (await reader.ReadAsync())
                {
                    var id = reader.GetInt32(0);
                    var nickname = reader.GetString(1);
                    var userId = reader.GetString(2);
                    var platform = reader.GetString(3);
                    var content = reader.IsDBNull(4) ? "" : reader.GetString(4);
                    var source = reader.IsDBNull(5) ? "" : reader.GetString(5);
                    var taskSource = reader.IsDBNull(6) ? "" : reader.GetString(6);

                    log.AppendLine($"  ID: {id}, �ǳ�: {nickname}, ƽ̨: {platform}, ������Դ: {taskSource}");
                }
                
                log.AppendLine("������ѯ����ͨ��");
            }
            catch (Exception ex)
            {
                log.AppendLine($"��ѯ����ʧ��: {ex.Message}");
            }
        }

        /// <summary>
        /// ����UserGroupService
        /// </summary>
        public static async Task<string> TestServiceAsync()
        {
            var log = new System.Text.StringBuilder();
            log.AppendLine("=== ����UserGroupService ===");

            try
            {
                // ���Ի�ȡ��Ա�б�
                var members = await App2.Services.UserGroupService.GetUserGroupMembersAsync();
                log.AppendLine($"GetUserGroupMembersAsync() �ɹ�����ȡ {members.Count} ����¼");

                // ���Է�ҳ��ѯ
                var pagedResult = await App2.Services.UserGroupService.GetUserGroupMembersPagedAsync(1, 5);
                log.AppendLine($"GetUserGroupMembersPagedAsync() �ɹ����ܼ�¼: {pagedResult.TotalCount}����ǰҳ: {pagedResult.Data.Count}");

                log.AppendLine("UserGroupService ����ͨ��");
            }
            catch (Exception ex)
            {
                log.AppendLine($"UserGroupService ����ʧ��: {ex.Message}");
                log.AppendLine($"��ϸ����: {ex.StackTrace}");
            }

            var result = log.ToString();
            Debug.WriteLine(result);
            return result;
        }

        /// <summary>
        /// �������޸���֤����
        /// </summary>
        public static async Task<string> CompleteFixTestAsync()
        {
            var log = new System.Text.StringBuilder();
            log.AppendLine("=== �������û�Ⱥ���޸���֤ ===");

            try
            {
                // 1. ����ֱ���޸�
                log.AppendLine("\n--- ����1: ����ֱ���޸� ---");
                var fixResult = await FixNowAsync();
                log.AppendLine("ֱ���޸����");

                // 2. ����UserGroupService���з���
                log.AppendLine("\n--- ����2: �������з��񷽷� ---");
                
                // ���Ի�����ѯ
                var members = await App2.Services.UserGroupService.GetUserGroupMembersAsync();
                log.AppendLine($"GetUserGroupMembersAsync(): {members.Count} ����¼");

                // ����ɸѡ��ѯ
                var filteredMembers = await App2.Services.UserGroupService.GetUserGroupMembersAsync(
                    platform: "X", nickname: null, sourceKeyword: null, taskSource: "��ע˽��");
                log.AppendLine($"ɸѡ��ѯ(ƽ̨=X, ������Դ=��ע˽��): {filteredMembers.Count} ����¼");

                // ���Է�ҳ��ѯ
                var pagedResult = await App2.Services.UserGroupService.GetUserGroupMembersPagedAsync(1, 5);
                log.AppendLine($"��ҳ��ѯ(ҳ��=1, ÿҳ=5): �ܼ�{pagedResult.TotalCount}��, ��ǰҳ{pagedResult.Data.Count}��");

                // ��֤����������
                log.AppendLine("\n--- ����3: ��֤���������� ---");
                if (pagedResult.Data.Count > 0)
                {
                    var firstMember = pagedResult.Data[0];
                    log.AppendLine($"��һ����¼: ID={firstMember.Id}, �ǳ�={firstMember.Nickname}, ƽ̨={firstMember.Platform}");
                    log.AppendLine($"  ��Դ={firstMember.Source}, ������Դ={firstMember.TaskSource}({firstMember.TaskSourceText})");
                }

                log.AppendLine("\n=== �޸���֤�ɹ���� ===");
            }
            catch (Exception ex)
            {
                log.AppendLine($"\n�޸���֤ʧ��: {ex.Message}");
                log.AppendLine($"��ջ: {ex.StackTrace}");
            }

            var result = log.ToString();
            Debug.WriteLine(result);
            return result;
        }
    }
}