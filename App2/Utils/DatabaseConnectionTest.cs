using System;
using System.Threading.Tasks;
using App2.Services;

namespace App2.Utils
{
    public class DatabaseConnectionTest
    {
        public static async Task<string> TestUserGroupQuery()
        {
            try
            {
                Console.WriteLine("开始测试数据库连接...");
                
                var members = await DatabaseService.GetUserGroupMembersAsync();
                
                Console.WriteLine($"成功获取到 {members.Count} 条记录");
                
                if (members.Count > 0)
                {
                    Console.WriteLine("第一条记录的详细信息:");
                    var first = members[0];
                    Console.WriteLine($"  ID: {first.Id}");
                    Console.WriteLine($"  Nickname: {first.Nickname}");
                    Console.WriteLine($"  UserId: {first.UserId}");
                    Console.WriteLine($"  Platform: {first.Platform}");
                    Console.WriteLine($"  Content: {first.Content}");
                }
                
                return $"测试成功，获取到 {members.Count} 条记录";
            }
            catch (Exception ex)
            {
                var errorMsg = $"数据库连接测试失败: {ex.Message}\n详细信息: {ex}";
                Console.WriteLine(errorMsg);
                return errorMsg;
            }
        }
    }
}
