using System;
using System.Threading.Tasks;
using MySql.Data.MySqlClient;
using App2.Config;

namespace App2.Utils
{
    /// <summary>
    /// 数据库表结构更新工具
    /// </summary>
    public static class DatabaseUpdater
    {
        /// <summary>
        /// 更新user_group表结构，添加缺失的字段
        /// </summary>
        /// <returns>更新结果</returns>
        public static async Task<string> UpdateUserGroupTableAsync()
        {
            try
            {
                using var connection = new MySqlConnection(DatabaseConfig.ConnectionString);
                await connection.OpenAsync();

                var result = new System.Text.StringBuilder();
                result.AppendLine("=== 开始更新user_group表结构 ===");

                // 检查并添加source字段
                if (!await FieldExistsAsync(connection, "user_group", "source"))
                {
                    await ExecuteUpdateAsync(connection, "ALTER TABLE user_group ADD COLUMN source VARCHAR(100) DEFAULT '' COMMENT '来源'");
                    result.AppendLine("✓ 添加source字段");
                }
                else
                {
                    result.AppendLine("• source字段已存在");
                }

                // 检查并添加task_source字段
                if (!await FieldExistsAsync(connection, "user_group", "task_source"))
                {
                    await ExecuteUpdateAsync(connection, "ALTER TABLE user_group ADD COLUMN task_source INT DEFAULT 0 COMMENT '任务来源：0=未知, 1=关注私信, 2=关注, 3=私信'");
                    result.AppendLine("✓ 添加task_source字段");
                    
                    // 添加索引
                    await ExecuteUpdateAsync(connection, "ALTER TABLE user_group ADD INDEX idx_task_source (task_source)");
                    result.AppendLine("✓ 添加task_source索引");
                }
                else
                {
                    result.AppendLine("• task_source字段已存在");
                }

                // 更新现有数据的task_source字段为默认值
                await ExecuteUpdateAsync(connection, "UPDATE user_group SET task_source = 1 WHERE task_source IS NULL OR task_source = 0");
                result.AppendLine("✓ 更新现有数据的task_source字段");

                result.AppendLine("=== user_group表结构更新完成 ===");
                return result.ToString();
            }
            catch (Exception ex)
            {
                return $"更新user_group表结构失败: {ex.Message}";
            }
        }

        /// <summary>
        /// 检查字段是否存在
        /// </summary>
        /// <param name="connection">数据库连接</param>
        /// <param name="tableName">表名</param>
        /// <param name="fieldName">字段名</param>
        /// <returns>字段是否存在</returns>
        private static async Task<bool> FieldExistsAsync(MySqlConnection connection, string tableName, string fieldName)
        {
            try
            {
                var query = $"SHOW COLUMNS FROM {tableName} LIKE @fieldName";
                using var command = new MySqlCommand(query, connection);
                command.Parameters.AddWithValue("@fieldName", fieldName);
                
                using var reader = await command.ExecuteReaderAsync();
                return await reader.ReadAsync();
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 执行更新SQL
        /// </summary>
        /// <param name="connection">数据库连接</param>
        /// <param name="sql">SQL语句</param>
        private static async Task ExecuteUpdateAsync(MySqlConnection connection, string sql)
        {
            using var command = new MySqlCommand(sql, connection);
            await command.ExecuteNonQueryAsync();
        }
    }
}