using System;
using System.Threading.Tasks;
using MySql.Data.MySqlClient;
using App2.Config;
using System.Collections.Generic;

namespace App2.Utils
{
    /// <summary>
    /// �û�Ⱥ���ṹ���¹���
    /// </summary>
    public static class UserGroupTableUpdater
    {
        /// <summary>
        /// ��鲢����user_group��ṹ
        /// </summary>
        public static async Task<string> UpdateUserGroupTableAsync()
        {
            var result = "=== �û�Ⱥ���ṹ���� ===\n";
            
            try
            {
                using var connection = new MySqlConnection(DatabaseConfig.ConnectionString);
                await connection.OpenAsync();

                // 1. ��鵱ǰ��ṹ
                result += "\n--- ��ǰ��ṹ ---\n";
                var currentColumns = await GetTableColumnsAsync(connection);
                
                foreach (var column in currentColumns)
                {
                    result += $"�ֶ�: {column.Key}, ����: {column.Value}\n";
                }

                // 2. ���ȱʧ���ֶ�
                var fieldsAdded = 0;
                
                // ��鲢���source�ֶ�
                if (!currentColumns.ContainsKey("source"))
                {
                    result += "\n--- ���source�ֶ� ---\n";
                    var addSourceQuery = @"ALTER TABLE user_group 
                                          ADD COLUMN source VARCHAR(100) NULL COMMENT '��Դ��ǩ' 
                                          AFTER content";
                    using var addSourceCommand = new MySqlCommand(addSourceQuery, connection);
                    await addSourceCommand.ExecuteNonQueryAsync();
                    result += "source�ֶ���ӳɹ�\n";
                    fieldsAdded++;
                }
                else
                {
                    result += "source�ֶ��Ѵ���\n";
                }

                // ��鲢���task_source�ֶ�
                if (!currentColumns.ContainsKey("task_source"))
                {
                    result += "\n--- ���task_source�ֶ� ---\n";
                    var addTaskSourceQuery = @"ALTER TABLE user_group 
                                              ADD COLUMN task_source INT DEFAULT 0 COMMENT '������Դ��0=δ֪, 1=��ע˽��, 2=��ע, 3=˽��' 
                                              AFTER source";
                    using var addTaskSourceCommand = new MySqlCommand(addTaskSourceQuery, connection);
                    await addTaskSourceCommand.ExecuteNonQueryAsync();
                    result += "task_source�ֶ���ӳɹ�\n";
                    fieldsAdded++;
                }
                else
                {
                    result += "task_source�ֶ��Ѵ���\n";
                }

                // 3. �����º�ı�ṹ
                if (fieldsAdded > 0)
                {
                    result += "\n--- ���º�ı�ṹ ---\n";
                    var updatedColumns = await GetTableColumnsAsync(connection);
                    
                    foreach (var column in updatedColumns)
                    {
                        result += $"�ֶ�: {column.Key}, ����: {column.Value}\n";
                    }
                }

                result += $"\n=== ������ɣ������ {fieldsAdded} ���ֶ� ===\n";
                
                return result;
            }
            catch (Exception ex)
            {
                return $"����ʧ��: {ex.Message}\n{ex.StackTrace}";
            }
        }

        /// <summary>
        /// ��ȡ����ֶ���Ϣ
        /// </summary>
        private static async Task<Dictionary<string, string>> GetTableColumnsAsync(MySqlConnection connection)
        {
            var columns = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase);
            
            try
            {
                var query = "DESCRIBE user_group";
                using var command = new MySqlCommand(query, connection);
                using var reader = await command.ExecuteReaderAsync();
                
                while (await reader.ReadAsync())
                {
                    var fieldName = reader.GetString(0); // Field
                    var fieldType = reader.GetString(1); // Type
                    columns[fieldName] = fieldType;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"��ȡ��ṹʧ��: {ex}");
            }
            
            return columns;
        }

        /// <summary>
        /// ���Ը��º�Ĳ�ѯ����
        /// </summary>
        public static async Task<string> TestUpdatedQueriesAsync()
        {
            try
            {
                var result = "=== ���Ը��º�Ĳ�ѯ ===\n";
                
                // ���Ի�����ѯ
                result += "\n--- ���Ի�����ѯ ---\n";
                var members = await App2.Services.UserGroupService.GetUserGroupMembersAsync();
                result += $"��ѯ�ɹ�����ȡ�� {members.Count} ����¼\n";
                
                // ���Է�ҳ��ѯ
                result += "\n--- ���Է�ҳ��ѯ ---\n";
                var pagedResult = await App2.Services.UserGroupService.GetUserGroupMembersPagedAsync(1, 5);
                result += $"��ҳ��ѯ�ɹ����ܼ�¼: {pagedResult.TotalCount}, ��ǰҳ��¼: {pagedResult.Data.Count}\n";
                
                // ��ʾǰ������¼����ϸ��Ϣ
                result += "\n--- ��¼���� ---\n";
                for (int i = 0; i < Math.Min(3, pagedResult.Data.Count); i++)
                {
                    var member = pagedResult.Data[i];
                    result += $"ID: {member.Id}, �ǳ�: {member.Nickname}, ƽ̨: {member.Platform}, ��Դ: {member.Source}, ������Դ: {member.TaskSource}({member.TaskSourceText})\n";
                }
                
                return result;
            }
            catch (Exception ex)
            {
                return $"���Բ�ѯʧ��: {ex.Message}\n{ex.StackTrace}";
            }
        }

        /// <summary>
        /// �����������ݵ�ʾ��ֵ
        /// </summary>
        public static async Task<string> UpdateSampleDataAsync()
        {
            try
            {
                using var connection = new MySqlConnection(DatabaseConfig.ConnectionString);
                await connection.OpenAsync();

                var result = "=== ����ʾ������ ===\n";

                // Ϊ���м�¼���ʾ������
                var updateQueries = new[]
                {
                    "UPDATE user_group SET source = 'X�ƹ�', task_source = 1 WHERE platform = 'X' AND id = (SELECT id FROM (SELECT id FROM user_group WHERE platform = 'X' LIMIT 1) AS temp)",
                    "UPDATE user_group SET source = 'Facebook�ƹ�', task_source = 2 WHERE platform = 'Facebook' AND id = (SELECT id FROM (SELECT id FROM user_group WHERE platform = 'Facebook' LIMIT 1) AS temp)",
                    "UPDATE user_group SET source = 'TikTok�ƹ�', task_source = 3 WHERE platform = 'Tiktok' AND id = (SELECT id FROM (SELECT id FROM user_group WHERE platform = 'Tiktok' LIMIT 1) AS temp)"
                };

                int updatedCount = 0;
                foreach (var query in updateQueries)
                {
                    try
                    {
                        using var command = new MySqlCommand(query, connection);
                        var rowsAffected = await command.ExecuteNonQueryAsync();
                        updatedCount += rowsAffected;
                        result += $"������ {rowsAffected} ����¼\n";
                    }
                    catch (Exception ex)
                    {
                        result += $"����ʧ��: {ex.Message}\n";
                    }
                }

                result += $"\n�ܹ������� {updatedCount} ����¼\n";
                return result;
            }
            catch (Exception ex)
            {
                return $"����ʾ������ʧ��: {ex.Message}";
            }
        }
    }
}