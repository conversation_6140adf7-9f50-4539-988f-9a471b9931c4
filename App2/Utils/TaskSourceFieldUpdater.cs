using System;
using System.Threading.Tasks;
using MySql.Data.MySqlClient;
using App2.Config;
using System.Diagnostics;

namespace App2.Utils
{
    /// <summary>
    /// ������Դ�ֶθ��¹���
    /// </summary>
    public static class TaskSourceFieldUpdater
    {
        /// <summary>
        /// ��task_source�ֶδ���������ת��Ϊ�ַ�������
        /// </summary>
        public static async Task<string> UpdateTaskSourceFieldAsync()
        {
            var log = new System.Text.StringBuilder();
            log.AppendLine("=== ��ʼ����task_source�ֶ� ===");
            
            try
            {
                using var connection = new MySqlConnection(DatabaseConfig.ConnectionString);
                await connection.OpenAsync();
                log.AppendLine("���ݿ����ӳɹ�");

                // 1. ��鵱ǰ��ṹ
                log.AppendLine("\n--- ����1: ��鵱ǰ��ṹ ---");
                await CheckCurrentTableStructureAsync(connection, log);

                // 2. ���ݵ�ǰ����
                log.AppendLine("\n--- ����2: ���ݵ�ǰ���� ---");
                await BackupCurrentDataAsync(connection, log);

                // 3. ����µ��ַ����ֶ�
                log.AppendLine("\n--- ����3: ����µ��ַ����ֶ� ---");
                await AddNewStringFieldAsync(connection, log);

                // 4. Ǩ������
                log.AppendLine("\n--- ����4: Ǩ������ ---");
                await MigrateDataAsync(connection, log);

                // 5. ɾ�����ֶβ����������ֶ�
                log.AppendLine("\n--- ����5: ɾ�����ֶβ������� ---");
                await ReplaceOldFieldAsync(connection, log);

                // 6. ��֤���
                log.AppendLine("\n--- ����6: ��֤��� ---");
                await VerifyResultAsync(connection, log);

                log.AppendLine("\n=== task_source�ֶθ������ ===");
            }
            catch (Exception ex)
            {
                log.AppendLine($"\n����: {ex.Message}");
                log.AppendLine($"��ջ: {ex.StackTrace}");
            }

            var result = log.ToString();
            Debug.WriteLine(result);
            return result;
        }

        private static async Task CheckCurrentTableStructureAsync(MySqlConnection connection, System.Text.StringBuilder log)
        {
            var query = "DESCRIBE user_group";
            using var command = new MySqlCommand(query, connection);
            using var reader = await command.ExecuteReaderAsync();

            log.AppendLine("��ǰ��ṹ:");
            while (await reader.ReadAsync())
            {
                var field = reader.GetString(0);
                var type = reader.GetString(1);
                log.AppendLine($"  {field}: {type}");
            }
        }

        private static async Task BackupCurrentDataAsync(MySqlConnection connection, System.Text.StringBuilder log)
        {
            // ����Ƿ���������Ҫ����
            var countQuery = "SELECT COUNT(*) FROM user_group WHERE task_source IS NOT NULL";
            using var countCommand = new MySqlCommand(countQuery, connection);
            var count = Convert.ToInt32(await countCommand.ExecuteScalarAsync());
            
            log.AppendLine($"���� {count} ����¼��ҪǨ��");
        }

        private static async Task AddNewStringFieldAsync(MySqlConnection connection, System.Text.StringBuilder log)
        {
            try
            {
                // ����µ��ַ����ֶ�
                var addFieldQuery = @"ALTER TABLE user_group 
                                     ADD COLUMN task_source_text VARCHAR(50) DEFAULT '' 
                                     COMMENT '������Դ�ı�'";
                using var addFieldCommand = new MySqlCommand(addFieldQuery, connection);
                await addFieldCommand.ExecuteNonQueryAsync();
                log.AppendLine("����� task_source_text �ֶ�");
            }
            catch (Exception ex)
            {
                if (ex.Message.Contains("Duplicate column name"))
                {
                    log.AppendLine("task_source_text �ֶ��Ѵ���");
                }
                else
                {
                    throw;
                }
            }
        }

        private static async Task MigrateDataAsync(MySqlConnection connection, System.Text.StringBuilder log)
        {
            // ������ֵת��Ϊ��Ӧ���ı�
            var migrationQueries = new[]
            {
                "UPDATE user_group SET task_source_text = 'δ֪' WHERE task_source = 0 OR task_source IS NULL",
                "UPDATE user_group SET task_source_text = '��ע˽��' WHERE task_source = 1",
                "UPDATE user_group SET task_source_text = '��ע' WHERE task_source = 2",
                "UPDATE user_group SET task_source_text = '˽��' WHERE task_source = 3"
            };

            int totalUpdated = 0;
            foreach (var query in migrationQueries)
            {
                try
                {
                    using var command = new MySqlCommand(query, connection);
                    var rowsAffected = await command.ExecuteNonQueryAsync();
                    totalUpdated += rowsAffected;
                    log.AppendLine($"  ������ {rowsAffected} ����¼");
                }
                catch (Exception ex)
                {
                    log.AppendLine($"  Ǩ�Ʋ�ѯʧ��: {ex.Message}");
                }
            }

            log.AppendLine($"�ܹ�Ǩ���� {totalUpdated} ����¼");
        }

        private static async Task ReplaceOldFieldAsync(MySqlConnection connection, System.Text.StringBuilder log)
        {
            try
            {
                // ɾ���ɵ������ֶ�
                var dropOldFieldQuery = "ALTER TABLE user_group DROP COLUMN task_source";
                using var dropCommand = new MySqlCommand(dropOldFieldQuery, connection);
                await dropCommand.ExecuteNonQueryAsync();
                log.AppendLine("��ɾ���ɵ� task_source �ֶ�");

                // ���������ֶ�
                var renameFieldQuery = "ALTER TABLE user_group CHANGE task_source_text task_source VARCHAR(50) DEFAULT '' COMMENT '������Դ'";
                using var renameCommand = new MySqlCommand(renameFieldQuery, connection);
                await renameCommand.ExecuteNonQueryAsync();
                log.AppendLine("�ѽ� task_source_text ������Ϊ task_source");
            }
            catch (Exception ex)
            {
                log.AppendLine($"�滻�ֶ�ʱ����: {ex.Message}");
            }
        }

        private static async Task VerifyResultAsync(MySqlConnection connection, System.Text.StringBuilder log)
        {
            try
            {
                // �����º�ı�ṹ
                var describeQuery = "DESCRIBE user_group";
                using var describeCommand = new MySqlCommand(describeQuery, connection);
                using var reader = await describeCommand.ExecuteReaderAsync();

                log.AppendLine("���º�ı�ṹ:");
                while (await reader.ReadAsync())
                {
                    var field = reader.GetString(0);
                    var type = reader.GetString(1);
                    log.AppendLine($"  {field}: {type}");
                }
                reader.Close();

                // �������
                var dataQuery = "SELECT nickname, task_source FROM user_group LIMIT 3";
                using var dataCommand = new MySqlCommand(dataQuery, connection);
                using var dataReader = await dataCommand.ExecuteReaderAsync();

                log.AppendLine("\nʾ������:");
                while (await dataReader.ReadAsync())
                {
                    var nickname = dataReader.GetString(0);
                    var taskSource = dataReader.IsDBNull(1) ? "" : dataReader.GetString(1);
                    log.AppendLine($"  {nickname}: {taskSource}");
                }
            }
            catch (Exception ex)
            {
                log.AppendLine($"��֤ʱ����: {ex.Message}");
            }
        }
    }
}