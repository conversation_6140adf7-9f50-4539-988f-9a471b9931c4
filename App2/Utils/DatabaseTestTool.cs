using System;
using System.Threading.Tasks;
using App2.Services;
using Microsoft.UI.Xaml.Controls;

namespace App2.Utils
{
    /// <summary>
    /// 数据库连接测试工具
    /// </summary>
    public static class DatabaseTestTool
    {
        /// <summary>
        /// 测试数据库连接并显示结果
        /// </summary>
        /// <param name="xamlRoot">XAML根元素，用于显示对话框</param>
        public static async Task TestConnectionAsync(Microsoft.UI.Xaml.XamlRoot xamlRoot)
        {
            try
            {
                bool isConnected = await DatabaseService.TestConnectionAsync();
                
                string title = isConnected ? "连接成功" : "连接失败";
                string message = isConnected 
                    ? "数据库连接正常！" 
                    : "无法连接到数据库，请检查：\n1. MySQL服务是否启动\n2. 连接字符串是否正确\n3. 数据库是否存在";

                var dialog = new ContentDialog()
                {
                    Title = title,
                    Content = message,
                    CloseButtonText = "确定",
                    XamlRoot = xamlRoot
                };

                await dialog.ShowAsync();
            }
            catch (Exception ex)
            {
                var dialog = new ContentDialog()
                {
                    Title = "测试失败",
                    Content = $"数据库连接测试出错：{ex.Message}",
                    CloseButtonText = "确定",
                    XamlRoot = xamlRoot
                };

                await dialog.ShowAsync();
            }
        }
    }
}