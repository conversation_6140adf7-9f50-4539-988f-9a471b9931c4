using System;
using System.Threading.Tasks;
using System.Diagnostics;

namespace App2.Utils
{
    /// <summary>
    /// �����޸����Թ���
    /// </summary>
    public static class QuickFixTester
    {
        /// <summary>
        /// �����û�Ⱥ������Ŀ����޸�
        /// </summary>
        public static async Task RunUserGroupFixAsync()
        {
            try
            {
                Debug.WriteLine("=== ��ʼ�û�Ⱥ����������޸� ===");
                
                // 1. ������Ϻ��޸�
                var fixResult = await UserGroupErrorFixer.DiagnoseAndFixAsync();
                Debug.WriteLine(fixResult);
                
                // 2. ���Ե�¼�������ֵ��¹���
                Debug.WriteLine("\n=== ���Ե�¼�������� ===");
                var tableDebugResult = await LoginDebugHelper.DebugUserGroupTableAsync();
                Debug.WriteLine(tableDebugResult);
                
                // 3. �����û�Ⱥ�����
                var serviceTestResult = await LoginDebugHelper.TestUserGroupServiceAsync();
                Debug.WriteLine(serviceTestResult);
                
                Debug.WriteLine("\n=== �����޸���� ===");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"�����޸�ʧ��: {ex.Message}");
                Debug.WriteLine($"��ջ����: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// ��Ӧ������ʱ�Զ������޸�
        /// </summary>
        public static void RunAutoFixOnStartup()
        {
            // �ں�̨�߳��������޸���������UI
            _ = Task.Run(async () =>
            {
                try
                {
                    Debug.WriteLine("Ӧ������ - �Զ������û�Ⱥ���޸�");
                    await RunUserGroupFixAsync();
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"�Զ��޸�ʧ��: {ex.Message}");
                }
            });
        }
    }
}