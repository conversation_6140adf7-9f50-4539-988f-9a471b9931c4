using System;
using System.Threading.Tasks;
using App2.Services;
using App2.Config;

namespace App2.Utils
{
    public class SimpleDbTest
    {
        public static async Task TestDatabaseAsync(string[] args)
        {
            Console.WriteLine("开始测试数据库连接...");
            Console.WriteLine($"连接字符串: {DatabaseConfig.ConnectionString}");
            
            try
            {
                var members = await DatabaseService.GetUserGroupMembersAsync();
                Console.WriteLine($"成功查询到 {members.Count} 条记录");
                
                foreach (var member in members)
                {
                    Console.WriteLine($"ID: {member.Id}, Nickname: {member.Nickname}, UserId: {member.UserId}, Platform: {member.Platform}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"查询失败: {ex.Message}");
                Console.WriteLine($"完整异常: {ex}");
            }
            
            Console.WriteLine("按任意键继续...");
            Console.ReadKey();
        }
    }
}
