using System;
using System.IO;
using System.Threading.Tasks;
using MySql.Data.MySqlClient;
using App2.Config;

namespace App2.Utils
{
    public class DatabaseInitializer
    {
        public static async Task<string> InitializeDatabase()
        {
            try
            {
                // 读取 SQL 脚本
                var sqlPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "..", "..", "..", "..", "Database", "CreateTables.sql");
                if (!File.Exists(sqlPath))
                {
                    // 尝试相对路径
                    sqlPath = "Database/CreateTables.sql";
                    if (!File.Exists(sqlPath))
                    {
                        return "找不到 SQL 脚本文件";
                    }
                }
                
                var sqlScript = await File.ReadAllTextAsync(sqlPath);
                Console.WriteLine($"读取 SQL 脚本: {sqlScript.Length} 字符");
                
                // 分割 SQL 语句
                var statements = sqlScript.Split(';', StringSplitOptions.RemoveEmptyEntries);
                
                using var connection = new MySqlConnection(DatabaseConfig.ConnectionString);
                await connection.OpenAsync();
                Console.WriteLine("数据库连接成功");
                
                int executedCount = 0;
                foreach (var statement in statements)
                {
                    var trimmedStatement = statement.Trim();
                    if (string.IsNullOrEmpty(trimmedStatement) || trimmedStatement.StartsWith("--"))
                        continue;
                        
                    try
                    {
                        using var command = new MySqlCommand(trimmedStatement, connection);
                        await command.ExecuteNonQueryAsync();
                        executedCount++;
                        Console.WriteLine($"执行第 {executedCount} 条语句成功");
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"执行语句失败: {trimmedStatement.Substring(0, Math.Min(50, trimmedStatement.Length))}...");
                        Console.WriteLine($"错误: {ex.Message}");
                    }
                }
                
                return $"数据库初始化完成，共执行 {executedCount} 条语句";
            }
            catch (Exception ex)
            {
                var error = $"数据库初始化失败: {ex.Message}";
                Console.WriteLine(error);
                return error;
            }
        }
        
        public static async Task<string> TestUserGroupTable()
        {
            try
            {
                using var connection = new MySqlConnection(DatabaseConfig.ConnectionString);
                await connection.OpenAsync();
                
                // 测试查询
                var query = "SELECT COUNT(*) FROM user_group";
                using var command = new MySqlCommand(query, connection);
                var count = await command.ExecuteScalarAsync();
                
                return $"user_group 表中有 {count} 条记录";
            }
            catch (Exception ex)
            {
                return $"测试 user_group 表失败: {ex.Message}";
            }
        }
    }
}
