using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Threading.Tasks;

namespace App2.Utils
{
    public static class LeidianTool
    {
        public static async Task<CommandResult> ExecuteCommandAsync(string simulatorPath, string command, string arguments)
        {
            var result = new CommandResult();
            string fullCommand = $"{command} {arguments}".Trim();
            // string executablePath = Path.Combine(simulatorPath, "dnconsole.exe");
            string executablePath = simulatorPath;

            if (!File.Exists(executablePath))
            {
                result.Success = false;
                result.Error = $"错误: 雷电模拟器执行文件未找到: {executablePath}";
                return result;
            }

            try
            {
                using var process = new Process();
                process.StartInfo.FileName = executablePath;
                process.StartInfo.Arguments = fullCommand;
                process.StartInfo.UseShellExecute = false;
                process.StartInfo.RedirectStandardOutput = true;
                process.StartInfo.RedirectStandardError = true;
                process.StartInfo.CreateNoWindow = true; // 不显示控制台窗口

                process.Start();

                string output = await process.StandardOutput.ReadToEndAsync();
                string error = await process.StandardError.ReadToEndAsync();

                await process.WaitForExitAsync();

                result.ExitCode = process.ExitCode;
                result.Output = output.Trim();
                result.Error = error.Trim();
                result.Success = process.ExitCode == 0;
            }
            catch (System.Exception ex)
            {
                result.Success = false;
                result.Error = $"执行命令时发生异常: {ex.Message}";
            }

            return result;
        }

        /// <summary>
        /// 复制模拟器
        /// </summary>
        /// <param name="simulatorPath">模拟器主程序路径</param>
        /// <param name="name">新模拟器的名称</param>
        /// <param name="from">要复制的源模拟器名称或索引</param>
        /// <returns>命令执行结果</returns>
        public static async Task<CommandResult> CopySimulatorAsync(string simulatorPath, string name, string from)
        {
            // 例如：dnconsole.exe copy --name "模拟器1" --from "雷电模拟器"
            string arguments = $"copy --name \"{name}\" --from \"{from}\"";
            return await ExecuteCommandAsync(simulatorPath, "copy", arguments);
        }

        /// <summary>
        /// 启动模拟器
        /// </summary>
        /// <param name="simulatorPath">模拟器主程序路径</param>
        /// <param name="name">模拟器名称</param>
        /// <returns>命令执行结果</returns>
        public static async Task<CommandResult> LaunchSimulatorAsync(string simulatorPath, string name)
        {
            // 例如：dnconsole.exe launch --name "模拟器1"
            string arguments = $"--name \"{name}\"";
            return await ExecuteCommandAsync(simulatorPath, "launch", arguments);
        }

        /// <summary>
        /// 列出所有模拟器
        /// </summary>
        /// <param name="simulatorPath">模拟器主程序路径</param>
        /// <returns>命令执行结果，Output包含模拟器列表文本</returns>
        public static async Task<CommandResult> ListSimulatorsAsync(string simulatorPath)
        {
            // 例如：dnconsole.exe list
            return await ExecuteCommandAsync(simulatorPath, "list", "");
        }

        /// <summary>
        /// 修改模拟器配置
        /// </summary>
        /// <param name="simulatorPath">模拟器主程序路径</param>
        /// <param name="name">模拟器名称</param>
        /// <param name="options">修改选项</param>
        /// <returns>命令执行结果</returns>
        public static async Task<CommandResult> ModifySimulatorAsync(string simulatorPath, string name, ModifyOptions options)
        {
            var arguments = new List<string>();
            
            // 添加模拟器标识符
            arguments.Add($"--name \"{name}\"");

            // 添加各种配置选项
            if (!string.IsNullOrEmpty(options.Resolution))
                arguments.Add($"--resolution {options.Resolution}");
            
            if (options.Cpu.HasValue)
                arguments.Add($"--cpu {options.Cpu.Value}");
            
            if (options.Memory.HasValue)
                arguments.Add($"--memory {options.Memory.Value}");
            
            if (!string.IsNullOrEmpty(options.Manufacturer))
                arguments.Add($"--manufacturer {options.Manufacturer}");
            
            if (!string.IsNullOrEmpty(options.Model))
                arguments.Add($"--model {options.Model}");
            
            if (!string.IsNullOrEmpty(options.PhoneNumber))
                arguments.Add($"--pnumber {options.PhoneNumber}");
            
            if (!string.IsNullOrEmpty(options.Imei))
                arguments.Add($"--imei {options.Imei}");
            
            if (!string.IsNullOrEmpty(options.Imsi))
                arguments.Add($"--imsi {options.Imsi}");
            
            if (!string.IsNullOrEmpty(options.SimSerial))
                arguments.Add($"--simserial {options.SimSerial}");
            
            if (!string.IsNullOrEmpty(options.AndroidId))
                arguments.Add($"--androidid {options.AndroidId}");
            
            if (!string.IsNullOrEmpty(options.Mac))
                arguments.Add($"--mac {options.Mac}");
            
            if (options.AutoRotate.HasValue)
                arguments.Add($"--autorotate {(options.AutoRotate.Value ? 1 : 0)}");
            
            if (options.LockWindow.HasValue)
                arguments.Add($"--lockwindow {(options.LockWindow.Value ? 1 : 0)}");
            
            if (options.Root.HasValue)
                arguments.Add($"--root {(options.Root.Value ? 1 : 0)}");

            string argumentsString = string.Join(" ", arguments);
            return await ExecuteCommandAsync(simulatorPath, "modify", argumentsString);
        }

        /// <summary>
        /// 设置模拟器属性
        /// </summary>
        /// <param name="simulatorPath">模拟器主程序路径</param>
        /// <param name="name">模拟器名称/param>
        /// <param name="key">属性键</param>
        /// <param name="value">属性值</param>
        /// <returns>命令执行结果</returns>
        public static async Task<CommandResult> SetPropertyAsync(string simulatorPath, string name, string key, string value)
        {
            var arguments = new List<string>();
            
            // 添加模拟器标识符
            if (int.TryParse(name, out int index))
            {
                arguments.Add($"--index {index}");
            }
            else
            {
                arguments.Add($"--name \"{name}\"");
            }
            
            arguments.Add($"--key {key}");
            arguments.Add($"--value {value}");

            string argumentsString = string.Join(" ", arguments);
            return await ExecuteCommandAsync(simulatorPath, "setprop", argumentsString);
        }

        /// <summary>
        /// 执行ADB命令
        /// </summary>
        /// <param name="simulatorPath">模拟器主程序路径</param>
        /// <param name="name">模拟器名称</param>
        /// <param name="adbCommand">ADB命令字符串</param>
        /// <returns>命令执行结果</returns>
        public static async Task<CommandResult> ExecuteAdbCommandAsync(string simulatorPath, string name, string adbCommand)
        {
            var arguments = new List<string>();
            
            // 添加模拟器标识符
            arguments.Add($"--name \"{name}\"");
            arguments.Add($"--command \"{adbCommand}\"");

            string argumentsString = string.Join(" ", arguments);
            return await ExecuteCommandAsync(simulatorPath, "adb", argumentsString);
        }

        /// <summary>
        /// 删除模拟器
        /// </summary>
        /// <param name="simulatorPath">模拟器主程序路径</param>
        /// <param name="name">模拟器名称</param>
        /// <returns>命令执行结果</returns>
        public static async Task<CommandResult> RemoveSimulatorAsync(string simulatorPath, string name)
        {
            string arguments = $"--name \"{name}\"";
            return await ExecuteCommandAsync(simulatorPath, "remove", arguments);
        }

        /// <summary>
        /// 检测模拟器是否正在运行
        /// </summary>
        /// <param name="simulatorPath">模拟器主程序路径</param>
        /// <param name="name">模拟器名称</param>
        /// <returns>命令执行结果，Output为"running"表示运行中，"stop"表示未运行</returns>
        public static async Task<CommandResult> IsRunningAsync(string simulatorPath, string name)
        {
            string arguments = $"--name \"{name}\"";
            return await ExecuteCommandAsync(simulatorPath, "isrunning", arguments);
        }

        /// <summary>
        /// 重命名模拟器
        /// </summary>
        /// <param name="simulatorPath">模拟器主程序路径</param>
        /// <param name="name">模拟器名称</param>
        /// <param name="newTitle">新的模拟器标题</param>
        /// <returns>命令执行结果</returns>
        public static async Task<CommandResult> RenameSimulatorAsync(string simulatorPath, string name, string newTitle)
        {
            var arguments = new List<string>();
            
            // 添加模拟器标识符
            arguments.Add($"--name \"{name}\"");
            arguments.Add($"--title \"{newTitle}\"");

            string argumentsString = string.Join(" ", arguments);
            return await ExecuteCommandAsync(simulatorPath, "rename", argumentsString);
        }

        /// <summary>
        /// 重启模拟器
        /// </summary>
        /// <param name="simulatorPath">模拟器主程序路径</param>
        /// <param name="name">模拟器名称</param>
        /// <returns>命令执行结果</returns>
        public static async Task<CommandResult> RebootSimulatorAsync(string simulatorPath, string name)
        {
            string arguments = $"--name \"{name}\"";
            return await ExecuteCommandAsync(simulatorPath, "reboot", arguments);
        }

        /// <summary>
        /// 退出指定模拟器
        /// </summary>
        /// <param name="simulatorPath">模拟器主程序路径</param>
        /// <param name="name">模拟器名称</param>
        /// <returns>命令执行结果</returns>
        public static async Task<CommandResult> QuitSimulatorAsync(string simulatorPath, string name)
        {
            string arguments = $"--name \"{name}\"";
            return await ExecuteCommandAsync(simulatorPath, "quit", arguments);
        }

        /// <summary>
        /// 退出所有模拟器
        /// </summary>
        /// <param name="simulatorPath">模拟器主程序路径</param>
        /// <returns>命令执行结果</returns>
        public static async Task<CommandResult> QuitAllSimulatorsAsync(string simulatorPath)
        {
            return await ExecuteCommandAsync(simulatorPath, "quitall", "");
        }
    }

    /// <summary>
    /// 模拟器修改选项类
    /// </summary>
    public class ModifyOptions
    {
        /// <summary>
        /// 分辨率 (格式: width,height,dpi)
        /// </summary>
        public string? Resolution { get; set; }

        /// <summary>
        /// CPU核心数 (1-4)
        /// </summary>
        public int? Cpu { get; set; }

        /// <summary>
        /// 内存大小 (MB)
        /// </summary>
        public int? Memory { get; set; }

        /// <summary>
        /// 制造商
        /// </summary>
        public string? Manufacturer { get; set; }

        /// <summary>
        /// 型号
        /// </summary>
        public string? Model { get; set; }

        /// <summary>
        /// 手机号码
        /// </summary>
        public string? PhoneNumber { get; set; }

        /// <summary>
        /// 设备号 (IMEI)
        /// </summary>
        public string? Imei { get; set; }

        /// <summary>
        /// 蜂窝网络中唯一标识 (IMSI)
        /// </summary>
        public string? Imsi { get; set; }

        /// <summary>
        /// SIM卡序列号
        /// </summary>
        public string? SimSerial { get; set; }

        /// <summary>
        /// 设备唯一标识符 (Android ID)
        /// </summary>
        public string? AndroidId { get; set; }

        /// <summary>
        /// 无线网卡物理地址 (MAC)
        /// </summary>
        public string? Mac { get; set; }

        /// <summary>
        /// 自动旋转
        /// </summary>
        public bool? AutoRotate { get; set; }

        /// <summary>
        /// 锁定窗口
        /// </summary>
        public bool? LockWindow { get; set; }

        /// <summary>
        /// ROOT权限
        /// </summary>
        public bool? Root { get; set; }
    }
}
