using System;
using System.Threading.Tasks;
using MySql.Data.MySqlClient;
using App2.Config;
using System.Diagnostics;

namespace App2.Utils
{
    /// <summary>
    /// �򵥵��û�Ⱥ�����ݲ��Թ���
    /// </summary>
    public static class SimpleUserGroupLoader
    {
        /// <summary>
        /// �򵥲������ݿ����Ӻͻ�����ѯ
        /// </summary>
        public static async Task<string> TestBasicConnectionAsync()
        {
            var log = new System.Text.StringBuilder();
            log.AppendLine("=== �����Ӳ��� ===");
            
            try
            {
                using var connection = new MySqlConnection(DatabaseConfig.ConnectionString);
                await connection.OpenAsync();
                log.AppendLine("? ���ݿ����ӳɹ�");

                // ���Լ򵥲�ѯ
                var query = "SELECT COUNT(*) FROM user_group";
                using var command = new MySqlCommand(query, connection);
                var count = await command.ExecuteScalarAsync();
                log.AppendLine($"? ���м�¼��: {count}");

                // ���Ի����ֶβ�ѯ
                var testQuery = "SELECT id, nickname, platform FROM user_group LIMIT 1";
                using var testCommand = new MySqlCommand(testQuery, connection);
                using var reader = await testCommand.ExecuteReaderAsync();
                
                if (await reader.ReadAsync())
                {
                    log.AppendLine("? �����ֶβ�ѯ�ɹ�");
                    log.AppendLine($"  ʾ����¼: ID={reader.GetInt32(0)}, �ǳ�={reader.GetString(1)}, ƽ̨={reader.GetString(2)}");
                }
                else
                {
                    log.AppendLine("! ����û������");
                }

                log.AppendLine("=== ������� ===");
            }
            catch (Exception ex)
            {
                log.AppendLine($"? ����ʧ��: {ex.Message}");
            }

            var result = log.ToString();
            Debug.WriteLine(result);
            return result;
        }

        /// <summary>
        /// ����UserGroupService�Ļ�������
        /// </summary>
        public static async Task<string> TestUserGroupServiceAsync()
        {
            var log = new System.Text.StringBuilder();
            log.AppendLine("=== UserGroupService���� ===");
            
            try
            {
                // ���Է�ҳ��ѯ
                var pagedResult = await App2.Services.UserGroupService.GetUserGroupMembersPagedAsync(1, 5);
                log.AppendLine($"? ��ҳ��ѯ�ɹ�");
                log.AppendLine($"  �ܼ�¼��: {pagedResult.TotalCount}");
                log.AppendLine($"  ��ǰҳ��¼: {pagedResult.Data.Count}");
                log.AppendLine($"  ��ҳ��: {pagedResult.TotalPages}");

                if (pagedResult.Data.Count > 0)
                {
                    var first = pagedResult.Data[0];
                    log.AppendLine($"  ��һ����¼: {first.Nickname} ({first.Platform})");
                }

                log.AppendLine("=== ������� ===");
            }
            catch (Exception ex)
            {
                log.AppendLine($"? �������ʧ��: {ex.Message}");
                log.AppendLine($"  ��ϸ����: {ex.StackTrace}");
            }

            var result = log.ToString();
            Debug.WriteLine(result);
            return result;
        }

        /// <summary>
        /// �����Ϊ�գ��������ٵĲ�������
        /// </summary>
        public static async Task<string> EnsureMinimalDataAsync()
        {
            var log = new System.Text.StringBuilder();
            log.AppendLine("=== ȷ���������� ===");
            
            try
            {
                using var connection = new MySqlConnection(DatabaseConfig.ConnectionString);
                await connection.OpenAsync();

                // ����Ƿ�������
                var countQuery = "SELECT COUNT(*) FROM user_group";
                using var countCommand = new MySqlCommand(countQuery, connection);
                var count = Convert.ToInt32(await countCommand.ExecuteScalarAsync());

                log.AppendLine($"��ǰ��¼��: {count}");

                if (count == 0)
                {
                    log.AppendLine("��Ϊ�գ��������ٲ�������...");
                    
                    var insertQuery = @"
                        INSERT INTO user_group (nickname, user_id, platform, content) VALUES 
                        ('�����û�1', 'test001', 'X', '���ǲ�������1'),
                        ('�����û�2', 'test002', 'Facebook', '���ǲ�������2'),
                        ('�����û�3', 'test003', 'TikTok', '���ǲ�������3')";

                    using var insertCommand = new MySqlCommand(insertQuery, connection);
                    var rowsAffected = await insertCommand.ExecuteNonQueryAsync();
                    log.AppendLine($"? ������ {rowsAffected} ����������");
                }
                else
                {
                    log.AppendLine("? �����������ݣ��������");
                }

                log.AppendLine("=== ��� ===");
            }
            catch (Exception ex)
            {
                log.AppendLine($"? ����ʧ��: {ex.Message}");
            }

            var result = log.ToString();
            Debug.WriteLine(result);
            return result;
        }
    }
}