using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Text.Json;
using System.Threading.Tasks;
using App2.Models;

namespace App2.Utils
{
    public static class LeidianToolsNew
    {
        /// <summary>
        /// 执行雷电模拟器命令
        /// </summary>
        /// <param name="simulatorPath">雷电模拟器路径 (如: C:\leidian\LDPlayer9\dnconsole.exe)</param>
        /// <param name="action">操作命令 (如: copy, add, launch等)</param>
        /// <param name="deviceIdentifier">设备标识符 (名称或索引)</param>
        /// <param name="parameters">额外参数字典</param>
        /// <returns>命令执行结果</returns>
        public static async Task<CommandResult> ExecuteAsync(string simulatorPath, string action, string? deviceIdentifier = null, Dictionary<string, object>? parameters = null)
        {
            var result = new CommandResult();

            if (!File.Exists(simulatorPath))
            {
                result.Success = false;
                result.Error = $"错误: 雷电模拟器执行文件未找到: {simulatorPath}";
                return result;
            }

            try
            {
                var arguments = BuildArguments(action, deviceIdentifier, parameters);
                System.Diagnostics.Debug.WriteLine($"## ------- Result.模拟器执行命令 ---- Leidian@ExecuteAsync: {arguments}");
                
                using var process = new Process();
                process.StartInfo.FileName = simulatorPath;
                process.StartInfo.Arguments = arguments;
                process.StartInfo.UseShellExecute = false;
                process.StartInfo.RedirectStandardOutput = true;
                process.StartInfo.RedirectStandardError = true;
                process.StartInfo.CreateNoWindow = true;

                process.Start();

                string output = await process.StandardOutput.ReadToEndAsync();
                string error = await process.StandardError.ReadToEndAsync();

                await process.WaitForExitAsync();

                if (process.ExitCode == 0)
                {
                    System.Diagnostics.Debug.WriteLine($"## ------- Result.END ---- : {result.Error}");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"## ------- Result.Error ---- : {result.Error}");
                }
                result.ExitCode = process.ExitCode;
                result.Output = output.Trim();
                result.Error = error.Trim();
                result.Success = process.ExitCode == 0;
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.Error = $"执行命令时发生异常: {ex.Message}";
            }

            return result;
        }

        /// <summary>
        /// 构建命令参数
        /// </summary>
        private static string BuildArguments(string action, string? deviceIdentifier, Dictionary<string, object>? parameters)
        {
            var args = new List<string> { action };

            // 添加设备标识符
            if (!string.IsNullOrEmpty(deviceIdentifier))
            {
                // 判断是索引还是名称
                if (int.TryParse(deviceIdentifier, out _))
                {
                    args.Add($"--index \"{deviceIdentifier}\"");
                }
                else
                {
                    args.Add($"--name \"{deviceIdentifier}\"");
                }
            }

            // 添加额外参数
            if (parameters != null)
            {
                foreach (var param in parameters)
                {
                    var value = param.Value?.ToString();
                    if (!string.IsNullOrEmpty(value))
                    {
                        // 特殊处理某些参数格式
                        switch (param.Key.ToLower())
                        {
                            case "resolution":
                                args.Add($"--{param.Key} {value}");
                                break;
                            default:
                                args.Add($"--{param.Key} \"{value}\"");
                                break;
                        }
                    }
                }
            }

            return string.Join(" ", args);
        }

        #region 便捷方法

        /// <summary>
        /// 复制模拟器
        /// </summary>
        public static async Task<CommandResult> CopyAsync(string simulatorPath, string newName, string fromDevice)
        {
            var parameters = new Dictionary<string, object>
            {
                { "from", fromDevice }
            };
            return await ExecuteAsync(simulatorPath, "copy", newName, parameters);
        }

        /// <summary>
        /// 添加新模拟器
        /// </summary>
        public static async Task<CommandResult> AddAsync(string simulatorPath, string name)
        {
            return await ExecuteAsync(simulatorPath, "add", name);
        }

        /// <summary>
        /// 启动模拟器
        /// </summary>
        public static async Task<CommandResult> LaunchAsync(string simulatorPath, string device)
        {
            return await ExecuteAsync(simulatorPath, "launch", device);
        }

        /// <summary>
        /// 重命名模拟器
        /// </summary>
        public static async Task<CommandResult> RenameAsync(string simulatorPath, string device, string newTitle)
        {
            var parameters = new Dictionary<string, object>
            {
                { "title", newTitle }
            };
            return await ExecuteAsync(simulatorPath, "rename", device, parameters);
        }

        /// <summary>
        /// 重启模拟器
        /// </summary>
        public static async Task<CommandResult> RebootAsync(string simulatorPath, string device)
        {
            return await ExecuteAsync(simulatorPath, "reboot", device);
        }

        /// <summary>
        /// 退出模拟器
        /// </summary>
        public static async Task<CommandResult> QuitAsync(string simulatorPath, string device)
        {
            return await ExecuteAsync(simulatorPath, "quit", device);
        }

        /// <summary>
        /// 退出所有模拟器
        /// </summary>
        public static async Task<CommandResult> QuitAllAsync(string simulatorPath)
        {
            return await ExecuteAsync(simulatorPath, "quitall");
        }

        /// <summary>
        /// 删除模拟器
        /// </summary>
        public static async Task<CommandResult> RemoveAsync(string simulatorPath, string device)
        {
            return await ExecuteAsync(simulatorPath, "remove", device);
        }

        /// <summary>
        /// 备份模拟器
        /// </summary>
        public static async Task<CommandResult> BackupAsync(string simulatorPath, string device, string filePath)
        {
            var parameters = new Dictionary<string, object>
            {
                { "file", filePath }
            };
            return await ExecuteAsync(simulatorPath, "backup", device, parameters);
        }

        /// <summary>
        /// 恢复模拟器
        /// </summary>
        public static async Task<CommandResult> RestoreAsync(string simulatorPath, string device, string filePath)
        {
            var parameters = new Dictionary<string, object>
            {
                { "file", filePath }
            };
            return await ExecuteAsync(simulatorPath, "restore", device, parameters);
        }

        /// <summary>
        /// 设置属性
        /// </summary>
        public static async Task<CommandResult> SetPropAsync(string simulatorPath, string device, string key, string value)
        {
            var parameters = new Dictionary<string, object>
            {
                { "key", key },
                { "value", value }
            };
            return await ExecuteAsync(simulatorPath, "setprop", device, parameters);
        }

        /// <summary>
        /// 获取属性
        /// </summary>
        public static async Task<CommandResult> GetPropAsync(string simulatorPath, string device, string key)
        {
            var parameters = new Dictionary<string, object>
            {
                { "key", key }
            };
            return await ExecuteAsync(simulatorPath, "getprop", device, parameters);
        }

        /// <summary>
        /// 执行ADB命令
        /// </summary>
        public static async Task<CommandResult> AdbAsync(string simulatorPath, string device, string command)
        {
            var parameters = new Dictionary<string, object>
            {
                { "command", command }
            };
            return await ExecuteAsync(simulatorPath, "adb", device, parameters);
        }

        /// <summary>
        /// 修改模拟器配置
        /// </summary>
        public static async Task<CommandResult> ModifyAsync(string simulatorPath, string device, Dictionary<string, object> modifyParams)
        {
            return await ExecuteAsync(simulatorPath, "modify", device, modifyParams);
        }

        /// <summary>
        /// 安装APP
        /// </summary>
        public static async Task<CommandResult> InstallAppAsync(string simulatorPath, string device, string apkPath, string? packageName = null)
        {
            var parameters = new Dictionary<string, object>
            {
                { "filename", apkPath }
            };
            if (!string.IsNullOrEmpty(packageName))
            {
                parameters.Add("packagename", packageName);
            }
            return await ExecuteAsync(simulatorPath, "installapp", device, parameters);
        }

        /// <summary>
        /// 启动APP
        /// </summary>
        public static async Task<CommandResult> RunAppAsync(string simulatorPath, string device, string packageName)
        {
            var parameters = new Dictionary<string, object>
            {
                { "packagename", packageName }
            };
            return await ExecuteAsync(simulatorPath, "runapp", device, parameters);
        }

        /// <summary>
        /// 检测模拟器是否正在运行
        /// </summary>
        public static async Task<CommandResult> IsRunningAsync(string simulatorPath, string device)
        {
            return await ExecuteAsync(simulatorPath, "isrunning", device);
        }

        /// <summary>
        /// 获取模拟器列表
        /// </summary>
        public static async Task<CommandResult> ListAsync(string simulatorPath)
        {
            return await ExecuteAsync(simulatorPath, "list");
        }

        /// <summary>
        /// 获取模拟器详细信息
        /// </summary>
        public static async Task<CommandResult> List2Async(string simulatorPath)
        {
            return await ExecuteAsync(simulatorPath, "list2");
        }

        /// <summary>
        /// 获取显示器设备信息
        /// </summary>
        public static async Task<CommandResult> List3Async(string simulatorPath, string? device = null)
        {
            return await ExecuteAsync(simulatorPath, "list3", device);
        }

        /// <summary>
        /// 排列显示的模拟器
        /// </summary>
        public static async Task<CommandResult> SortWndAsync(string simulatorPath)
        {
            return await ExecuteAsync(simulatorPath, "sortWnd");
        }

        #endregion

        #region 辅助方法

        /// <summary>
        /// 创建修改配置参数字典
        /// </summary>
        public static Dictionary<string, object> CreateModifyParams(
            string? resolution = null,
            int? cpu = null,
            int? memory = null,
            string? manufacturer = null,
            string? model = null,
            string? phoneNumber = null,
            string? imei = null,
            string? imsi = null,
            string? simSerial = null,
            string? androidId = null,
            string? mac = null,
            bool? autoRotate = null,
            bool? lockWindow = null,
            bool? root = null)
        {
            var parameters = new Dictionary<string, object>();

            if (!string.IsNullOrEmpty(resolution)) parameters.Add("resolution", resolution);
            if (cpu.HasValue) parameters.Add("cpu", cpu.Value);
            if (memory.HasValue) parameters.Add("memory", memory.Value);
            if (!string.IsNullOrEmpty(manufacturer)) parameters.Add("manufacturer", manufacturer);
            if (!string.IsNullOrEmpty(model)) parameters.Add("model", model);
            if (!string.IsNullOrEmpty(phoneNumber)) parameters.Add("pnumber", phoneNumber);
            if (!string.IsNullOrEmpty(imei)) parameters.Add("imei", imei);
            if (!string.IsNullOrEmpty(imsi)) parameters.Add("imsi", imsi);
            if (!string.IsNullOrEmpty(simSerial)) parameters.Add("simserial", simSerial);
            if (!string.IsNullOrEmpty(androidId)) parameters.Add("androidid", androidId);
            if (!string.IsNullOrEmpty(mac)) parameters.Add("mac", mac);
            if (autoRotate.HasValue) parameters.Add("autorotate", autoRotate.Value ? 1 : 0);
            if (lockWindow.HasValue) parameters.Add("lockwindow", lockWindow.Value ? 1 : 0);
            if (root.HasValue) parameters.Add("root", root.Value ? 1 : 0);

            return parameters;
        }

        #endregion
    }
}
