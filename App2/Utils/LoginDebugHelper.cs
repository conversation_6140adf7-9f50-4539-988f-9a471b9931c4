using System;
using System.Threading.Tasks;
using MySql.Data.MySqlClient;
using App2.Config;

namespace App2.Utils
{
    /// <summary>
    /// ��¼��������
    /// </summary>
    public static class LoginDebugHelper
    {
        /// <summary>
        /// �����û���ṹ������
        /// </summary>
        public static async Task<string> DebugUserTableAsync()
        {
            try
            {
                using var connection = new MySqlConnection(DatabaseConfig.ConnectionString);
                await connection.OpenAsync();

                var result = "=== �û��������Ϣ ===\n";

                // 1. ����ṹ
                result += "\n--- ��ṹ ---\n";
                var descQuery = "DESCRIBE users";
                using var descCommand = new MySqlCommand(descQuery, connection);
                using var descReader = await descCommand.ExecuteReaderAsync();

                while (await descReader.ReadAsync())
                {
                    result += $"�ֶ�: {descReader.GetString(0)}, ����: {descReader.GetString(1)}, ��ֵ: {descReader.GetString(2)}\n";
                }
                descReader.Close();

                // 2. ��������û�����
                result += "\n--- �����û����� ---\n";
                var userQuery = "SELECT id, username, password_hash, salt, status FROM users LIMIT 10";
                using var userCommand = new MySqlCommand(userQuery, connection);
                using var userReader = await userCommand.ExecuteReaderAsync();

                while (await userReader.ReadAsync())
                {
                    var id = userReader.GetInt32(0);
                    var username = userReader.GetString(1);
                    var passwordHash = userReader.GetString(2);
                    var salt = userReader.GetString(3);
                    var status = userReader.GetString(4);
                    
                    result += $"ID: {id}, �û���: {username}, �����ϣ: {passwordHash[..Math.Min(20, passwordHash.Length)]}..., ��: {salt}, ״̬: {status}\n";
                }
                userReader.Close();

                // 3. ����������֤
                result += "\n--- ������֤���� ---\n";
                var testUserQuery = "SELECT username, password_hash, salt FROM users WHERE username IN ('admin', 'test') LIMIT 2";
                using var testCommand = new MySqlCommand(testUserQuery, connection);
                using var testReader = await testCommand.ExecuteReaderAsync();

                while (await testReader.ReadAsync())
                {
                    var username = testReader.GetString(0);
                    var passwordHash = testReader.GetString(1);
                    var salt = testReader.GetString(2);
                    
                    result += $"\n�����û�: {username}\n";
                    result += $"�洢�Ĺ�ϣ: {passwordHash}\n";
                    result += $"��ֵ: {salt}\n";
                    
                    // �������� "123456"
                    var testPassword = "123456";
                    
                    // .NET SHA256����
                    var dotnetHash = System.Security.Cryptography.SHA256.HashData(
                        System.Text.Encoding.UTF8.GetBytes(testPassword + salt));
                    var dotnetHashHex = Convert.ToHexString(dotnetHash).ToLower();
                    result += $".NET SHA256������: {dotnetHashHex}\n";
                    result += $"�Ƿ�ƥ��: {passwordHash.Equals(dotnetHashHex, StringComparison.OrdinalIgnoreCase)}\n";
                }
                testReader.Close();

                // 4. ֱ�Ӳ���MySQL SHA2����
                result += "\n--- MySQL SHA2�������� ---\n";
                var sha2Query = "SELECT SHA2(CONCAT('123456', 'default_salt'), 256) as mysql_hash";
                using var sha2Command = new MySqlCommand(sha2Query, connection);
                var mysqlHash = await sha2Command.ExecuteScalarAsync() as string;
                result += $"MySQL SHA2('123456' + 'default_salt'): {mysqlHash}\n";

                return result;
            }
            catch (Exception ex)
            {
                return $"����ʧ��: {ex.Message}\n{ex.StackTrace}";
            }
        }

        /// <summary>
        /// �����ض��û���������ĵ�¼
        /// </summary>
        public static async Task<string> TestLoginAsync(string username, string password)
        {
            try
            {
                var loginResult = await App2.Services.AuthService.ValidateLoginAsync(username, password, "debug");
                
                var result = $"=== ��¼���Խ�� ===\n";
                result += $"�û���: {username}\n";
                result += $"����: {password}\n";
                result += $"��¼�ɹ�: {loginResult.Success}\n";
                result += $"��Ϣ: {loginResult.Message}\n";
                
                if (loginResult.User != null)
                {
                    result += $"�û�ID: {loginResult.User.Id}\n";
                    result += $"�û���: {loginResult.User.Username}\n";
                    result += $"״̬: {loginResult.User.Status}\n";
                }
                
                return result;
            }
            catch (Exception ex)
            {
                return $"��¼����ʧ��: {ex.Message}\n{ex.StackTrace}";
            }
        }

        /// <summary>
        /// ���������û���ʹ�ü�������ڵ��ԣ�
        /// </summary>
        public static async Task<string> CreateTestUserAsync()
        {
            try
            {
                using var connection = new MySqlConnection(DatabaseConfig.ConnectionString);
                await connection.OpenAsync();

                // ɾ�����ܴ��ڵĲ����û�
                var deleteQuery = "DELETE FROM users WHERE username = 'testuser'";
                using var deleteCommand = new MySqlCommand(deleteQuery, connection);
                await deleteCommand.ExecuteNonQueryAsync();

                // �����µĲ����û���ʹ�ü�����
                var insertQuery = @"INSERT INTO users (username, password_hash, salt, real_name, role, status) 
                                   VALUES ('testuser', '123456', 'simple', '�����û�', 'operator', 'active')";
                using var insertCommand = new MySqlCommand(insertQuery, connection);
                var rowsAffected = await insertCommand.ExecuteNonQueryAsync();

                return rowsAffected > 0 ? "�����û������ɹ� (�û���: testuser, ����: 123456)" : "�����û�����ʧ��";
            }
            catch (Exception ex)
            {
                return $"���������û�ʧ��: {ex.Message}";
            }
        }

        /// <summary>
        /// �����û�Ⱥ���ṹ������
        /// </summary>
        public static async Task<string> DebugUserGroupTableAsync()
        {
            try
            {
                using var connection = new MySqlConnection(DatabaseConfig.ConnectionString);
                await connection.OpenAsync();

                var result = "=== �û�Ⱥ��������Ϣ ===\n";

                // 1. ����ṹ
                result += "\n--- ��ṹ ---\n";
                var descQuery = "DESCRIBE user_group";
                using var descCommand = new MySqlCommand(descQuery, connection);
                using var descReader = await descCommand.ExecuteReaderAsync();

                while (await descReader.ReadAsync())
                {
                    result += $"�ֶ�: {descReader.GetString(0)}, ����: {descReader.GetString(1)}, ��ֵ: {descReader.GetString(2)}\n";
                }
                descReader.Close();

                // 2. �����������
                result += "\n--- �������� ---\n";
                var dataQuery = "SELECT * FROM user_group LIMIT 5";
                using var dataCommand = new MySqlCommand(dataQuery, connection);
                using var dataReader = await dataCommand.ExecuteReaderAsync();

                while (await dataReader.ReadAsync())
                {
                    result += "��¼: ";
                    for (int i = 0; i < dataReader.FieldCount; i++)
                    {
                        var fieldName = dataReader.GetName(i);
                        var fieldValue = dataReader.IsDBNull(i) ? "NULL" : dataReader.GetValue(i)?.ToString() ?? "NULL";
                        result += $"{fieldName}={fieldValue}, ";
                    }
                    result += "\n";
                }
                dataReader.Close();

                // 3. ���Բ�ѯ����
                result += "\n--- ���Բ�ѯ ---\n";
                try
                {
                    var testQuery = "SELECT id, nickname, user_id, platform, content FROM user_group LIMIT 1";
                    using var testCommand = new MySqlCommand(testQuery, connection);
                    using var testReader = await testCommand.ExecuteReaderAsync();
                    
                    if (await testReader.ReadAsync())
                    {
                        result += "������ѯ�ɹ�\n";
                        result += $"ID����: {testReader.GetFieldType(0)}\n";
                        result += $"Nickname����: {testReader.GetFieldType(1)}\n";
                        result += $"UserId����: {testReader.GetFieldType(2)}\n";
                        result += $"Platform����: {testReader.GetFieldType(3)}\n";
                        result += $"Content����: {testReader.GetFieldType(4)}\n";
                    }
                    testReader.Close();
                }
                catch (Exception ex)
                {
                    result += $"���Բ�ѯʧ��: {ex.Message}\n";
                }

                return result;
            }
            catch (Exception ex)
            {
                return $"�����û�Ⱥ���ʧ��: {ex.Message}\n{ex.StackTrace}";
            }
        }

        /// <summary>
        /// �򵥲����û�Ⱥ�����
        /// </summary>
        public static async Task<string> TestUserGroupServiceAsync()
        {
            try
            {
                var result = "=== �û�Ⱥ�������� ===\n";
                
                // ���Ի�ȡ�û����Ա�б�
                result += "\n--- ���Ի�����ѯ ---\n";
                var members = await App2.Services.UserGroupService.GetUserGroupMembersAsync();
                result += $"������ѯ�ɹ�����ȡ�� {members.Count} ����¼\n";
                
                if (members.Count > 0)
                {
                    var firstMember = members[0];
                    result += $"��һ����¼: ID={firstMember.Id}, �ǳ�={firstMember.Nickname}, ƽ̨={firstMember.Platform}\n";
                }
                
                // ���Է�ҳ��ѯ
                result += "\n--- ���Է�ҳ��ѯ ---\n";
                var pagedResult = await App2.Services.UserGroupService.GetUserGroupMembersPagedAsync(1, 5);
                result += $"��ҳ��ѯ�ɹ����ܼ�¼: {pagedResult.TotalCount}, ��ǰҳ��¼: {pagedResult.Data.Count}\n";
                
                return result;
            }
            catch (Exception ex)
            {
                return $"�û�Ⱥ��������ʧ��: {ex.Message}\n{ex.StackTrace}";
            }
        }
    }
}