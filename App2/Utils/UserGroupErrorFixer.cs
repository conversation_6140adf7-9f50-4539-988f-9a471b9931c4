using System;
using System.Threading.Tasks;
using MySql.Data.MySqlClient;
using App2.Config;
using System.Collections.Generic;
using System.Text;

namespace App2.Utils
{
    /// <summary>
    /// �û�Ⱥ������޸�����
    /// </summary>
    public static class UserGroupErrorFixer
    {
        /// <summary>
        /// ��ϲ��޸��û�Ⱥ������������
        /// </summary>
        public static async Task<string> DiagnoseAndFixAsync()
        {
            var result = new StringBuilder("=== �û�Ⱥ�������Ϻ��޸� ===\n");
            
            try
            {
                using var connection = new MySqlConnection(DatabaseConfig.ConnectionString);
                await connection.OpenAsync();

                // 1. �����Ƿ����
                result.AppendLine("\n--- 1. ��������� ---");
                var tableExists = await CheckTableExistsAsync(connection);
                result.AppendLine($"�û�Ⱥ����Ƿ����: {tableExists}");

                if (!tableExists)
                {
                    result.AppendLine("�����û�Ⱥ���...");
                    await CreateUserGroupTableAsync(connection);
                    result.AppendLine("�û�Ⱥ��������");
                }

                // 2. ����ṹ
                result.AppendLine("\n--- 2. ����ṹ ---");
                var columns = await GetTableColumnsAsync(connection);
                result.AppendLine($"���� {columns.Count} ���ֶ�:");
                foreach (var column in columns)
                {
                    result.AppendLine($"  {column.Key}: {column.Value}");
                }

                // 3. ȷ�������ֶδ���
                result.AppendLine("\n--- 3. �������ֶ� ---");
                var requiredFields = new Dictionary<string, string>
                {
                    ["id"] = "INT PRIMARY KEY AUTO_INCREMENT",
                    ["nickname"] = "VARCHAR(100) NOT NULL",
                    ["user_id"] = "VARCHAR(50) NOT NULL",
                    ["platform"] = "VARCHAR(50) NOT NULL",
                    ["content"] = "TEXT",
                    ["source"] = "VARCHAR(100)",
                    ["task_source"] = "INT DEFAULT 0",
                    ["created_at"] = "DATETIME DEFAULT CURRENT_TIMESTAMP",
                    ["updated_at"] = "DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"
                };

                foreach (var field in requiredFields)
                {
                    if (!columns.ContainsKey(field.Key))
                    {
                        result.AppendLine($"���ȱʧ�ֶ�: {field.Key}");
                        await AddColumnAsync(connection, field.Key, field.Value);
                    }
                }

                // 4. �������������
                result.AppendLine("\n--- 4. ������������� ---");
                var dataCount = await GetRecordCountAsync(connection);
                result.AppendLine($"���м�¼��: {dataCount}");

                if (dataCount == 0)
                {
                    result.AppendLine("�����������...");
                    await InsertSampleDataAsync(connection);
                    result.AppendLine("�������ݲ������");
                }

                // 5. ���Բ�ѯ
                result.AppendLine("\n--- 5. ���Բ�ѯ���� ---");
                await TestQueriesAsync(connection, result);

                // 6. ���շ������
                result.AppendLine("\n--- 6. �����û�Ⱥ����� ---");
                await TestUserGroupServiceAsync(result);

                result.AppendLine("\n=== ��Ϻ��޸���� ===");
                return result.ToString();
            }
            catch (Exception ex)
            {
                result.AppendLine($"\n����: {ex.Message}");
                result.AppendLine($"��ջ����: {ex.StackTrace}");
                return result.ToString();
            }
        }

        private static async Task<bool> CheckTableExistsAsync(MySqlConnection connection)
        {
            try
            {
                var query = "SHOW TABLES LIKE 'user_group'";
                using var command = new MySqlCommand(query, connection);
                var result = await command.ExecuteScalarAsync();
                return result != null;
            }
            catch
            {
                return false;
            }
        }

        private static async Task CreateUserGroupTableAsync(MySqlConnection connection)
        {
            var createQuery = @"
                CREATE TABLE user_group (
                    id INT PRIMARY KEY AUTO_INCREMENT COMMENT '��¼ID',
                    nickname VARCHAR(100) NOT NULL COMMENT '�û��ǳ�',
                    user_id VARCHAR(50) NOT NULL COMMENT '�û�ID',
                    platform VARCHAR(50) NOT NULL COMMENT '����ƽ̨',
                    content TEXT COMMENT '����',
                    source VARCHAR(100) COMMENT '��Դ��ǩ',
                    task_source INT DEFAULT 0 COMMENT '������Դ��0=δ֪, 1=��ע˽��, 2=��ע, 3=˽��',
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '����ʱ��',
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '����ʱ��',
                    INDEX idx_nickname (nickname),
                    INDEX idx_user_id (user_id),
                    INDEX idx_platform (platform)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='�û�Ⱥ���'";

            using var command = new MySqlCommand(createQuery, connection);
            await command.ExecuteNonQueryAsync();
        }

        private static async Task<Dictionary<string, string>> GetTableColumnsAsync(MySqlConnection connection)
        {
            var columns = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase);
            
            try
            {
                var query = "DESCRIBE user_group";
                using var command = new MySqlCommand(query, connection);
                using var reader = await command.ExecuteReaderAsync();
                
                while (await reader.ReadAsync())
                {
                    var fieldName = reader.GetString(0);
                    var fieldType = reader.GetString(1);
                    columns[fieldName] = fieldType;
                }
            }
            catch
            {
                // ����ܲ�����
            }
            
            return columns;
        }

        private static async Task AddColumnAsync(MySqlConnection connection, string columnName, string columnDefinition)
        {
            try
            {
                var query = $"ALTER TABLE user_group ADD COLUMN {columnName} {columnDefinition}";
                using var command = new MySqlCommand(query, connection);
                await command.ExecuteNonQueryAsync();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"����ֶ� {columnName} ʧ��: {ex.Message}");
            }
        }

        private static async Task<int> GetRecordCountAsync(MySqlConnection connection)
        {
            try
            {
                var query = "SELECT COUNT(*) FROM user_group";
                using var command = new MySqlCommand(query, connection);
                var result = await command.ExecuteScalarAsync();
                return Convert.ToInt32(result);
            }
            catch
            {
                return 0;
            }
        }

        private static async Task InsertSampleDataAsync(MySqlConnection connection)
        {
            var insertQuery = @"
                INSERT INTO user_group (nickname, user_id, platform, content, source, task_source) VALUES 
                ('whssa', '245455552', 'X', 'hi, I have a game to recommend...', 'X�ƹ�', 1),
                ('Johnson', '524525434', 'Facebook', 'hi, I have a game to recommend...', 'Facebook�ƹ�', 2),
                ('Miller', '4534534345', 'TikTok', '-', 'TikTok�ƹ�', 3),
                ('TestUser', '123568', 'Instagram', 'hello world 123', 'Instagram�ƹ�', 1)
                ON DUPLICATE KEY UPDATE nickname = VALUES(nickname)";

            using var command = new MySqlCommand(insertQuery, connection);
            await command.ExecuteNonQueryAsync();
        }

        private static async Task TestQueriesAsync(MySqlConnection connection, StringBuilder result)
        {
            try
            {
                // ���Ի�����ѯ
                var query = "SELECT id, nickname, user_id, platform, content, source, task_source FROM user_group LIMIT 3";
                using var command = new MySqlCommand(query, connection);
                using var reader = await command.ExecuteReaderAsync();
                
                int recordCount = 0;
                while (await reader.ReadAsync())
                {
                    recordCount++;
                    var id = reader.GetInt32(0);
                    var nickname = reader.IsDBNull(1) ? "" : reader.GetString(1);
                    var userId = reader.IsDBNull(2) ? "" : reader.GetString(2);
                    var platform = reader.IsDBNull(3) ? "" : reader.GetString(3);
                    var content = reader.IsDBNull(4) ? "" : reader.GetString(4);
                    var source = reader.IsDBNull(5) ? "" : reader.GetString(5);
                    var taskSource = reader.IsDBNull(6) ? 0 : reader.GetInt32(6);
                    
                    result.AppendLine($"  ��¼{recordCount}: ID={id}, �ǳ�={nickname}, ƽ̨={platform}, ������Դ={taskSource}");
                }
                
                result.AppendLine($"������ѯ����ͨ������ȡ�� {recordCount} ����¼");
            }
            catch (Exception ex)
            {
                result.AppendLine($"��ѯ����ʧ��: {ex.Message}");
            }
        }

        private static async Task TestUserGroupServiceAsync(StringBuilder result)
        {
            try
            {
                // ���Ի�ȡ�û����Ա�б�
                var members = await App2.Services.UserGroupService.GetUserGroupMembersAsync();
                result.AppendLine($"UserGroupService.GetUserGroupMembersAsync() �ɹ�����ȡ�� {members.Count} ����¼");
                
                // ���Է�ҳ��ѯ
                var pagedResult = await App2.Services.UserGroupService.GetUserGroupMembersPagedAsync(1, 5);
                result.AppendLine($"UserGroupService.GetUserGroupMembersPagedAsync() �ɹ����ܼ�¼: {pagedResult.TotalCount}, ��ǰҳ: {pagedResult.Data.Count}");
                
                result.AppendLine("���з������ͨ��");
            }
            catch (Exception ex)
            {
                result.AppendLine($"�������ʧ��: {ex.Message}");
                result.AppendLine($"��ϸ����: {ex.StackTrace}");
            }
        }
    }
}