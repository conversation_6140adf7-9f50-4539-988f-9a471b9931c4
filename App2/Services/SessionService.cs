using System;
using System.Threading.Tasks;
using System.IO;
using Windows.Storage;
using App2.Models;

namespace App2.Services
{
    /// <summary>
    /// 会话管理服务
    /// </summary>
    public static class SessionService
    {
        private const string SESSION_FILE_NAME = "user_session.dat";
        private static readonly string SessionDirectory = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData), "App2");
        private static readonly string SessionFilePath = Path.Combine(SessionDirectory, SESSION_FILE_NAME);
        private static User? _currentUser;
        private static string? _currentSessionToken;

        /// <summary>
        /// 当前登录用户
        /// </summary>
        public static User? CurrentUser => _currentUser;

        /// <summary>
        /// 当前会话令牌
        /// </summary>
        public static string? CurrentSessionToken => _currentSessionToken;

        /// <summary>
        /// 是否已登录
        /// </summary>
        public static bool IsLoggedIn => _currentUser != null && !string.IsNullOrEmpty(_currentSessionToken);

        /// <summary>
        /// 保存会话信息
        /// </summary>
        /// <param name="user">用户信息</param>
        /// <param name="sessionToken">会话令牌</param>
        public static async Task SaveSessionAsync(User user, string sessionToken)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"开始保存会话到文件: 用户={user.Username}, 令牌={sessionToken[..8]}...");
                
                _currentUser = user;
                _currentSessionToken = sessionToken;

                // 确保目录存在
                Directory.CreateDirectory(SessionDirectory);
                System.Diagnostics.Debug.WriteLine($"会话文件路径: {SessionFilePath}");
                
                var sessionData = $"{user.Id}|{user.Username}|{sessionToken}|{DateTime.Now:yyyy-MM-dd HH:mm:ss}";
                
                // 使用文件系统直接写入
                await File.WriteAllTextAsync(SessionFilePath, sessionData);
                
                System.Diagnostics.Debug.WriteLine($"会话保存成功: {SessionFilePath}");
                System.Diagnostics.Debug.WriteLine($"保存的数据: {sessionData}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"保存会话失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 加载本地会话并验证
        /// </summary>
        /// <returns>是否成功恢复会话</returns>
        public static async Task<bool> LoadAndValidateSessionAsync()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("开始从文件加载本地会话...");
                System.Diagnostics.Debug.WriteLine($"检查会话文件: {SessionFilePath}");
                
                if (!File.Exists(SessionFilePath))
                {
                    System.Diagnostics.Debug.WriteLine("没有找到会话文件");
                    return false;
                }

                System.Diagnostics.Debug.WriteLine($"找到会话文件: {SessionFilePath}");
                
                var sessionData = await File.ReadAllTextAsync(SessionFilePath);
                System.Diagnostics.Debug.WriteLine($"读取到会话数据: {sessionData}");
                
                var parts = sessionData.Split('|');
                
                if (parts.Length < 3)
                {
                    System.Diagnostics.Debug.WriteLine("会话数据格式错误");
                    return false;
                }

                var sessionToken = parts[2];
                System.Diagnostics.Debug.WriteLine($"提取会话令牌: {sessionToken[..8]}...");
                
                // 验证会话是否仍然有效
                System.Diagnostics.Debug.WriteLine("开始验证会话有效性...");
                var user = await AuthService.ValidateSessionAsync(sessionToken);
                
                if (user != null)
                {
                    System.Diagnostics.Debug.WriteLine($"会话验证成功: 用户={user.Username}");
                    _currentUser = user;
                    _currentSessionToken = sessionToken;
                    return true;
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("会话验证失败，清理本地会话");
                    // 会话无效，删除本地文件
                    await ClearSessionAsync();
                    return false;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"加载会话失败: {ex.Message}");
                // 加载失败，清理本地会话
                await ClearSessionAsync();
                return false;
            }
        }

        /// <summary>
        /// 清除会话信息
        /// </summary>
        public static async Task ClearSessionAsync()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("开始清除会话信息");
                
                // 如果有会话令牌，从数据库中删除
                if (!string.IsNullOrEmpty(_currentSessionToken))
                {
                    System.Diagnostics.Debug.WriteLine("从数据库注销会话");
                    await AuthService.LogoutSessionAsync(_currentSessionToken);
                }

                _currentUser = null;
                _currentSessionToken = null;

                // 删除本地会话文件
                if (File.Exists(SessionFilePath))
                {
                    System.Diagnostics.Debug.WriteLine($"删除会话文件: {SessionFilePath}");
                    File.Delete(SessionFilePath);
                    System.Diagnostics.Debug.WriteLine("会话文件删除成功");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("会话文件不存在，无需删除");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"清除会话失败: {ex.Message}");
                // 清除失败不影响主流程
            }
        }

        /// <summary>
        /// 清除内存中的会话信息（不删除数据库和本地文件）
        /// </summary>
        public static void ClearMemorySession()
        {
            System.Diagnostics.Debug.WriteLine("清除内存中的会话信息");
            _currentUser = null;
            _currentSessionToken = null;
        }

        /// <summary>
        /// 注销登录
        /// </summary>
        public static async Task LogoutAsync()
        {
            await ClearSessionAsync();
        }
    }
}