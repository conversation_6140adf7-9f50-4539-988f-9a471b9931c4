using System;
using System.IO;
using System.Text.Json;
using System.Threading.Tasks;
using App2.Models;

namespace App2.Services
{
    /// <summary>
    /// 模拟器默认配置服务
    /// 负责模拟器创建时的默认参数管理
    /// </summary>
    public static class SimulatorDefaultsService
    {
        private const string ConfigFileName = "simulator_defaults.json";
        private static readonly string ConfigDirectory = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData), "App2");
        private static readonly string ConfigFilePath = Path.Combine(ConfigDirectory, ConfigFileName);
        
        private static SimulatorDefaultConfig? _cachedConfig;

        /// <summary>
        /// 获取JSON序列化选项
        /// </summary>
        private static JsonSerializerOptions GetJsonSerializerOptions()
        {
            return new JsonSerializerOptions
            {
                WriteIndented = true,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping,
                TypeInfoResolver = AppJsonSerializerContext.Default
            };
        }

        /// <summary>
        /// 获取默认配置
        /// 如果配置文件不存在，返回系统默认值
        /// </summary>
        /// <returns>模拟器默认配置</returns>
        public static async Task<SimulatorDefaultConfig> GetDefaultsAsync()
        {
            if (_cachedConfig != null)
            {
                return _cachedConfig;
            }

            try
            {
                System.Diagnostics.Debug.WriteLine($"### GetDefaultsAsync.模拟器默认配置文件路径: {ConfigFilePath}");
                
                if (File.Exists(ConfigFilePath))
                {
                    var json = await File.ReadAllTextAsync(ConfigFilePath);
                    var options = GetJsonSerializerOptions();
                    _cachedConfig = JsonSerializer.Deserialize<SimulatorDefaultConfig>(json, options);
                    
                    if (_cachedConfig != null)
                    {
                        System.Diagnostics.Debug.WriteLine($"成功加载模拟器默认配置: {ConfigFilePath}");
                        return _cachedConfig;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"加载模拟器默认配置失败: {ex.Message}");
            }

            // 返回系统默认配置
            _cachedConfig = GetSystemDefaults();
            System.Diagnostics.Debug.WriteLine("使用系统默认模拟器配置");
            return _cachedConfig;
        }

        /// <summary>
        /// 保存默认配置
        /// </summary>
        /// <param name="config">要保存的配置</param>
        /// <returns>保存是否成功</returns>
        public static async Task<bool> SaveDefaultsAsync(SimulatorDefaultConfig config)
        {
            try
            {
                // 确保目录存在
                Directory.CreateDirectory(ConfigDirectory);

                var options = GetJsonSerializerOptions();
                var json = JsonSerializer.Serialize(config, options);
                await File.WriteAllTextAsync(ConfigFilePath, json);

                // 更新缓存
                _cachedConfig = config;

                System.Diagnostics.Debug.WriteLine($"成功保存模拟器默认配置到: {ConfigFilePath}");
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"保存模拟器默认配置失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取系统默认配置
        /// </summary>
        /// <returns>系统默认配置</returns>
        public static SimulatorDefaultConfig GetSystemDefaults()
        {
            return new SimulatorDefaultConfig
            {
                Type = "leidian", // 默认为雷电模拟器
                Performance = new PerformanceSettings
                {
                    Resolution = "900,1600,320",
                    Cpu = "2",
                    Memory = "2048"
                },
                Game = new GameSettings
                {
                    Fps = "60"
                },
                Other = new OtherSettings
                {
                    AutoRotate = "1",
                    LockWindow = "0",
                    Root = "1"
                }
            };
        }

        /// <summary>
        /// 将默认配置应用到模拟器配置
        /// </summary>
        /// <param name="defaults">默认配置</param>
        /// <returns>应用了默认值的模拟器配置</returns>
        public static SimulatorConfig ApplyDefaults(SimulatorDefaultConfig defaults)
        {
            return new SimulatorConfig
            {
                // 模拟器类型
                Type = defaults.Type,
                
                // 性能设置
                Width = defaults.Performance.Width,
                Height = defaults.Performance.Height,
                Dpi = defaults.Performance.Dpi,
                Cpu = defaults.Performance.CpuInt,
                Memory = defaults.Performance.MemoryInt,
                
                // 其他设置
                AutoRotate = defaults.Other.AutoRotateBool,
                LockWindow = defaults.Other.LockWindowBool,
                Root = defaults.Other.RootBool,
                
                // 设备信息设置使用空值（让用户自己配置）
                Manufacturer = null,
                Model = null,
                PhoneNumber = null,
                Imei = null,
                Imsi = null,
                AndroidId = null,
                SimSerial = null,
                Mac = null
            };
        }

        /// <summary>
        /// 重置为系统默认配置
        /// </summary>
        /// <returns>重置是否成功</returns>
        public static async Task<bool> ResetToSystemDefaultsAsync()
        {
            var systemDefaults = GetSystemDefaults();
            return await SaveDefaultsAsync(systemDefaults);
        }

        /// <summary>
        /// 检查配置文件是否存在
        /// </summary>
        /// <returns>配置文件是否存在</returns>
        public static bool ConfigFileExists()
        {
            return File.Exists(ConfigFilePath);
        }

        /// <summary>
        /// 获取配置文件路径
        /// </summary>
        /// <returns>配置文件完整路径</returns>
        public static string GetConfigFilePath()
        {
            return ConfigFilePath;
        }

        /// <summary>
        /// 清除缓存，强制重新加载配置
        /// </summary>
        public static void ClearCache()
        {
            _cachedConfig = null;
        }
    }
}
