using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using App2.Models;
using MySql.Data.MySqlClient;
using App2.Config;

namespace App2.Services
{
    /// <summary>
    /// ���ݿ������
    /// </summary>
    public static class DatabaseService
    {
        /// <summary>
        /// ��ȡ�û����Ա�б�
        /// </summary>
        /// <returns>�û����Ա�б�</returns>
        public static async Task<List<UserGroupMember>> GetUserGroupMembersAsync()
        {
            return await UserGroupService.GetUserGroupMembersAsync();
        }

        /// <summary>
        /// ��ȡ�û����Ա�б�֧��ɸѡ��������
        /// </summary>
        /// <param name="platform">ƽ̨ɸѡ��������Ϊ�գ�</param>
        /// <param name="source">������Դɸѡ��������Ϊ�գ�</param>
        /// <param name="nickname">�ǳ������ؼ��ʣ���Ϊ�գ�</param>
        /// <param name="sourceKeyword">��ǩ�ؼ�����������Ϊ�գ�</param>
        /// <param name="taskSource">������Դɸѡ��task_source�ֶΣ���Ϊ�գ�</param>
        /// <returns>�û����Ա�б�</returns>
        public static async Task<List<UserGroupMember>> GetUserGroupMembersAsync(string? platform = null, string? source = null, string? nickname = null, string? sourceKeyword = null, string? taskSource = null)
        {
            return await UserGroupService.GetUserGroupMembersAsync(platform, source, nickname, sourceKeyword, taskSource);
        }

        /// <summary>
        /// ɾ���û����Ա
        /// </summary>
        /// <param name="id">��ԱID</param>
        /// <returns>ɾ�����</returns>
        public static async Task<(bool Success, string Message)> DeleteUserGroupMemberAsync(int id)
        {
            return await UserGroupService.DeleteUserGroupMemberAsync(id);
        }

        /// <summary>
        /// ����ɾ���û����Ա
        /// </summary>
        /// <param name="ids">��ԱID�б�</param>
        /// <returns>ɾ�����</returns>
        public static async Task<(bool Success, string Message)> DeleteUserGroupMembersAsync(List<int> ids)
        {
            return await UserGroupService.DeleteUserGroupMembersAsync(ids);
        }

        /// <summary>
        /// �������ݿ�����
        /// </summary>
        /// <returns>�����Ƿ�ɹ�</returns>
        public static async Task<bool> TestConnectionAsync()
        {
            try
            {
                using var connection = new MySqlConnection(DatabaseConfig.ConnectionString);
                await connection.OpenAsync();
                
                // ִ��һ���򵥵Ĳ�ѯ��ȷ����������
                using var command = new MySqlCommand("SELECT 1", connection);
                await command.ExecuteScalarAsync();
                
                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// �û���¼��֤
        /// </summary>
        /// <param name="username">�û���</param>
        /// <param name="password">����</param>
        /// <param name="ipAddress">IP��ַ</param>
        /// <returns>��¼���</returns>
        public static async Task<LoginResult> ValidateLoginAsync(string username, string password, string ipAddress = "")
        {
            return await AuthService.ValidateLoginAsync(username, password, ipAddress);
        }

        /// <summary>
        /// �޸��û�����
        /// </summary>
        /// <param name="userId">�û�ID</param>
        /// <param name="oldPassword">������</param>
        /// <param name="newPassword">������</param>
        /// <returns>�޸Ľ��</returns>
        public static async Task<(bool Success, string Message)> ChangePasswordAsync(int userId, string oldPassword, string newPassword)
        {
            return await AuthService.ChangePasswordAsync(userId, oldPassword, newPassword);
        }

        /// <summary>
        /// ��ȡ�û����Ա��ҳ�б�֧��ɸѡ�������ͷ�ҳ��
        /// </summary>
        /// <param name="pageNumber">ҳ�루��1��ʼ��</param>
        /// <param name="pageSize">ÿҳ��¼��</param>
        /// <param name="platform">ƽ̨ɸѡ��������Ϊ�գ�</param>
        /// <param name="source">������Դɸѡ��������Ϊ�գ�</param>
        /// <param name="nickname">�ǳ������ؼ��ʣ���Ϊ�գ�</param>
        /// <param name="sourceKeyword">��ǩ�ؼ�����������Ϊ�գ�</param>
        /// <param name="taskSource">������Դɸѡ��task_source�ֶΣ���Ϊ�գ�</param>
        /// <returns>��ҳ�û����Ա�б�</returns>
        public static async Task<PagedResult<UserGroupMember>> GetUserGroupMembersPagedAsync(int pageNumber = 1, int pageSize = 10, string? platform = null, string? source = null, string? nickname = null, string? sourceKeyword = null, string? taskSource = null)
        {
            return await UserGroupService.GetUserGroupMembersPagedAsync(pageNumber, pageSize, platform, source, nickname, sourceKeyword, taskSource);
        }
    }
}