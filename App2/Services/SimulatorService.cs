using App2.Models;
using App2.Config;
using System;
using System.Collections.Generic;
using MySql.Data.MySqlClient;
using System.Threading.Tasks;

namespace App2.Services
{
    /// <summary>
    /// 模拟器数据服务类
    /// 提供模拟器相关的数据库操作功能
    /// </summary>
    public class SimulatorService
    {
        /// <summary>
        /// 获取所有模拟器列表
        /// </summary>
        /// <returns>返回按ID降序排列的模拟器列表</returns>
        /// <exception cref="Exception">当数据库操作失败时抛出异常</exception>
        public async Task<List<Simulator>> GetAllSimulatorsAsync()
        {
            var simulators = new List<Simulator>();
            try
            {
                using var connection = new MySqlConnection(DatabaseConfig.ConnectionString);
                await connection.OpenAsync();

                var command = new MySqlCommand("SELECT * FROM simulator ORDER BY id DESC", connection);
                using var reader = await command.ExecuteReaderAsync();

                // System.Diagnostics.Debug.WriteLine("-------------- 从数据库查询模拟器数据 --------------");
                while (await reader.ReadAsync())
                {
                    var statusValue = reader.GetInt32(reader.GetOrdinal("status"));
                    var simulator = new Simulator
                    {
                        Id = reader.GetInt32(reader.GetOrdinal("id")),
                        Title = reader.GetString(reader.GetOrdinal("title")),
                        MarikName = reader.IsDBNull(reader.GetOrdinal("marik_name")) ? null : reader.GetString(reader.GetOrdinal("marik_name")),
                        Type = reader.IsDBNull(reader.GetOrdinal("type")) ? null : reader.GetString(reader.GetOrdinal("type")),
                        Status = statusValue,
                        CreatedAt = reader.GetDateTime(reader.GetOrdinal("created_at")),
                        UpdatedAt = reader.IsDBNull(reader.GetOrdinal("updated_at")) ? null : reader.GetDateTime(reader.GetOrdinal("updated_at")),
                        Config = reader.IsDBNull(reader.GetOrdinal("config")) ? null : reader.GetString(reader.GetOrdinal("config"))
                    };
                    
                    // System.Diagnostics.Debug.WriteLine($"数据库查询 - ID: {simulator.Id}, 标题: {simulator.Title}, 原始状态值: {statusValue}, 模型状态值: {simulator.Status}, 状态文本: {simulator.StatusText}");
                    simulators.Add(simulator);
                }

                return simulators;
            }
            catch (MySqlException ex)
            {
                // 捕获数据库相关的异常并提供更详细的错误信息
                throw new Exception($"数据库操作失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 添加新的模拟器
        /// </summary>
        /// <param name="simulator">要添加的模拟器对象</param>
        /// <returns>添加成功返回true，失败返回false</returns>
        /// <exception cref="MySqlException">当数据库连接或执行失败时抛出</exception>
        /// <exception cref="ArgumentNullException">当simulator参数为null时抛出</exception>
        public async Task<bool> AddSimulatorAsync(Simulator simulator)
        {
            using var connection = new MySqlConnection(DatabaseConfig.ConnectionString);
            await connection.OpenAsync();
            
            var command = new MySqlCommand(
                "INSERT INTO simulator (title, marik_name, type, status, created_at, config) VALUES (@title, @marik_name, @type, @status, @created_at, @config)",
                connection);
            
            command.Parameters.AddWithValue("@title", simulator.Title);
            command.Parameters.AddWithValue("@marik_name", simulator.MarikName);
            command.Parameters.AddWithValue("@type", simulator.Type);
            command.Parameters.AddWithValue("@status", simulator.Status);
            command.Parameters.AddWithValue("@created_at", DateTime.Now);
            command.Parameters.AddWithValue("@config", simulator.Config);
            
            var result = await command.ExecuteNonQueryAsync();
            return result > 0;
        }

        /// <summary>
        /// 更新模拟器信息
        /// </summary>
        /// <param name="simulator">包含更新信息的模拟器对象，必须包含有效的Id</param>
        /// <returns>更新成功返回true，失败返回false</returns>
        /// <exception cref="MySqlException">当数据库连接或执行失败时抛出</exception>
        /// <exception cref="ArgumentNullException">当simulator参数为null时抛出</exception>
        /// <remarks>
        /// 此方法会更新除Id和CreatedAt之外的所有字段
        /// UpdatedAt字段会自动设置为当前时间
        /// </remarks>
        public async Task<bool> UpdateSimulatorAsync(Simulator simulator)
        {
            using var connection = new MySqlConnection(DatabaseConfig.ConnectionString);
            await connection.OpenAsync();
            
            var command = new MySqlCommand(
                "UPDATE simulator SET title = @title, marik_name = @marik_name, type = @type, status = @status, updated_at = @updated_at, config = @config WHERE id = @id",
                connection);
            
            command.Parameters.AddWithValue("@id", simulator.Id);
            command.Parameters.AddWithValue("@title", simulator.Title);
            command.Parameters.AddWithValue("@marik_name", simulator.MarikName);
            command.Parameters.AddWithValue("@type", simulator.Type);
            command.Parameters.AddWithValue("@status", simulator.Status);
            command.Parameters.AddWithValue("@updated_at", DateTime.Now);
            command.Parameters.AddWithValue("@config", simulator.Config);
            
            var result = await command.ExecuteNonQueryAsync();
            return result > 0;
        }

        /// <summary>
        /// 更新指定模拟器的配置信息
        /// </summary>
        /// <param name="id">模拟器的唯一标识ID</param>
        /// <param name="config">新的配置信息，可以为null或空字符串</param>
        /// <returns>更新成功返回true，失败返回false</returns>
        /// <exception cref="MySqlException">当数据库连接或执行失败时抛出</exception>
        /// <exception cref="ArgumentException">当id小于等于0时抛出</exception>
        /// <remarks>
        /// 此方法只更新config字段和updated_at字段
        /// 适用于只需要更新配置而不影响其他字段的场景
        /// </remarks>
        public async Task<bool> UpdateSimulatorConfigAsync(int id, string config)
        {
            using var connection = new MySqlConnection(DatabaseConfig.ConnectionString);
            await connection.OpenAsync();
            
            var command = new MySqlCommand(
                "UPDATE simulator SET config = @config, updated_at = NOW() WHERE id = @id",
                connection);
            
            command.Parameters.AddWithValue("@id", id);
            command.Parameters.AddWithValue("@config", config);
            
            var result = await command.ExecuteNonQueryAsync();
            return result > 0;
        }

        /// <summary>
        /// 根据ID获取指定的模拟器
        /// </summary>
        /// <param name="id">模拟器的唯一标识ID</param>
        /// <returns>找到则返回对应的模拟器对象，未找到返回null</returns>
        /// <exception cref="MySqlException">当数据库连接或执行失败时抛出</exception>
        /// <exception cref="ArgumentException">当id小于等于0时抛出</exception>
        public async Task<Simulator?> GetSimulatorByIdAsync(int id)
        {
            using var connection = new MySqlConnection(DatabaseConfig.ConnectionString);
            await connection.OpenAsync();
            
            var command = new MySqlCommand("SELECT * FROM simulator WHERE id = @id", connection);
            command.Parameters.AddWithValue("@id", id);
            
            using var reader = await command.ExecuteReaderAsync();
            if (await reader.ReadAsync())
            {
                return new Simulator
                {
                    Id = reader.GetInt32(reader.GetOrdinal("id")),
                    Title = reader.GetString(reader.GetOrdinal("title")),
                    MarikName = reader.IsDBNull(reader.GetOrdinal("marik_name")) ? null : reader.GetString(reader.GetOrdinal("marik_name")),
                    Type = reader.IsDBNull(reader.GetOrdinal("type")) ? null : reader.GetString(reader.GetOrdinal("type")),
                    Status = reader.GetInt32(reader.GetOrdinal("status")),
                    CreatedAt = reader.GetDateTime(reader.GetOrdinal("created_at")),
                    UpdatedAt = reader.IsDBNull(reader.GetOrdinal("updated_at")) ? null : reader.GetDateTime(reader.GetOrdinal("updated_at")),
                    Config = reader.IsDBNull(reader.GetOrdinal("config")) ? null : reader.GetString(reader.GetOrdinal("config"))
                };
            }
            
            return null;
        }

        /// <summary>
        /// 根据标识名称获取模拟器
        /// </summary>
        /// <param name="marikName">模拟器的标识名称</param>
        /// <returns>找到则返回对应的模拟器对象，未找到返回null</returns>
        /// <exception cref="MySqlException">当数据库连接或执行失败时抛出</exception>
        /// <exception cref="ArgumentException">当marikName为null或空字符串时抛出</exception>
        /// <remarks>
        /// MarikName是模拟器的业务标识符，通常用于与雷电模拟器的设备名称对应
        /// 此字段在业务逻辑中具有唯一性约束
        /// </remarks>
        public async Task<Simulator?> GetSimulatorByMarikNameAsync(string marikName)
        {
            using var connection = new MySqlConnection(DatabaseConfig.ConnectionString);
            await connection.OpenAsync();
            
            var command = new MySqlCommand("SELECT * FROM simulator WHERE marik_name = @marik_name", connection);
            command.Parameters.AddWithValue("@marik_name", marikName);
            
            using var reader = await command.ExecuteReaderAsync();
            if (await reader.ReadAsync())
            {
                return new Simulator
                {
                    Id = reader.GetInt32(reader.GetOrdinal("id")),
                    Title = reader.GetString(reader.GetOrdinal("title")),
                    MarikName = reader.IsDBNull(reader.GetOrdinal("marik_name")) ? null : reader.GetString(reader.GetOrdinal("marik_name")),
                    Type = reader.IsDBNull(reader.GetOrdinal("type")) ? null : reader.GetString(reader.GetOrdinal("type")),
                    Status = reader.GetInt32(reader.GetOrdinal("status")),
                    CreatedAt = reader.GetDateTime(reader.GetOrdinal("created_at")),
                    UpdatedAt = reader.IsDBNull(reader.GetOrdinal("updated_at")) ? null : reader.GetDateTime(reader.GetOrdinal("updated_at")),
                    Config = reader.IsDBNull(reader.GetOrdinal("config")) ? null : reader.GetString(reader.GetOrdinal("config"))
                };
            }
            
            return null;
        }

        /// <summary>
        /// 删除指定的模拟器
        /// </summary>
        /// <param name="id">要删除的模拟器ID</param>
        /// <returns>删除成功返回true，失败返回false</returns>
        /// <exception cref="MySqlException">当数据库连接或执行失败时抛出</exception>
        /// <exception cref="ArgumentException">当id小于等于0时抛出</exception>
        /// <remarks>
        /// 此操作为物理删除，删除后数据无法恢复
        /// 建议在删除前检查是否有关联的任务或其他数据
        /// </remarks>
        public async Task<bool> DeleteSimulatorAsync(int id)
        {
            using var connection = new MySqlConnection(DatabaseConfig.ConnectionString);
            await connection.OpenAsync();
            
            var command = new MySqlCommand("DELETE FROM simulator WHERE id = @id", connection);
            command.Parameters.AddWithValue("@id", id);
            
            var result = await command.ExecuteNonQueryAsync();
            return result > 0;
        }
    }
}
