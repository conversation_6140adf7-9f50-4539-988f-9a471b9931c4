using System;
using System.Text;
using System.IO;
using Windows.Storage;
using System.Collections.Generic;

namespace App2.Services
{
    /// <summary>
    /// 应用程序设置服务
    /// </summary>
    public static class SettingsService
    {
        private const string FIRST_RUN_KEY = "IsFirstRun";
        private const string LAST_LOGIN_TIME_KEY = "LastLoginTime";
        private const string APP_THEME_KEY = "AppTheme";
        private const string AUTO_START_KEY = "AutoStart";
        private const string REMEMBER_LOGIN_KEY = "RememberLogin";
        private const string SESSION_TIMEOUT_KEY = "SessionTimeout";
        private const string REMEMBER_PASSWORD_KEY = "RememberPassword";
        private const string SAVED_USERNAME_KEY = "SavedUsername";
        private const string SAVED_PASSWORD_KEY = "SavedPassword";
        private const string ENCRYPTION_KEY = "KiroApp2024SecretKey";
        private const string SIMULATOR_PATH_KEY = "SimulatorPath"; // 模拟器路径
        private const string ADB_PATH_KEY = "AdbPath"; // adb 路径

        /// <summary>
        /// 检查是否是首次运行应用
        /// </summary>
        /// <returns>是否是首次运行</returns>
        public static bool IsFirstRun()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("从文件检查是否首次运行");
                var settings = LoadSettingsFromFile();

                if (settings.TryGetValue(FIRST_RUN_KEY, out string? value) && bool.TryParse(value, out bool result))
                {
                    // result为true表示首次运行已完成，所以不是首次运行，返回false
                    // result为false表示还没有设置过，是首次运行，返回true
                    bool isFirstRun = !result;
                    System.Diagnostics.Debug.WriteLine($"从文件读取的值: {value}, 解析结果: {result}, 是否首次运行: {isFirstRun}");
                    return isFirstRun;
                }

                // 如果值不存在，则认为是首次运行
                System.Diagnostics.Debug.WriteLine("没有找到首次运行标志，设置为已完成");
                SetFirstRunCompleted();
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"检查首次运行失败: {ex.Message}");
                // 如果出现异常，默认为首次运行
                return true;
            }
        }

        /// <summary>
        /// 设置首次运行已完成
        /// </summary>
        public static void SetFirstRunCompleted()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("设置首次运行已完成到文件");

                // 读取现有设置
                var settings = LoadSettingsFromFile();

                // 更新首次运行标志 - 设置为true表示首次运行已完成
                settings[FIRST_RUN_KEY] = "true";

                // 保存到文件
                SaveSettingsToFile(settings);

                System.Diagnostics.Debug.WriteLine("首次运行标志设置成功 - 设置为true表示已完成");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"设置首次运行标志失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 保存最后登录时间
        /// </summary>
        public static void SaveLastLoginTime()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("保存最后登录时间到文��");

                // 读取现有设置
                var settings = LoadSettingsFromFile();

                // 更新最后登录时间
                settings[LAST_LOGIN_TIME_KEY] = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");

                // 保存到文件
                SaveSettingsToFile(settings);

                System.Diagnostics.Debug.WriteLine("最后登录时间保存成功");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"保存最后登录时间失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取最后登录时间
        /// </summary>
        /// <returns>最后登录时间，如果没有则返回null</returns>
        public static DateTime? GetLastLoginTime()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("从文件获取最后登录时间");
                var settings = LoadSettingsFromFile();

                if (settings.TryGetValue(LAST_LOGIN_TIME_KEY, out string? value) && DateTime.TryParse(value, out DateTime result))
                {
                    System.Diagnostics.Debug.WriteLine($"获取最后登录时间: {result}");
                    return result;
                }

                System.Diagnostics.Debug.WriteLine("没有找到最后登录时间");
                return null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取最后登录时间失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 获取应用主题设置
        /// </summary>
        /// <returns>主题设置</returns>
        public static string GetAppTheme()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("从文件获取应用主题设置");
                var settings = LoadSettingsFromFile();

                if (settings.TryGetValue(APP_THEME_KEY, out string? value))
                {
                    System.Diagnostics.Debug.WriteLine($"获取应用主题: {value}");
                    return value;
                }

                System.Diagnostics.Debug.WriteLine("没有找到主题设置，返回默认值");
                return "Default";
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取应用主题失败: {ex.Message}");
                return "Default";
            }
        }

        /// <summary>
        /// 保存应用主题设置
        /// </summary>
        /// <param name="theme">主题设置</param>
        public static void SetAppTheme(string theme)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"设置应用主题到文件: {theme}");

                // 读取现有设置
                var settings = LoadSettingsFromFile();

                // 更新主题设置
                settings[APP_THEME_KEY] = theme;

                // 保存到文件
                SaveSettingsToFile(settings);

                System.Diagnostics.Debug.WriteLine("应用主题设置保存成功");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"设置应用主题失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取自启动设置
        /// </summary>
        /// <returns>是否自启动</returns>
        public static bool GetAutoStart()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("从文件获取自启动设置");
                var settings = LoadSettingsFromFile();

                if (settings.TryGetValue(AUTO_START_KEY, out string? value) && bool.TryParse(value, out bool result))
                {
                    System.Diagnostics.Debug.WriteLine($"获取自启动设置: {result}");
                    return result;
                }

                System.Diagnostics.Debug.WriteLine("没有找到自启动设置，返回默认值false");
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取自启动设置失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 设置自启动
        /// </summary>
        /// <param name="autoStart">是否自启动</param>
        public static void SetAutoStart(bool autoStart)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"设置自启动到文件: {autoStart}");

                // 读取现有设置
                var settings = LoadSettingsFromFile();

                // 更新自启动设置
                settings[AUTO_START_KEY] = autoStart.ToString().ToLower();

                // 保存到文件
                SaveSettingsToFile(settings);

                System.Diagnostics.Debug.WriteLine("自启动设置保存成功");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"设置自启动失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取记住登��状态设置
        /// </summary>
        /// <returns>是否记住登录状态</returns>
        public static bool GetRememberLogin()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("从文件获取记住登录状态设置");
                var settings = LoadSettingsFromFile();

                if (settings.TryGetValue(REMEMBER_LOGIN_KEY, out string? value) && bool.TryParse(value, out bool result))
                {
                    System.Diagnostics.Debug.WriteLine($"获取记住登录状态: {result}");
                    return result;
                }

                System.Diagnostics.Debug.WriteLine("没有找到记住登录状态设置，返回默认值true");
                return true; // 默认为true
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取记住登录状态失败: {ex.Message}");
                return true;
            }
        }

        /// <summary>
        /// 设置记住登录状态
        /// </summary>
        /// <param name="remember">是否记住登录状态</param>
        public static void SetRememberLogin(bool remember)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"设置记住登录状态到文件: {remember}");

                // 读取现有设置
                var settings = LoadSettingsFromFile();

                // 更新记住登录状态设置
                settings[REMEMBER_LOGIN_KEY] = remember.ToString().ToLower();

                // 保存到文件
                SaveSettingsToFile(settings);

                System.Diagnostics.Debug.WriteLine("记住登录状态设置保存成功");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"设置记住登录状态失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取会话超时时间
        /// </summary>
        /// <returns>会话超时时间（天）</returns>
        public static int GetSessionTimeout()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("从文件获取会话超时时间");
                var settings = LoadSettingsFromFile();

                if (settings.TryGetValue(SESSION_TIMEOUT_KEY, out string? value) && int.TryParse(value, out int result))
                {
                    System.Diagnostics.Debug.WriteLine($"获取会话超时时间: {result}天");
                    return result;
                }

                System.Diagnostics.Debug.WriteLine("没有找到会话超时时间设置，返回默认值7天");
                return 7;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取会话超时时间失败: {ex.Message}");
                return 7;
            }
        }

        /// <summary>
        /// 设置会话超时时间
        /// </summary>
        /// <param name="days">超时时间（天）</param>
        public static void SetSessionTimeout(int days)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"设置会话超时时间到文件: {days}天");

                // 读取现有设置
                var settings = LoadSettingsFromFile();

                // 更新会话超时时间设置
                settings[SESSION_TIMEOUT_KEY] = days.ToString();

                // 保存到文件
                SaveSettingsToFile(settings);

                System.Diagnostics.Debug.WriteLine("会话超时时间设置保存成功");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"设置会话超时时间失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取是否记住密码
        /// </summary>
        /// <returns>是否记住密码</returns>
        public static bool GetRememberPassword()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("从文件获取记住密码设置");
                var settings = LoadSettingsFromFile();

                if (settings.TryGetValue(REMEMBER_PASSWORD_KEY, out string? value) && bool.TryParse(value, out bool result))
                {
                    System.Diagnostics.Debug.WriteLine($"获取记住密码设置: {result}");
                    return result;
                }

                System.Diagnostics.Debug.WriteLine("没有找到记住密码设置，返回默认值false");
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取记住密码设置失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 设置是否记住密码
        /// </summary>
        /// <param name="remember">是否记住密码</param>
        public static void SetRememberPassword(bool remember)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"设置记住密码到文件: {remember}");

                // 读取现有设置
                var settings = LoadSettingsFromFile();

                // 更新记住密码设置
                settings[REMEMBER_PASSWORD_KEY] = remember.ToString().ToLower();

                if (!remember)
                {
                    // 如��不记住密码，清除保存的用户名和密码
                    settings.Remove(SAVED_USERNAME_KEY);
                    settings.Remove(SAVED_PASSWORD_KEY);
                    System.Diagnostics.Debug.WriteLine("已清除保存的凭据");
                }

                // 保存到文件
                SaveSettingsToFile(settings);

                System.Diagnostics.Debug.WriteLine("记住密码设置保存成功");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"设置记住密码失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取模拟器路径配置
        /// </summary>
        /// <returns>模拟器路径</returns>
        public static string? GetSimulatorPath()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("从文件获取模拟器路径设置");
                var settings = LoadSettingsFromFile();

                if (settings.TryGetValue(SIMULATOR_PATH_KEY, out string? value) && !string.IsNullOrEmpty(value))
                {
                    System.Diagnostics.Debug.WriteLine($"获取到模拟器路径: {value}");
                    return value;
                }

                System.Diagnostics.Debug.WriteLine("没有找到模拟器路径设置");
                return null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取模拟器路径失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 保存模拟器路径设置
        /// </summary>
        /// <param name="path">模拟器路径</param>
        public static void SetSimulatorPath(string path)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"设置模拟器路径到文件: {path}");

                // 读取现有设置
                var settings = LoadSettingsFromFile();

                // 更新模拟器路径设置
                settings[SIMULATOR_PATH_KEY] = path;

                // 保存到文件
                SaveSettingsToFile(settings);

                System.Diagnostics.Debug.WriteLine("模拟器路径设置保存成功");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"设置模拟器路径失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 清除模拟器路径设置
        /// </summary>
        public static void ClearSimulatorPath()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("清除模拟器路径设置");

                // 读取现有设置
                var settings = LoadSettingsFromFile();

                // 移除模拟器路径设置
                if (settings.ContainsKey(SIMULATOR_PATH_KEY))
                {
                    settings.Remove(SIMULATOR_PATH_KEY);
                }

                // 保存到���件
                SaveSettingsToFile(settings);

                System.Diagnostics.Debug.WriteLine("模拟器路径设置清除成功");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"清除模拟器路径失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取ADB路径配置
        /// </summary>
        /// <returns>ADB路径</returns>
        public static string? GetAdbPath()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("从文件获取ADB路径设置");
                var settings = LoadSettingsFromFile();

                if (settings.TryGetValue(ADB_PATH_KEY, out string? value) && !string.IsNullOrEmpty(value))
                {
                    System.Diagnostics.Debug.WriteLine($"获取到ADB路径: {value}");
                    return value;
                }

                System.Diagnostics.Debug.WriteLine("没有找到ADB路径设置");
                return null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取ADB路径失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 保存ADB路径设置
        /// </summary>
        /// <param name="path">ADB路径</param>
        public static void SetAdbPath(string path)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"设���ADB路径到文件: {path}");

                // 读取现有设置
                var settings = LoadSettingsFromFile();

                // 更新ADB路径设置
                settings[ADB_PATH_KEY] = path;

                // 保存到文件
                SaveSettingsToFile(settings);

                System.Diagnostics.Debug.WriteLine("ADB路径设置保存成功");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"设置ADB路径失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 清除ADB路径设置
        /// </summary>
        public static void ClearAdbPath()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("清除ADB路径设置");

                // 读取现有设置
                var settings = LoadSettingsFromFile();

                // 移除ADB路径设置
                if (settings.ContainsKey(ADB_PATH_KEY))
                {
                    settings.Remove(ADB_PATH_KEY);
                }

                // 保存到文件
                SaveSettingsToFile(settings);

                System.Diagnostics.Debug.WriteLine("ADB路径设置清除成功");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"清除ADB路径失败: {ex.Message}");
            }
        }

        // 文件系统设置存储路径
        private static readonly string SettingsDirectory = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData), "App2");
        private static readonly string SettingsFilePath = Path.Combine(SettingsDirectory, "settings.dat");

        /// <summary>
        /// 保存用户凭据
        /// </summary>
        /// <param name="username">用户名</param>
        /// <param name="password">密码</param>
        public static void SaveCredentials(string username, string password)
        {
            try
            {
                // 参数验证
                if (string.IsNullOrWhiteSpace(username) || string.IsNullOrWhiteSpace(password))
                {
                    System.Diagnostics.Debug.WriteLine("用户名或密码为空，无法保存凭据");
                    return;
                }

                System.Diagnostics.Debug.WriteLine($"开始保存凭据到文件 - 用户名: {username}");

                // 确保目录存在
                Directory.CreateDirectory(SettingsDirectory);

                // 先加密密码
                string encryptedPassword = EncryptPassword(password);
                if (string.IsNullOrEmpty(encryptedPassword))
                {
                    System.Diagnostics.Debug.WriteLine("密码加密失败，无法保存凭据");
                    return;
                }

                // 读取现有设置
                var settings = LoadSettingsFromFile();

                // 更新凭据设置
                settings[SAVED_USERNAME_KEY] = username;
                settings[SAVED_PASSWORD_KEY] = encryptedPassword;
                settings[REMEMBER_PASSWORD_KEY] = "true";

                // 保存到文件
                SaveSettingsToFile(settings);

                System.Diagnostics.Debug.WriteLine($"凭据保存成功到文件 - 加密密码长度: {encryptedPassword.Length}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"保��凭据到文件失败: {ex.GetType().Name} - {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"堆栈跟踪: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// 获取保存的用户名
        /// </summary>
        /// <returns>保存的用户名</returns>
        public static string? GetSavedUsername()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("从文件获取保存的用户名");
                var settings = LoadSettingsFromFile();

                if (settings.TryGetValue(SAVED_USERNAME_KEY, out string? username))
                {
                    System.Diagnostics.Debug.WriteLine($"获取保存的用户名: {username}");
                    return username;
                }

                System.Diagnostics.Debug.WriteLine("没有找到保存的用户名");
                return null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取保存的用户名失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 获取保存的密码
        /// </summary>
        /// <returns>保存的密码</returns>
        public static string? GetSavedPassword()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("从文件获取保存的密码");
                var settings = LoadSettingsFromFile();

                if (settings.TryGetValue(SAVED_PASSWORD_KEY, out string? encryptedPassword) && !string.IsNullOrEmpty(encryptedPassword))
                {
                    System.Diagnostics.Debug.WriteLine($"获取保存的密码 - 加密值长度: {encryptedPassword.Length}");
                    string decryptedPassword = DecryptPassword(encryptedPassword);
                    System.Diagnostics.Debug.WriteLine($"密码解密结果: {(string.IsNullOrEmpty(decryptedPassword) ? "空" : "有值")}");
                    return decryptedPassword;
                }

                System.Diagnostics.Debug.WriteLine("没有找到保存的密码");
                return null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取保存的密码失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 清除保存的凭据
        /// </summary>
        public static void ClearSavedCredentials()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("从文件清除保存的凭据");

                // 读取现有设置
                var settings = LoadSettingsFromFile();

                // 清除凭据相关设置
                settings.Remove(SAVED_USERNAME_KEY);
                settings.Remove(SAVED_PASSWORD_KEY);
                settings[REMEMBER_PASSWORD_KEY] = "false";

                // 保存到文件
                SaveSettingsToFile(settings);

                System.Diagnostics.Debug.WriteLine("凭据清除成功");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"清除凭据失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 清除所有设置
        /// </summary>
        public static void ClearAllSettings()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("清除所有设置文件");

                if (File.Exists(SettingsFilePath))
                {
                    File.Delete(SettingsFilePath);
                    System.Diagnostics.Debug.WriteLine("设置文件删除成功");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("设置文件不存在，无需删除");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"清除所有设置失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 加密密码
        /// </summary>
        /// <param name="password">明文密码</param>
        /// <returns>加密后的密码</returns>
        private static string EncryptPassword(string password)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"开始加密密码: {password}");

                byte[] data = Encoding.UTF8.GetBytes(password);
                byte[] key = Encoding.UTF8.GetBytes(ENCRYPTION_KEY);

                // 使用简单的XOR加密
                for (int i = 0; i < data.Length; i++)
                {
                    data[i] = (byte)(data[i] ^ key[i % key.Length]);
                }

                string result = Convert.ToBase64String(data);
                System.Diagnostics.Debug.WriteLine($"加密结果: {result}");
                return result;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"加密失败: {ex.Message}");
                // 加密失败，返回原密码（不推荐，但保证功能可用）
                return password;
            }
        }

        /// <summary>
        /// 解密密码
        /// </summary>
        /// <param name="encryptedPassword">加密的密码</param>
        /// <returns>明文密码</returns>
        private static string DecryptPassword(string encryptedPassword)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"开始解密密码: {encryptedPassword}");

                byte[] data = Convert.FromBase64String(encryptedPassword);
                byte[] key = Encoding.UTF8.GetBytes(ENCRYPTION_KEY);

                // 使用XOR解密
                for (int i = 0; i < data.Length; i++)
                {
                    data[i] = (byte)(data[i] ^ key[i % key.Length]);
                }

                string result = Encoding.UTF8.GetString(data);
                System.Diagnostics.Debug.WriteLine($"解密结果: {result}");
                return result;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"解密失败: {ex.Message}");
                // 解密失败，返回空字符串
                return string.Empty;
            }
        }

        /// <summary>
        /// 从文件加载设置
        /// </summary>
        /// <returns>设置字典</returns>
        private static Dictionary<string, string> LoadSettingsFromFile()
        {
            var settings = new Dictionary<string, string>();

            try
            {
                if (File.Exists(SettingsFilePath))
                {
                    System.Diagnostics.Debug.WriteLine($"从文件加载设置: {SettingsFilePath}");
                    string[] lines = File.ReadAllLines(SettingsFilePath);

                    foreach (string line in lines)
                    {
                        if (!string.IsNullOrWhiteSpace(line) && line.Contains('='))
                        {
                            int equalIndex = line.IndexOf('=');
                            string key = line.Substring(0, equalIndex).Trim();
                            string value = line.Substring(equalIndex + 1).Trim();
                            settings[key] = value;
                        }
                    }

                    System.Diagnostics.Debug.WriteLine($"成功加载 {settings.Count} 个设置项");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("设置文件不存在，返回空设置");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"加载设置文件失败: {ex.Message}");
            }

            return settings;
        }

        /// <summary>
        /// 保存设置到文件
        /// </summary>
        /// <param name="settings">设置字典</param>
        private static void SaveSettingsToFile(Dictionary<string, string> settings)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"保存设置到文件: {SettingsFilePath}");

                var lines = new List<string>();
                foreach (var kvp in settings)
                {
                    lines.Add($"{kvp.Key}={kvp.Value}");
                }

                File.WriteAllLines(SettingsFilePath, lines);
                System.Diagnostics.Debug.WriteLine($"成功保存 {settings.Count} 个设置项到文件");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"保存设置文件失败: {ex.Message}");
                throw;
            }
        }
    }
}