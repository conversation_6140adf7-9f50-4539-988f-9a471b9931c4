using System;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.IO;
using System.Diagnostics;
using App2.Models;
using App2.Utils;
using System.Text.Json;
using MySql.Data.MySqlClient;
using App2.Config;

namespace App2.Services
{
    /// <summary>
    /// 模拟器管理服务 封装（对内提供相关流程操作）
    /// 提供模拟器创建、检测等封装功能
    /// </summary>
    public class SimulatorManagerService
    {
        private readonly string _leidianPath;
        private readonly SimulatorService _simulatorService;
        
        // 任务的配置文件
        private const string TaskConfigFileName = "task_config.json";
        private static readonly string TaskConfigDirectory = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData), "App2");
        private static readonly string TaskConfigFilePath = Path.Combine(TaskConfigDirectory, TaskConfigFileName);


        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="leidianPath">雷电模拟器路径 (dnconsole.exe的路径)</param>
        public SimulatorManagerService(string leidianPath)
        {
            _leidianPath = leidianPath;
            _simulatorService = new SimulatorService();
        }

        /// <summary>
        /// ## 创建用户模拟器 ##
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="bindUser">创建后是否绑定账户</param>
        /// <returns>操作结果</returns>
        public async Task<ApiResponse<object>> CreateUserSimulatorAsync(int userId, bool bindUser=true)
        {
            string simulatorName = $"simulator_{userId}";
            try
            {
                // 1. 判断用户模拟器是否已存在
                bool exists = await CheckSimulatorExistsAsync(simulatorName);
                
                if (exists)
                {
                    return ApiResponse<object>.Error($"模拟器 '{simulatorName}' 已存在", false);
                }

                // 2. 获取默认配置
                var defaultConfig = await SimulatorDefaultsService.GetDefaultsAsync();
                if (defaultConfig == null)
                {
                    return ApiResponse<object>.Error("获取默认配置失败", false);
                }

                // 3. 调用雷电模拟器创建命令
                await LeidianToolsNew.AddAsync(_leidianPath, simulatorName);
                var existsType = await CheckSimulatorExistsAsync(simulatorName);
                if (!existsType)
                {
                    return ApiResponse<object>.Error($"创建模拟器失败", false);
                }
                
                var parameters = new Dictionary<string, object>
                {
                    { "resolution", defaultConfig.Performance.Resolution },
                    { "cpu", defaultConfig.Performance.Cpu },
                    { "memory", defaultConfig.Performance.Memory },
                    // { "fps", defaultConfig.Game.Fps }, // TODO: 命令执行有问题
                    { "autorotate", defaultConfig.Other.AutoRotate },
                    { "lockwindow", defaultConfig.Other.LockWindow },
                    { "root", defaultConfig.Other.Root }
                };
                var result = await LeidianToolsNew.ModifyAsync(_leidianPath, simulatorName, parameters);
                // TODO: 修改完后，查询修改配置是否修改成功。
                
                // if (!result.Success)
                // {
                //     return ApiResponse<bool>.Error($"模拟器配置修改失败: {result.Error}", false);
                // }

                if (bindUser == false)
                {
                    return ApiResponse<object>.Success("", "模拟器创建成功");
                }

                // 4. 查询用户账号信息
                string accountName;
                using (var connection = new MySqlConnection(DatabaseConfig.ConnectionString))
                {
                    await connection.OpenAsync();
                    var command = new MySqlCommand("SELECT account_name FROM user_account WHERE id = @id", connection);
                    command.Parameters.AddWithValue("@id", userId);
                    
                    var accountNameObj = await command.ExecuteScalarAsync();
                    accountName = accountNameObj?.ToString() ?? "未命名账号";
                }
                
                // 5. 保存模拟器信息到数据库
                var simulator = new Simulator
                {
                    Title = accountName,
                    MarikName = simulatorName,
                    Type = defaultConfig.Type,
                    Status = 1, // 1 表示正常状态
                    CreatedAt = DateTime.Now,
                    Config = JsonSerializer.Serialize(defaultConfig, AppJsonSerializerContext.Default.SimulatorDefaultConfig)
                };
                
                bool added = await _simulatorService.AddSimulatorAsync(simulator);
                if (!added)
                {
                    return ApiResponse<object>.Error("保存模拟器信息失败", false);
                }
                
                // 获取新添加的模拟器ID
                int simulatorId;
                using (var connection = new MySqlConnection(DatabaseConfig.ConnectionString))
                {
                    await connection.OpenAsync();
                    var command = new MySqlCommand("SELECT id FROM simulator WHERE marik_name = @marikName ORDER BY id DESC LIMIT 1", connection);
                    command.Parameters.AddWithValue("@marikName", simulatorName);
                    
                    var idObj = await command.ExecuteScalarAsync();
                    if (idObj == null || !int.TryParse(idObj.ToString(), out simulatorId))
                    {
                        return ApiResponse<object>.Error("无法获取新创建的模拟器ID", false);
                    }
                }
                
                // 6. 更新用户账号表中的simulator_id
                using (var connection = new MySqlConnection(DatabaseConfig.ConnectionString))
                {
                    await connection.OpenAsync();
                    var command = new MySqlCommand("UPDATE user_account SET simulator_id = @simulatorId WHERE id = @userId", connection);
                    command.Parameters.AddWithValue("@simulatorId", simulatorId);
                    command.Parameters.AddWithValue("@userId", userId);
                    
                    await command.ExecuteNonQueryAsync();
                }

                return ApiResponse<object>.Success(simulatorId, "模拟器创建成功");
            }
            catch (Exception ex)
            {
                await LeidianToolsNew.RemoveAsync(_leidianPath, simulatorName);
                return ApiResponse<object>.Error($"创建模拟器时发生错误: {ex.Message}", false);
            }
        }

        /// <summary>
        /// ## 检测用户模拟器是否存在 ## 
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>操作结果，数据为是否存在</returns>
        public async Task<ApiResponse<bool>> CheckUserSimulatorAsync(int userId)
        {
            try
            {
                string simulatorName = $"simulator_{userId}";
                bool exists = await CheckSimulatorExistsAsync(simulatorName);
                if (exists)
                {
                    return ApiResponse<bool>.Success(exists, "已存在");
                }
                else
                {
                    return ApiResponse<bool>.Error("不存在");
                }
            }
            catch (Exception ex)
            {
                return ApiResponse<bool>.Error($"检测模拟器时发生错误: {ex.Message}", false);
            }
        }

        /// <summary>
        /// 检查模拟器是否存在
        /// </summary>
        /// <param name="simulatorName">模拟器名称</param>
        /// <returns>是否存在</returns>
        private async Task<bool> CheckSimulatorExistsAsync(string simulatorName)
        {
            // 调用雷电模拟器的list命令获取所有模拟器列表
            var result = await LeidianToolsNew.ExecuteAsync(_leidianPath, "list");
            if (!result.Success)
            {
                throw new Exception($"无法获取模拟器列表: {result.Error}");
            }

            // 解析输出并检查是否有匹配的模拟器名称
            var lines = result.Output.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);
            foreach (var line in lines)
            {
                if (line.Contains(simulatorName, StringComparison.OrdinalIgnoreCase))
                {
                    return true;
                }
            }
            
            return false;
        }

        /// <summary>
        /// ## 任务初始化流程 ##
        /// </summary>
        /// <param name="taskJson">任务内容JSON字符串</param>
        /// <param name="userId">用户ID</param>
        /// <returns>操作结果</returns>
        public async Task<ApiResponse<object>> InitializeTaskAsync(string taskJson, int userId)
        {
            // taskJson = {"Account":{"SimulatorId":8,"LoginAccount":"foodie_xiaoli","Password":"","ProxyId":""},"TaskType":{"Id":1,"Name":"关注>私信任务","UserNum":100,"IntervalTimeMin":10,"IntervalTimeMax":30,"CountTimeMin":0,"CountTimeMax":0,"FansNum":100,"Content":"","IsViewChecked":false,"IsCommentChecked":false,"IsLikeChecked":false},"Id":41,"Platform":"Instagram","SearchUrl":"帖子下留言的用户","Keywords":"王者荣耀 极速飞车","Content":"xxxxxx"}
            string simulatorName = $"simulator_{userId}";
            try
            {
                // 1. 判断模拟器是否存在，不存在则创建
                bool simulatorExists = await CheckSimulatorExistsAsync(simulatorName);
                if (!simulatorExists)
                {
                    var createResult = await CreateUserSimulatorAsync(userId, false);
                    if (createResult.Code < 0)
                    {
                        return ApiResponse<object>.Error($"创建模拟器失败: {createResult.Message}");
                    }
                    
                    // 等待模拟器创建完成
                    await Task.Delay(2000);
                    
                    // 再次检查模拟器是否创建成功
                    bool createdSuccessfully = await CheckSimulatorExistsAsync(simulatorName);
                    if (!createdSuccessfully)
                    {
                        return ApiResponse<object>.Error("模拟器创建后验证失败");
                    }
                }
                
                // 2. 本地写入任务内容JSON到文件
                var writeResult = await WriteTaskConfigToFileAsync(taskJson);
                if (writeResult.Code < 0)
                {
                    return ApiResponse<object>.Error($"写入任务配置文件失败: {writeResult.Message}");
                }
        
                // 3. 启动模拟器，间隔3秒后再推送文件到模拟器内
                var launchResult = await LeidianToolsNew.LaunchAsync(_leidianPath, simulatorName);
                if (!launchResult.Success)
                {
                    return ApiResponse<object>.Error($"启动模拟器失败: {launchResult.Error}");
                }
                
                // 等待模拟器启动完成（最多等待60秒）
                bool isStarted = await WaitForSimulatorStartupAsync(simulatorName, 60);
                if (!isStarted)
                {
                    return ApiResponse<object>.Error("模拟器启动超时，请检查模拟器状态");
                }
                
                    
                // 4. 模拟器界面排列
                await LeidianToolsNew.SortWndAsync(_leidianPath);
                
                // 推送文件到模拟器
                var pushResult = await PushTaskConfigToSimulatorAsync(simulatorName);
                if (pushResult.Code < 0)
                {
                    return ApiResponse<object>.Error($"推送配置文件到模拟器失败: {pushResult.Message}");
                }
        
                // 5. 安装相关软件
                var installResult = await InstallRequiredAppsAsync(simulatorName);
                if (installResult.Code < 0)
                {
                    return ApiResponse<object>.Error($"安装相关软件失败: {installResult.Message}");
                }
        
                // 6. 设置开启脚本相关权限
                var permissionResult = await SetScriptPermissionsAsync(simulatorName);
                if (permissionResult.Code < 0)
                {
                    return ApiResponse<object>.Error($"设置脚本权限失败: {permissionResult.Message}");
                }
        
                // 7. 运行脚本软件
                var runResult = await RunScriptAppAsync(simulatorName);
                if (runResult.Code < 0)
                {
                    return ApiResponse<object>.Error($"运行脚本软件失败: {runResult.Message}");
                }
            
                return ApiResponse<object>.Success(new { status = "completed" }, "任务初始化完成");
            }
            catch (Exception ex)
            {
                return ApiResponse<object>.Error($"任务初始化时发生错误: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 写入任务配置到本地文件
        /// </summary>
        /// <param name="taskJson">任务JSON内容</param>
        /// <returns>操作结果</returns>
        private async Task<ApiResponse<object>> WriteTaskConfigToFileAsync(string taskJson)
        {
            try
            {
                // 确保目录存在
                if (!Directory.Exists(TaskConfigDirectory))
                {
                    Directory.CreateDirectory(TaskConfigDirectory);
                }
        
                // 清空文件内容并写入新内容
                // var escapedJson = taskJson.Replace("\"", "\\\"");
                var command = $"echo {taskJson} > \"{TaskConfigFilePath}\"";
                
                var processInfo = new ProcessStartInfo("cmd.exe", $"/c {command}")
                {
                    CreateNoWindow = true,
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true
                };
        
                using var process = Process.Start(processInfo);
                if (process == null)
                {
                    return ApiResponse<object>.Error("无法启动写入进程");
                }
        
                await process.WaitForExitAsync();
        
                // 验证文件是否写入成功
                if (!File.Exists(TaskConfigFilePath))
                {
                    return ApiResponse<object>.Error("配置文件写入后不存在");
                }
        
                return ApiResponse<object>.Success(new { filePath = TaskConfigFilePath }, "配置文件写入成功");
            }
            catch (Exception ex)
            {
                return ApiResponse<object>.Error($"写入配置文件时发生错误: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 推送任务配置到模拟器
        /// </summary>
        /// <param name="simulatorName">模拟器名称</param>
        /// <returns>操作结果</returns>
        private async Task<ApiResponse<object>> PushTaskConfigToSimulatorAsync(string simulatorName)
        {
            try
            {
                // 推送文件到模拟器
                var pushResult = await LeidianToolsNew.AdbAsync(_leidianPath, simulatorName, $"push \"{TaskConfigFilePath}\" /sdcard/task_config.json");
        
                if (!pushResult.Success)
                {
                    return ApiResponse<object>.Error($"推送文件失败: {pushResult.Error}");
                }
        
                // 检查推送结果
                if (pushResult.Output.Contains("adb: error") || pushResult.Output.Contains("No such file"))
                {
                    return ApiResponse<object>.Error($"推送文件失败: {pushResult.Output}");
                }
        
                // 检测推送内容是否成功
                var checkResult = await LeidianToolsNew.AdbAsync(_leidianPath, simulatorName, "shell cat /sdcard/task_config.json");
        
                if (!checkResult.Success || checkResult.Output.Contains("No such file"))
                {
                    return ApiResponse<object>.Error("文件推送验证失败");
                }
        
                // 验证推送的内容是否为有效JSON
                try
                {
                    JsonDocument.Parse(checkResult.Output);
                }
                catch (JsonException)
                {
                    return ApiResponse<object>.Error("推送的文件内容不是有效的JSON格式");
                }
        
                return ApiResponse<object>.Success(new { targetPath = "/sdcard/task_config.json" }, "文件推送成功");
            }
            catch (Exception ex)
            {
                return ApiResponse<object>.Error($"推送文件时发生错误: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 安装相关软件
        /// </summary>
        /// <param name="simulatorName">模拟器名称</param>
        /// <returns>操作结果</returns>
        private async Task<ApiResponse<object>> InstallRequiredAppsAsync(string simulatorName)
        {
            try
            {
                // 获取项目根目录下的 Assets/Apk 文件夹路径
                string projectRoot = Directory.GetCurrentDirectory();
                string apkDirectory = Path.Combine(projectRoot, "Assets", "Apk");

                var appsToInstall = new[]
                {
                    new { FileName = Path.Combine(apkDirectory, "search_task.apk"), PackageName = "com.example.yanghao" },
                    new { FileName = Path.Combine(apkDirectory, "instagram.apk"), PackageName = "com.instagram.android" },
                    new { FileName = Path.Combine(apkDirectory, "shadowrocket.apk"), PackageName = "com.v2cross.proxy" },
                    new { FileName = Path.Combine(apkDirectory, "search_vpn.apk"), PackageName = "com.example.vpn" }
                };

                var installedApps = new List<string>();
                foreach (var app in appsToInstall)
                {
                    // 检查APK文件是否存在
                    if (!File.Exists(app.FileName))
                    {
                        return ApiResponse<object>.Error($"APK文件不存在: {app.FileName}");
                    }
                    
                    var checkResult = await LeidianToolsNew.AdbAsync(_leidianPath, simulatorName, $"shell pm list packages | grep {app.PackageName}");
                    if (checkResult.Success && checkResult.Output.Contains($"package:{app.PackageName}"))
                    {
                        installedApps.Add(app.PackageName);
                        continue;
                    }

                    // 安装应用
                    await LeidianToolsNew.InstallAppAsync(_leidianPath, simulatorName, app.FileName, app.PackageName);
                    
                    // 检测安装是否成功 - 重试机制
                    bool isInstalled = await WaitForAppInstallationAsync(simulatorName, app.PackageName, 60);
                    if (!isInstalled)
                    {
                        return ApiResponse<object>.Error($"应用 {app.PackageName} 安装超时或失败");
                    }

                    installedApps.Add(app.PackageName);
                }

                return ApiResponse<object>.Success(new { installedApps }, "所有应用安装成功");
            }
            catch (Exception ex)
            {
                return ApiResponse<object>.Error($"安装应用时发生错误: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 设置脚本相关权限
        /// </summary>
        /// <param name="simulatorName">模拟器名称</param>
        /// <returns>操作结果</returns>
        private async Task<ApiResponse<object>> SetScriptPermissionsAsync(string simulatorName)
        {
            try
            {
                var checkResult = await LeidianToolsNew.AdbAsync(_leidianPath, simulatorName, "shell settings get secure enabled_accessibility_services");
                if (checkResult.Success && checkResult.Output.Contains("com.example.yanghao/com.stardust.autojs.core.accessibility.AccessibilityService:com.example.vpn/com.stardust.autojs.core.accessibility.AccessibilityService"))
                {
                    System.Diagnostics.Debug.WriteLine("---- 脚本权限已设置 ----");
                    return ApiResponse<object>.Success("", "脚本权限设置完成");
                }
                    
                var permissionCommands = new[]
                {
                    "shell settings put secure enabled_accessibility_services com.example.yanghao/com.stardust.autojs.core.accessibility.AccessibilityService:com.example.vpn/com.stardust.autojs.core.accessibility.AccessibilityService",
                    "sleep",
                    "sleep",
                    "sleep",
                    "sleep",
                    "shell input tap 600 1250",
                    "shell input tap 600 1350",
                    "sleep",
                    "shell input tap 600 1250",
                    "shell input tap 600 1350",
                    "sleep",
                    "shell pm grant com.example.yanghao android.permission.READ_EXTERNAL_STORAGE",
                    "shell pm grant com.example.yanghao android.permission.WRITE_EXTERNAL_STORAGE",
                    "shell pm grant com.example.yanghao android.permission.READ_PHONE_STATE",
                    "shell appops set com.example.yanghao SYSTEM_ALERT_WINDOW allow",
                    
                    "shell pm grant com.example.vpn android.permission.READ_EXTERNAL_STORAGE",
                    "shell pm grant com.example.vpn android.permission.WRITE_EXTERNAL_STORAGE",
                    "shell pm grant com.example.vpn android.permission.READ_PHONE_STATE",
                    "shell appops set com.example.vpn SYSTEM_ALERT_WINDOW allow",

                    "sleep",
                    "shell pm grant com.instagram.android android.permission.READ_EXTERNAL_STORAGE",
                    "shell pm grant com.instagram.android android.permission.WRITE_EXTERNAL_STORAGE",
                };
        
                foreach (var command in permissionCommands)
                {
                    if (command == "sleep")
                    {
                        await Task.Delay(1000); // 等待1秒
                        continue; // 跳过休眠命令
                    }
                    await LeidianToolsNew.AdbAsync(_leidianPath, simulatorName, command);
                }
        
                System.Diagnostics.Debug.WriteLine("---- 脚本权限设置完成 ----");
                return ApiResponse<object>.Success(new { permissionsSet = permissionCommands.Length }, "脚本权限设置完成");
            }
            catch (Exception ex)
            {
                return ApiResponse<object>.Error($"设置脚本权限时发生错误: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 运行脚本软件
        /// </summary>
        /// <param name="simulatorName">模拟器名称</param>
        /// <returns>操作结果</returns>
        private async Task<ApiResponse<object>> RunScriptAppAsync(string simulatorName)
        {
            try
            {
                await LeidianToolsNew.RunAppAsync(_leidianPath, simulatorName, "com.example.vpn");
                System.Diagnostics.Debug.WriteLine("---- 等待VPN启动完成 ----");
                await Task.Delay(40000);
                
                var runResult = await LeidianToolsNew.RunAppAsync(_leidianPath, simulatorName, "com.example.yanghao");
                if (!runResult.Success)
                {
                    return ApiResponse<object>.Error($"启动脚本应用失败: {runResult.Error}");
                }
        
                return ApiResponse<object>.Success(new { appPackage = "com.example.yanghao" }, "脚本软件启动完成");
            }
            catch (Exception ex)
            {
                return ApiResponse<object>.Error($"运行脚本软件时发生错误: {ex.Message}");
            }
        }
        
        
        /// <summary>
        /// 检测模拟器是否存在 - TODO: 没啥用，可以删除
        /// </summary>
        /// <param name="simulatorName">模拟器名称</param>
        /// <returns>是否运行中</returns>
        private async Task<bool> IsSimulatorRunningAsync(string simulatorName)
        {
            try
            {
                var result = await LeidianToolsNew.ExecuteAsync(_leidianPath, "list2");
                if (!result.Success)
                {
                    return false;
                }

                var lines = result.Output.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);
                foreach (var line in lines)
                {
                    if (line.Contains(simulatorName, StringComparison.OrdinalIgnoreCase) && 
                        line.Contains("正在运行", StringComparison.OrdinalIgnoreCase))
                    {
                        return true;
                    }
                }

                return false;
            }
            catch
            {
                return false;
            }
        }
        
        /// <summary>
        /// 等待模拟器启动完成
        /// </summary>
        /// <param name="simulatorName">模拟器名称</param>
        /// <param name="maxWaitTimeSeconds">最大等待时间（秒）</param>
        /// <returns>是否启动成功</returns>
        private async Task<bool> WaitForSimulatorStartupAsync(string simulatorName, int maxWaitTimeSeconds = 60)
        {
            int elapsedSeconds = 0;
    
            while (elapsedSeconds < maxWaitTimeSeconds)
            {
                var result = await LeidianToolsNew.AdbAsync(_leidianPath, simulatorName, "shell echo true");
                if (result.Success && result.Output == "true")
                {
                    return true;
                }
                
                await Task.Delay(3000); // 等待3秒
                elapsedSeconds += 3;
            }
    
            return false; // 超时未启动
        }
        
        /// <summary>
        /// 等待应用安装完成
        /// </summary>
        /// <param name="simulatorName">模拟器名称</param>
        /// <param name="packageName">应用包名</param>
        /// <param name="maxWaitTimeSeconds">最大等待时间（秒）</param>
        /// <returns>是否安装成功</returns>
        private async Task<bool> WaitForAppInstallationAsync(string simulatorName, string packageName, int maxWaitTimeSeconds = 20)
        {
            int elapsedSeconds = 0;
            const int checkIntervalSeconds = 2; // 每2秒检测一次

            while (elapsedSeconds < maxWaitTimeSeconds)
            {
                // 检测安装是否成功
                var checkResult = await LeidianToolsNew.AdbAsync(_leidianPath, simulatorName, $"shell pm list packages | grep {packageName}");
        
                if (checkResult.Success && checkResult.Output.Contains($"package:{packageName}"))
                {
                    return true;
                }

                await Task.Delay(checkIntervalSeconds * 1000); // 等待2秒
                elapsedSeconds += checkIntervalSeconds;
            }

            return false; // 超时未安装成功
        }
    }
}
