using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using App2.Models;
using MySql.Data.MySqlClient;
using App2.Config;
using System.Text;

namespace App2.Services
{
    /// <summary>
    /// �û�Ⱥ�����
    /// </summary>
    public static class UserGroupService
    {
        /// <summary>
        /// ��ȡ�û�Ⱥ���Ա�б�
        /// </summary>
        /// <returns>�û�Ⱥ���Ա�б�</returns>
        public static async Task<List<UserGroupMember>> GetUserGroupMembersAsync()
        {
            var members = new List<UserGroupMember>();
            
            try
            {
                using var connection = new MySqlConnection(DatabaseConfig.ConnectionString);
                await connection.OpenAsync();
                
                // �ȼ���ṹ��ȷ����Щ�ֶδ���
                var columns = await GetTableColumnsAsync(connection, "user_group");
                
                // ������̬��ѯ
                var query = "SELECT id, nickname, user_id, platform, content";
                
                // ��ӿ�ѡ�ֶ�
                if (columns.Contains("source"))
                    query += ", source";
                if (columns.Contains("task_source"))
                    query += ", task_source";
                if (columns.Contains("task_id"))
                    query += ", task_id";
                    
                query += " FROM user_group ORDER BY id DESC";
                
                using var command = new MySqlCommand(query, connection);
                using var reader = await command.ExecuteReaderAsync();
                
                while (await reader.ReadAsync())
                {
                    var member = SafeReadMember((MySqlDataReader)reader, columns);
                    members.Add(member);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"��ȡ�û�Ⱥ���Ա�б�ʧ��: {ex}");
                throw;
            }
            
            return members;
        }
        
        /// <summary>
        /// ��ȡ��ṹ��Ϣ
        /// </summary>
        private static async Task<HashSet<string>> GetTableColumnsAsync(MySqlConnection connection, string tableName)
        {
            var columns = new HashSet<string>(StringComparer.OrdinalIgnoreCase);
            
            try
            {
                var query = $"DESCRIBE {tableName}";
                using var command = new MySqlCommand(query, connection);
                using var reader = await command.ExecuteReaderAsync();
                
                while (await reader.ReadAsync())
                {
                    columns.Add(reader.GetString(0)); // Field name
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"��ȡ��ṹʧ��: {ex}");
            }
            
            return columns;
        }

        /// <summary>
        /// ��ȡ�û�Ⱥ���Ա�б�֧��ɸѡ������
        /// </summary>
        /// <param name="platform">ƽ̨ɸѡ����ѡ��Ϊ�գ�</param>
        /// <param name="source">������Դɸѡ����ѡ��Ϊ�գ�</param>
        /// <param name="nickname">�ǳ������ؼ��֣���ѡ��Ϊ�գ�</param>
        /// <param name="sourceKeyword">��ǩ�ؼ�����������ѡ��Ϊ�գ�</param>
        /// <param name="taskSource">������Դɸѡ��task_source�ֶΣ�����ѡ��Ϊ�գ�</param>
        /// <returns>�û�Ⱥ���Ա�б�</returns>
        public static async Task<List<UserGroupMember>> GetUserGroupMembersAsync(string? platform = null, string? source = null, string? nickname = null, string? sourceKeyword = null, string? taskSource = null)
        {
            var members = new List<UserGroupMember>();
            
            try
            {
                using var connection = new MySqlConnection(DatabaseConfig.ConnectionString);
                await connection.OpenAsync();
                
                // ����ṹ
                var columns = await GetTableColumnsAsync(connection, "user_group");
                
                var queryBuilder = new StringBuilder("SELECT id, nickname, user_id, platform, content");
                
                // ��ӿ�ѡ�ֶ�
                if (columns.Contains("source"))
                    queryBuilder.Append(", source");
                if (columns.Contains("task_source"))
                    queryBuilder.Append(", task_source");
                if (columns.Contains("task_id"))
                    queryBuilder.Append(", task_id");
                    
                queryBuilder.Append(" FROM user_group WHERE 1=1");
                
                var parameters = new List<MySqlParameter>();

                // ����ɸѡ����
                if (!string.IsNullOrEmpty(platform))
                {
                    queryBuilder.Append(" AND platform = @platform");
                    parameters.Add(new MySqlParameter("@platform", platform));
                }

                if (!string.IsNullOrEmpty(nickname))
                {
                    queryBuilder.Append(" AND nickname LIKE @nickname");
                    parameters.Add(new MySqlParameter("@nickname", $"%{nickname}%"));
                }

                if (!string.IsNullOrEmpty(sourceKeyword))
                {
                    // ֻ�е�source�ֶλ�content�ֶ�ʱ������
                    if (columns.Contains("source"))
                        queryBuilder.Append(" AND (source LIKE @sourceKeyword OR content LIKE @sourceKeyword)");
                    else
                        queryBuilder.Append(" AND content LIKE @sourceKeyword");
                    parameters.Add(new MySqlParameter("@sourceKeyword", $"%{sourceKeyword}%"));
                }

                if (!string.IsNullOrEmpty(taskSource) && columns.Contains("task_source"))
                {
                    queryBuilder.Append(" AND task_source = @taskSource");
                    parameters.Add(new MySqlParameter("@taskSource", taskSource));
                }

                queryBuilder.Append(" ORDER BY id DESC");
                
                using var command = new MySqlCommand(queryBuilder.ToString(), connection);
                command.Parameters.AddRange(parameters.ToArray());
                
                using var reader = await command.ExecuteReaderAsync();
                
                while (await reader.ReadAsync())
                {
                    var member = SafeReadMember((MySqlDataReader)reader, columns);
                    members.Add(member);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"��ȡ�û�Ⱥ���Ա�б�ʧ��: {ex}");
                throw;
            }
            
            return members;
        }

        /// <summary>
        /// ��ȡ�û�Ⱥ���Ա��ҳ�б�֧��ɸѡ�����ͷ�ҳ
        /// </summary>
        /// <param name="pageNumber">ҳ�루��1��ʼ��</param>
        /// <param name="pageSize">ÿҳ��¼��</param>
        /// <param name="platform">ƽ̨ɸѡ����ѡ��Ϊ�գ�</param>
        /// <param name="source">������Դɸѡ����ѡ��Ϊ�գ�</param>
        /// <param name="nickname">�ǳ������ؼ��֣���ѡ��Ϊ�գ�</param>
        /// <param name="sourceKeyword">��ǩ�ؼ�����������ѡ��Ϊ�գ�</param>
        /// <param name="taskSource">������Դɸѡ��task_source�ֶΣ�����ѡ��Ϊ�գ�</param>
        /// <returns>��ҳ�û�Ⱥ���Ա�б�</returns>
        public static async Task<PagedResult<UserGroupMember>> GetUserGroupMembersPagedAsync(int pageNumber = 1, int pageSize = 10, string? platform = null, string? source = null, string? nickname = null, string? sourceKeyword = null, string? taskSource = null)
        {
            var result = new PagedResult<UserGroupMember>
            {
                CurrentPage = pageNumber,
                PageSize = pageSize
            };
            
            try
            {
                using var connection = new MySqlConnection(DatabaseConfig.ConnectionString);
                await connection.OpenAsync();
                
                // ����ṹ
                var columns = await GetTableColumnsAsync(connection, "user_group");
                
                // ����WHERE����
                var whereClause = new StringBuilder("WHERE 1=1");
                var parameters = new List<MySqlParameter>();

                if (!string.IsNullOrEmpty(platform))
                {
                    whereClause.Append(" AND platform = @platform");
                    parameters.Add(new MySqlParameter("@platform", platform));
                }

                if (!string.IsNullOrEmpty(nickname))
                {
                    whereClause.Append(" AND nickname LIKE @nickname");
                    parameters.Add(new MySqlParameter("@nickname", $"%{nickname}%"));
                }

                if (!string.IsNullOrEmpty(sourceKeyword))
                {
                    // ֻ�е�source�ֶλ�content�ֶ�ʱ������
                    if (columns.Contains("source"))
                        whereClause.Append(" AND (source LIKE @sourceKeyword OR content LIKE @sourceKeyword)");
                    else
                        whereClause.Append(" AND content LIKE @sourceKeyword");
                    parameters.Add(new MySqlParameter("@sourceKeyword", $"%{sourceKeyword}%"));
                }

                if (!string.IsNullOrEmpty(taskSource) && columns.Contains("task_source"))
                {
                    whereClause.Append(" AND task_source = @taskSource");
                    parameters.Add(new MySqlParameter("@taskSource", taskSource));
                }

                // 1. ��ȡ�ܼ�¼��
                var countQuery = $"SELECT COUNT(*) FROM user_group {whereClause}";
                using var countCommand = new MySqlCommand(countQuery, connection);
                countCommand.Parameters.AddRange(parameters.ToArray());
                
                var totalCountObj = await countCommand.ExecuteScalarAsync();
                result.TotalCount = Convert.ToInt32(totalCountObj);

                // 2. ��ȡ��ҳ����
                if (result.TotalCount > 0)
                {
                    var offset = (pageNumber - 1) * pageSize;
                    
                    // ������ѯ�ֶ�
                    var selectFields = "id, nickname, user_id, platform, content";
                    if (columns.Contains("source"))
                        selectFields += ", source";
                    if (columns.Contains("task_source"))
                        selectFields += ", task_source";
                    if (columns.Contains("task_id"))
                        selectFields += ", task_id";
                    
                    var dataQuery = $@"SELECT {selectFields} 
                                      FROM user_group {whereClause} 
                                      ORDER BY id DESC 
                                      LIMIT @pageSize OFFSET @offset";
                    
                    using var dataCommand = new MySqlCommand(dataQuery, connection);
                    dataCommand.Parameters.AddRange(parameters.ToArray());
                    dataCommand.Parameters.Add(new MySqlParameter("@pageSize", pageSize));
                    dataCommand.Parameters.Add(new MySqlParameter("@offset", offset));
                    
                    using var reader = await dataCommand.ExecuteReaderAsync();
                    
                    while (await reader.ReadAsync())
                    {
                        var member = SafeReadMember((MySqlDataReader)reader, columns);
                        result.Data.Add(member);
                    }
                }

                System.Diagnostics.Debug.WriteLine($"��ҳ��ѯ��� - ҳ��: {pageNumber}, ÿҳ: {pageSize}, �ܼ�¼: {result.TotalCount}, ��ǰҳ��¼: {result.Data.Count}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"��ȡ�û�Ⱥ���Ա��ҳ�б�ʧ��: {ex}");
                throw;
            }
            
            return result;
        }

        /// <summary>
        /// ɾ���û�Ⱥ���Ա
        /// </summary>
        /// <param name="id">��ԱID</param>
        /// <returns>ɾ�����</returns>
        public static async Task<(bool Success, string Message)> DeleteUserGroupMemberAsync(int id)
        {
            try
            {
                using var connection = new MySqlConnection(DatabaseConfig.ConnectionString);
                await connection.OpenAsync();
                
                var query = "DELETE FROM user_group WHERE id = @id";
                using var command = new MySqlCommand(query, connection);
                command.Parameters.AddWithValue("@id", id);
                
                var rowsAffected = await command.ExecuteNonQueryAsync();
                
                if (rowsAffected > 0)
                {
                    return (true, "ɾ���ɹ�");
                }
                else
                {
                    return (false, "δ�ҵ�Ҫɾ���ļ�¼");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"ɾ���û�Ⱥ���Աʧ��: {ex}");
                return (false, $"ɾ��ʧ��: {ex.Message}");
            }
        }

        /// <summary>
        /// ����ɾ���û�Ⱥ���Ա
        /// </summary>
        /// <param name="ids">��ԱID�б�</param>
        /// <returns>ɾ�����</returns>
        public static async Task<(bool Success, string Message)> DeleteUserGroupMembersAsync(List<int> ids)
        {
            if (ids == null || ids.Count == 0)
            {
                return (false, "û��Ҫɾ���ļ�¼");
            }

            try
            {
                using var connection = new MySqlConnection(DatabaseConfig.ConnectionString);
                await connection.OpenAsync();
                
                // ����IN��ѯ
                var idParameters = string.Join(",", ids.Select((_, index) => $"@id{index}"));
                var query = $"DELETE FROM user_group WHERE id IN ({idParameters})";
                
                using var command = new MySqlCommand(query, connection);
                
                // ��Ӳ���
                for (int i = 0; i < ids.Count; i++)
                {
                    command.Parameters.AddWithValue($"@id{i}", ids[i]);
                }
                
                var rowsAffected = await command.ExecuteNonQueryAsync();
                
                if (rowsAffected > 0)
                {
                    return (true, $"�ɹ�ɾ�� {rowsAffected} ����¼");
                }
                else
                {
                    return (false, "δ�ҵ�Ҫɾ���ļ�¼");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"����ɾ���û�Ⱥ���Աʧ��: {ex}");
                return (false, $"ɾ��ʧ��: {ex.Message}");
            }
        }

        /// <summary>
        /// ��ȫ�ض�ȡ���ݿ��ֶ�ֵ
        /// </summary>
        private static UserGroupMember SafeReadMember(MySqlDataReader reader, HashSet<string> columns)
        {
            var member = new UserGroupMember();
            
            try
            {
                // �����ֶ� - ��Щ�Ǳ���ģ�ʹ��������ȡ
                member.Id = reader.GetInt32(0); // id
                member.Nickname = reader.IsDBNull(1) ? "" : reader.GetString(1); // nickname
                member.UserId = reader.IsDBNull(2) ? "" : reader.GetString(2); // user_id
                member.Platform = reader.IsDBNull(3) ? "" : reader.GetString(3); // platform
                member.Content = reader.IsDBNull(4) ? "" : reader.GetString(4); // content
                
                int columnIndex = 5;
                
                // ��ѡ�ֶ� - ���ݲ�ѯ��˳��
                if (columns.Contains("source"))
                {
                    member.Source = reader.IsDBNull(columnIndex) ? "" : reader.GetString(columnIndex);
                    columnIndex++;
                }
                else
                {
                    member.Source = "";
                }
                
                if (columns.Contains("task_source"))
                {
                    // task_source�������ַ�������
                    if (reader.IsDBNull(columnIndex))
                    {
                        member.TaskSource = "";
                    }
                    else
                    {
                        try
                        {
                            // ֱ�Ӷ�ȡΪ�ַ���
                            member.TaskSource = reader.GetString(columnIndex);
                        }
                        catch
                        {
                            // ���ʧ�ܣ����Զ�ȡΪ����������ת��
                            try
                            {
                                var value = reader.GetValue(columnIndex);
                                member.TaskSource = value?.ToString() ?? "";
                            }
                            catch
                            {
                                member.TaskSource = "";
                            }
                        }
                    }
                    columnIndex++;
                }
                else
                {
                    member.TaskSource = "";
                }
                
                // ���task_id�ֶζ�ȡ
                if (columns.Contains("task_id"))
                {
                    if (reader.IsDBNull(columnIndex))
                    {
                        member.TaskId = 0;
                    }
                    else
                    {
                        try
                        {
                            member.TaskId = reader.GetInt32(columnIndex);
                        }
                        catch
                        {
                            // �����ȡʧ�ܣ�����������ʽ
                            try
                            {
                                var value = reader.GetValue(columnIndex);
                                member.TaskId = Convert.ToInt32(value);
                            }
                            catch
                            {
                                member.TaskId = 0;
                            }
                        }
                    }
                }
                else
                {
                    member.TaskId = 0;
                }
                
                return member;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"��ȡ��Ա����ʱ����: {ex.Message}");
                // ����һ��Ĭ�ϵĳ�Ա����
                return new UserGroupMember
                {
                    Id = 0,
                    Nickname = "��ȡ����",
                    UserId = "",
                    Platform = "",
                    Content = "",
                    Source = "",
                    TaskSource = "",
                    TaskId = 0
                };
            }
        }
    }
}