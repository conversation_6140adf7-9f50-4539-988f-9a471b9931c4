using System;
using System.Collections.Generic;
using MySql.Data.MySqlClient;
using App2.Config;
using App2.Models;

namespace App2.Services
{
    /// <summary>
    /// 任务类型表数据库操作服务（同步实现）
    /// </summary>
    public static class TaskTypeService
    {
        /// <summary>
        /// 查询所有任务类型（同步）
        /// </summary>
        public static List<TaskType> GetAll()
        {
            var result = new List<TaskType>();
            using var connection = new MySqlConnection(DatabaseConfig.ConnectionString);
            connection.Open();

            const string query = @"SELECT id, name, user_num, interval_time_min, interval_time_max, count_time_min, count_time_max, fans_num, content FROM task_type";
            using var command = new MySqlCommand(query, connection);
            using var reader = command.ExecuteReader();
            while (reader.Read())
            {
                result.Add(new TaskType
                {
                    Id = reader.GetInt32("id"),
                    Name = reader.GetString("name"),
                    UserNum = reader.IsDBNull(reader.GetOrdinal("user_num")) ? 0 : reader.GetInt32("user_num"),
                    IntervalTimeMin = reader.IsDBNull(reader.GetOrdinal("interval_time_min")) ? 0 : reader.GetInt32("interval_time_min"),
                    IntervalTimeMax = reader.IsDBNull(reader.GetOrdinal("interval_time_max")) ? 0 : reader.GetInt32("interval_time_max"),
                    CountTimeMin = reader.IsDBNull(reader.GetOrdinal("count_time_min")) ? 0 : reader.GetInt32("count_time_min"),
                    CountTimeMax = reader.IsDBNull(reader.GetOrdinal("count_time_max")) ? 0 : reader.GetInt32("count_time_max"),
                    FansNum = reader.IsDBNull(reader.GetOrdinal("fans_num")) ? 0 : reader.GetInt32("fans_num"),
                    Content = reader.GetString("content")
                });
            }
            return result;
        }


        public static List<TaskTypeRadio> GetAllSelect()
        {
            var result = new List<TaskTypeRadio>();
            using var connection = new MySqlConnection(DatabaseConfig.ConnectionString);
            connection.Open();

            const string query = @"SELECT id, name FROM task_type";
            using var command = new MySqlCommand(query, connection);
            using var reader = command.ExecuteReader();
            while (reader.Read())
            {
                result.Add(new TaskTypeRadio
                {
                    Id = reader.GetInt32("id"),
                    Name = reader.GetString("name"),
                });
            }
            return result;
        }



        /// <summary>
        /// 根据ID查询任务类型（同步）
        /// </summary>
        public static TaskType? GetById(int id)
        {
            using var connection = new MySqlConnection(DatabaseConfig.ConnectionString);
            connection.Open();

            const string query = @"SELECT id, name, user_num, interval_time_min, interval_time_max, count_time_min, count_time_max, fans_num, content FROM task_type WHERE id = @id";
            using var command = new MySqlCommand(query, connection);
            command.Parameters.AddWithValue("@id", id);

            using var reader = command.ExecuteReader();
            if (reader.Read())
            {
                return new TaskType
                {
                    Id = reader.GetInt32("id"),
                    Name = reader.GetString("name"),
                    UserNum = reader.IsDBNull(reader.GetOrdinal("user_num")) ? 0 : reader.GetInt32("user_num"),
                    IntervalTimeMin = reader.IsDBNull(reader.GetOrdinal("interval_time_min")) ? 0 : reader.GetInt32("interval_time_min"),
                    IntervalTimeMax = reader.IsDBNull(reader.GetOrdinal("interval_time_max")) ? 0 : reader.GetInt32("interval_time_max"),
                    CountTimeMin = reader.IsDBNull(reader.GetOrdinal("count_time_min")) ? 0 : reader.GetInt32("count_time_min"),
                    CountTimeMax = reader.IsDBNull(reader.GetOrdinal("count_time_max")) ? 0 : reader.GetInt32("count_time_max"),
                    FansNum = reader.IsDBNull(reader.GetOrdinal("fans_num")) ? 0 : reader.GetInt32("fans_num"),
                    Content = reader.GetString("content")
                };
            }
            return null;
        }

        /// <summary>
        /// 新增任务类型（同步）
        /// </summary>
        public static bool Add(TaskType taskType)
        {
            using var connection = new MySqlConnection(DatabaseConfig.ConnectionString);
            connection.Open();

            const string query = @"INSERT INTO task_type (name, user_num, interval_time_min, interval_time_max, count_time_min, count_time_max, fans_num, content)
                                   VALUES (@name, @user_num, @interval_time_min, @interval_time_max, @count_time_min, @count_time_max, @fans_num, @content)";
            using var command = new MySqlCommand(query, connection);
            command.Parameters.AddWithValue("@name", taskType.Name);
            command.Parameters.AddWithValue("@user_num", taskType.UserNum);
            command.Parameters.AddWithValue("@interval_time_min", taskType.IntervalTimeMin);
            command.Parameters.AddWithValue("@interval_time_max", taskType.IntervalTimeMax);
            command.Parameters.AddWithValue("@count_time_min", taskType.CountTimeMin);
            command.Parameters.AddWithValue("@count_time_max", taskType.CountTimeMax);
            command.Parameters.AddWithValue("@fans_num", taskType.FansNum);
            command.Parameters.AddWithValue("@content", taskType.Content);

            return command.ExecuteNonQuery() > 0;
        }

        /// <summary>
        /// 更新任务类型（同步）
        /// </summary>
        public static bool Update(TaskType taskType)
        {
            using var connection = new MySqlConnection(DatabaseConfig.ConnectionString);
            connection.Open();

            const string query = @"UPDATE task_type SET name = @name, user_num = @user_num, interval_time_min = @interval_time_min, interval_time_max = @interval_time_max,
                                   count_time_min = @count_time_min, count_time_max = @count_time_max, fans_num = @fans_num, content = @content WHERE id = @id";
            using var command = new MySqlCommand(query, connection);
            command.Parameters.AddWithValue("@id", taskType.Id);
            command.Parameters.AddWithValue("@name", taskType.Name);
            command.Parameters.AddWithValue("@user_num", taskType.UserNum);
            command.Parameters.AddWithValue("@interval_time_min", taskType.IntervalTimeMin);
            command.Parameters.AddWithValue("@interval_time_max", taskType.IntervalTimeMax);
            command.Parameters.AddWithValue("@count_time_min", taskType.CountTimeMin);
            command.Parameters.AddWithValue("@count_time_max", taskType.CountTimeMax);
            command.Parameters.AddWithValue("@fans_num", taskType.FansNum);
            command.Parameters.AddWithValue("@content", taskType.Content);

            return command.ExecuteNonQuery() > 0;
        }

        /// <summary>
        /// 删除任务类型（同步）
        /// </summary>
        public static bool Delete(int id)
        {
            using var connection = new MySqlConnection(DatabaseConfig.ConnectionString);
            connection.Open();

            const string query = "DELETE FROM task_type WHERE id = @id";
            using var command = new MySqlCommand(query, connection);
            command.Parameters.AddWithValue("@id", id);

            return command.ExecuteNonQuery() > 0;
        }
    }
}