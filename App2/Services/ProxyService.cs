using App2.Config;
using App2.Models;
using App2.Views.Pages;
using MySql.Data.MySqlClient;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static App2.Models.DataItem;

namespace App2.Services
{
    public class RemoteDatabaseService
    {

        // 保存或更新代理到远程数据库
        public async Task SaveOrUpdateProxyAsync(DataItem proxy)
        {
            using (var connection = new MySqlConnection(DatabaseConfig.ConnectionString))
            {
                await connection.OpenAsync();

                var command = new MySqlCommand(@"
                    INSERT INTO proxy (ip, status, testtime, type, port, username, password)
                    VALUES (@ip, @status, @testtime, @type, @port, @username, @password)", connection);

                command.Parameters.AddWithValue("@ip", proxy.Host);
                command.Parameters.AddWithValue("@status", proxy.Status);
                command.Parameters.AddWithValue("@testtime", proxy.LastUpdated);
                command.Parameters.AddWithValue("@type", proxy.Type);
                command.Parameters.AddWithValue("@port", proxy.Port);
                command.Parameters.AddWithValue("@username", proxy.Username);
                command.Parameters.AddWithValue("@password", proxy.Password);
                await command.ExecuteNonQueryAsync();
            }
        }

        public async Task<List<DataItem>> GetProxyAsync()
        {

            var proxies = new List<DataItem>();

            using (var connection = new MySqlConnection(DatabaseConfig.ConnectionString))
            {
                await connection.OpenAsync();

                var command = new MySqlCommand(
                    "SELECT id, ip, status, testtime, type, port, username, password FROM proxy",
                    connection);

                using (var reader = await command.ExecuteReaderAsync())
                {
                    while (await reader.ReadAsync())
                    {
                        proxies.Add(new DataItem
                        {
                            Id = reader.GetInt32(reader.GetOrdinal("id")),
                            Host = reader.GetString("ip"),
                            Status = reader.GetString("status"),
                            LastUpdated = reader.GetDateTime("testtime"),
                            Type = reader.GetString("type"),
                            Port = reader.GetString("port"),
                            Username = reader.IsDBNull("username") ? "" : reader.GetString("username"),
                            Password = reader.IsDBNull("password") ? "" : reader.GetString("password")
                        });
                    }
                }
            }
            return proxies;
        }

        public async Task<bool> DeleteProxyAsyncc(int id)
        {
            using var connection = new MySqlConnection(DatabaseConfig.ConnectionString);
            await connection.OpenAsync();

            var command = new MySqlCommand("DELETE FROM proxy WHERE id = @id", connection);
            command.Parameters.AddWithValue("@id", id);

            var result = await command.ExecuteNonQueryAsync();
            return result > 0;
        }


        public async Task<bool> UpdateProxyAsyncc(DataItem proxy)
        {
            using var connection = new MySqlConnection(DatabaseConfig.ConnectionString);
            await connection.OpenAsync();

            var command = new MySqlCommand(
                "UPDATE proxy SET status = @status,testtime=@testtime WHERE id = @id",
                connection);
            command.Parameters.AddWithValue("@id", proxy.Id);
            command.Parameters.AddWithValue("@status", proxy.Status);
            command.Parameters.AddWithValue("@testtime", proxy.LastUpdated);

            var result = await command.ExecuteNonQueryAsync();
            return result > 0;
        }

         public async Task<ProxyTaskData> GetById(int id)
        {
            using var connection = new MySqlConnection(DatabaseConfig.ConnectionString);
            connection.Open();

            const string query = @"SELECT id, ip, status, testtime, type, port, username, password FROM proxy WHERE id = @id";
            using var command = new MySqlCommand(query, connection);
            command.Parameters.AddWithValue("@id", id);

            using var reader = command.ExecuteReader();
            if (reader.Read())
            {
                return new ProxyTaskData
                {
                    Id = reader.GetInt32(reader.GetOrdinal("id")),
                    Name = "proxy_" + reader.GetInt32(reader.GetOrdinal("id")),
                    Host = reader.GetString("ip"),
                    Type = reader.GetString("type"),
                    Port = reader.GetString("port"),
                    UserName = reader.IsDBNull("username") ? "" : reader.GetString("username"),
                    Password = reader.IsDBNull("password") ? "" : reader.GetString("password")
                };
            }
            return null;
        }

        public async Task<List<ProxyTaskData>> GetProxyAll()
        {

            var proxies = new List<ProxyTaskData>();

            using (var connection = new MySqlConnection(DatabaseConfig.ConnectionString))
            {
                await connection.OpenAsync();

                var command = new MySqlCommand(
                    "SELECT proxy.id, proxy.ip, proxy.status, proxy.testtime, proxy.type, proxy.port, proxy.username, proxy.password FROM proxy left join user_account as a on proxy.id = a.proxy_id where a.id IS NULL",
                    connection);

                using (var reader = await command.ExecuteReaderAsync())
                {
                    while (await reader.ReadAsync())
                    {
                        proxies.Add(new ProxyTaskData
                        {
                            Id = reader.GetInt32(reader.GetOrdinal("id")),
                            Name = "proxy_" + reader.GetInt32(reader.GetOrdinal("id")),
                            Host = reader.GetString("ip"),
                            Type = reader.GetString("type"),
                            Port = reader.GetString("port"),
                            UserName = reader.IsDBNull("username") ? "" : reader.GetString("username"),
                            Password = reader.IsDBNull("password") ? "" : reader.GetString("password")
                        });
                    }
                }
            }
            return proxies;
        }

    }
}