using System;
using System.Threading.Tasks;
using App2.Models;
using MySql.Data.MySqlClient;
using App2.Config;

namespace App2.Services
{
    /// <summary>
    /// �����֤����
    /// </summary>
    public static class AuthService
    {
        /// <summary>
        /// �û���¼��֤
        /// </summary>
        /// <param name="username">�û���</param>
        /// <param name="password">����</param>
        /// <param name="ipAddress">IP��ַ</param>
        /// <returns>��¼���</returns>
        public static async Task<LoginResult> ValidateLoginAsync(string username, string password, string ipAddress = "")
        {
            try
            {
                using var connection = new MySqlConnection(DatabaseConfig.ConnectionString);
                await connection.OpenAsync();

                // ��ѯ�û���Ϣ - ʹ�����ݿ�ʵ�ʵ��ֶ���
                var query = @"SELECT id, username, password_hash, salt, email, created_at, updated_at, status 
                             FROM users 
                             WHERE username = @username AND status = 'active'";

                using var command = new MySqlCommand(query, connection);
                command.Parameters.AddWithValue("@username", username);

                using var reader = await command.ExecuteReaderAsync();

                if (await reader.ReadAsync())
                {
                    var storedPasswordHash = reader.GetString(2); // password_hash column
                    var salt = reader.GetString(3); // salt column
                    
                    // ������֤�߼�
                    bool isPasswordValid = false;
                    
                    // 1. ����Ƿ������ݿ�Ĭ�ϵĹ�ϣ��ʽ (SHA2�������ɵ�)
                    var expectedHash = System.Security.Cryptography.SHA256.HashData(
                        System.Text.Encoding.UTF8.GetBytes(password + salt));
                    var expectedHashHex = Convert.ToHexString(expectedHash).ToLower();
                    
                    if (storedPasswordHash.Equals(expectedHashHex, StringComparison.OrdinalIgnoreCase))
                    {
                        isPasswordValid = true;
                    }
                    
                    // 2. ����������֤ʧ�ܣ�����MySQL��SHA2������ʽ��֤
                    if (!isPasswordValid)
                    {
                        var mysqlHashQuery = "SELECT SHA2(CONCAT(@password, @salt), 256) as computed_hash";
                        using var hashCommand = new MySqlCommand(mysqlHashQuery, connection);
                        hashCommand.Parameters.AddWithValue("@password", password);
                        hashCommand.Parameters.AddWithValue("@salt", salt);
                        
                        var computedHash = await hashCommand.ExecuteScalarAsync() as string;
                        if (computedHash != null && storedPasswordHash.Equals(computedHash, StringComparison.OrdinalIgnoreCase))
                        {
                            isPasswordValid = true;
                        }
                    }
                    
                    // 3. ���ף�ֱ�ӱȽ��������루���ݿ��ܵľ����ݣ�
                    if (!isPasswordValid && storedPasswordHash == password)
                    {
                        isPasswordValid = true;
                    }
                    
                    if (isPasswordValid)
                    {
                        var user = new User
                        {
                            Id = reader.GetInt32(0), // id
                            Username = reader.GetString(1), // username  
                            Password = password, // ���������������ڼ���
                            PasswordHash = storedPasswordHash,
                            Salt = salt,
                            Email = reader.IsDBNull(4) ? "" : reader.GetString(4), // email
                            CreatedAt = reader.GetDateTime(5), // created_at
                            UpdatedAt = reader.GetDateTime(6), // updated_at
                            Status = Enum.Parse<App2.Models.UserStatus>(reader.GetString(7), true) // status
                        };

                        reader.Close();

                        // �����Ự����
                        var sessionToken = Guid.NewGuid().ToString();
                        
                        // ���Ự��Ϣ���浽���ݿ�
                        await SaveSessionAsync(user.Id, sessionToken, ipAddress);

                        return new LoginResult
                        {
                            Success = true,
                            Message = "��¼�ɹ�",
                            User = user,
                            SessionToken = sessionToken
                        };
                    }
                    else
                    {
                        return new LoginResult
                        {
                            Success = false,
                            Message = "�û������������"
                        };
                    }
                }
                else
                {
                    return new LoginResult
                    {
                        Success = false,
                        Message = "�û������������"
                    };
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"��¼��֤�쳣: {ex}");
                return new LoginResult
                {
                    Success = false,
                    Message = $"��¼ʧ��: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// �޸��û�����
        /// </summary>
        /// <param name="userId">�û�ID</param>
        /// <param name="oldPassword">������</param>
        /// <param name="newPassword">������</param>
        /// <returns>�޸Ľ��</returns>
        public static async Task<(bool Success, string Message)> ChangePasswordAsync(int userId, string oldPassword, string newPassword)
        {
            try
            {
                using var connection = new MySqlConnection(DatabaseConfig.ConnectionString);
                await connection.OpenAsync();

                // ������֤������ - ʹ����ȷ���ֶ���
                var verifyQuery = "SELECT password_hash, salt FROM users WHERE id = @userId";
                using var verifyCommand = new MySqlCommand(verifyQuery, connection);
                verifyCommand.Parameters.AddWithValue("@userId", userId);

                using var verifyReader = await verifyCommand.ExecuteReaderAsync();
                
                if (!await verifyReader.ReadAsync())
                {
                    return (false, "�û�������");
                }
                
                var storedPasswordHash = verifyReader.GetString(0);
                var salt = verifyReader.GetString(1);
                verifyReader.Close();

                // ��֤������
                var oldPasswordHash = System.Security.Cryptography.SHA256.HashData(
                    System.Text.Encoding.UTF8.GetBytes(oldPassword + salt));
                var oldPasswordHashString = Convert.ToHexString(oldPasswordHash).ToLower();
                
                bool isOldPasswordValid = false;
                
                // ���ȳ��Թ�ϣ��֤
                if (storedPasswordHash.Length == 64)
                {
                    isOldPasswordValid = storedPasswordHash.Equals(oldPasswordHashString, StringComparison.OrdinalIgnoreCase);
                }
                
                // �����ϣ��֤ʧ�ܣ�����ֱ�ӱȽϣ����ݾ����ݣ�
                if (!isOldPasswordValid && storedPasswordHash == oldPassword)
                {
                    isOldPasswordValid = true;
                }

                if (!isOldPasswordValid)
                {
                    return (false, "�����벻��ȷ");
                }

                // �����µ���ֵ�������ϣ
                var newSalt = Guid.NewGuid().ToString("N")[..16]; // 16λ��ֵ
                var newPasswordHash = System.Security.Cryptography.SHA256.HashData(
                    System.Text.Encoding.UTF8.GetBytes(newPassword + newSalt));
                var newPasswordHashString = Convert.ToHexString(newPasswordHash).ToLower();

                // ��������
                var updateQuery = "UPDATE users SET password_hash = @newPasswordHash, salt = @newSalt, updated_at = NOW() WHERE id = @userId";
                using var updateCommand = new MySqlCommand(updateQuery, connection);
                updateCommand.Parameters.AddWithValue("@newPasswordHash", newPasswordHashString);
                updateCommand.Parameters.AddWithValue("@newSalt", newSalt);
                updateCommand.Parameters.AddWithValue("@userId", userId);

                var rowsAffected = await updateCommand.ExecuteNonQueryAsync();

                if (rowsAffected > 0)
                {
                    return (true, "�����޸ĳɹ�");
                }
                else
                {
                    return (false, "�����޸�ʧ��");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"�޸������쳣: {ex}");
                return (false, $"�޸�����ʧ��: {ex.Message}");
            }
        }

        /// <summary>
        /// ����Ự��Ϣ�����ݿ�
        /// </summary>
        /// <param name="userId">�û�ID</param>
        /// <param name="sessionToken">�Ự����</param>
        /// <param name="ipAddress">IP��ַ</param>
        /// <returns>������</returns>
        public static async Task<bool> SaveSessionAsync(int userId, string sessionToken, string ipAddress = "")
        {
            try
            {
                using var connection = new MySqlConnection(DatabaseConfig.ConnectionString);
                await connection.OpenAsync();

                // ɾ�����û��ľɻỰ
                var deleteQuery = "DELETE FROM user_sessions WHERE user_id = @userId";
                using var deleteCommand = new MySqlCommand(deleteQuery, connection);
                deleteCommand.Parameters.AddWithValue("@userId", userId);
                await deleteCommand.ExecuteNonQueryAsync();

                // �����»Ự
                var insertQuery = @"INSERT INTO user_sessions (user_id, session_token, ip_address, created_at, expires_at) 
                                   VALUES (@userId, @sessionToken, @ipAddress, NOW(), DATE_ADD(NOW(), INTERVAL 30 DAY))";
                
                using var insertCommand = new MySqlCommand(insertQuery, connection);
                insertCommand.Parameters.AddWithValue("@userId", userId);
                insertCommand.Parameters.AddWithValue("@sessionToken", sessionToken);
                insertCommand.Parameters.AddWithValue("@ipAddress", ipAddress);

                var rowsAffected = await insertCommand.ExecuteNonQueryAsync();
                return rowsAffected > 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"����Ự�쳣: {ex}");
                return false;
            }
        }

        /// <summary>
        /// ��֤�Ự����
        /// </summary>
        /// <param name="sessionToken">�Ự����</param>
        /// <returns>�Ự��Ӧ���û��������Ч�򷵻�null</returns>
        public static async Task<User?> ValidateSessionAsync(string sessionToken)
        {
            try
            {
                using var connection = new MySqlConnection(DatabaseConfig.ConnectionString);
                await connection.OpenAsync();

                var query = @"SELECT u.id, u.username, u.password_hash, u.salt, u.email, u.created_at, u.updated_at, u.status
                             FROM users u
                             INNER JOIN user_sessions s ON u.id = s.user_id
                             WHERE s.session_token = @sessionToken 
                             AND s.expires_at > NOW() 
                             AND u.status = 'active'";

                using var command = new MySqlCommand(query, connection);
                command.Parameters.AddWithValue("@sessionToken", sessionToken);

                using var reader = await command.ExecuteReaderAsync();

                if (await reader.ReadAsync())
                {
                    return new User
                    {
                        Id = reader.GetInt32(0), // id
                        Username = reader.GetString(1), // username
                        PasswordHash = reader.GetString(2), // password_hash
                        Salt = reader.GetString(3), // salt
                        Email = reader.IsDBNull(4) ? "" : reader.GetString(4), // email
                        CreatedAt = reader.GetDateTime(5), // created_at
                        UpdatedAt = reader.GetDateTime(6), // updated_at
                        Status = Enum.Parse<App2.Models.UserStatus>(reader.GetString(7), true) // status
                    };
                }

                return null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"��֤�Ự�쳣: {ex}");
                return null;
            }
        }

        /// <summary>
        /// ע���Ự
        /// </summary>
        /// <param name="sessionToken">�Ự����</param>
        /// <returns>ע�����</returns>
        public static async Task<bool> LogoutSessionAsync(string sessionToken)
        {
            try
            {
                using var connection = new MySqlConnection(DatabaseConfig.ConnectionString);
                await connection.OpenAsync();

                var query = "DELETE FROM user_sessions WHERE session_token = @sessionToken";
                using var command = new MySqlCommand(query, connection);
                command.Parameters.AddWithValue("@sessionToken", sessionToken);

                var rowsAffected = await command.ExecuteNonQueryAsync();
                return rowsAffected > 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"ע���Ự�쳣: {ex}");
                return false;
            }
        }
    }
}