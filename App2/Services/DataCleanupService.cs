using System;
using System.Threading.Tasks;
using MySql.Data.MySqlClient;
using App2.Config;

namespace App2.Services
{
    /// <summary>
    /// 数据清理服务
    /// </summary>
    public static class DataCleanupService
    {
        /// <summary>
        /// 清理过期会话
        /// </summary>
        /// <returns>清理的会话数量</returns>
        public static async Task<int> CleanExpiredSessionsAsync()
        {
            try
            {
                using var connection = new MySqlConnection(DatabaseConfig.ConnectionString);
                await connection.OpenAsync();

                const string query = "DELETE FROM user_sessions WHERE expires_at <= NOW()";
                using var command = new MySqlCommand(query, connection);
                
                return await command.ExecuteNonQueryAsync();
            }
            catch
            {
                return 0;
            }
        }

        /// <summary>
        /// 清理过期登录日志（保留最近30天）
        /// </summary>
        /// <returns>清理的日志数量</returns>
        public static async Task<int> CleanOldLoginLogsAsync()
        {
            try
            {
                using var connection = new MySqlConnection(DatabaseConfig.ConnectionString);
                await connection.OpenAsync();

                const string query = "DELETE FROM login_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL 30 DAY)";
                using var command = new MySqlCommand(query, connection);
                
                return await command.ExecuteNonQueryAsync();
            }
            catch
            {
                return 0;
            }
        }

        /// <summary>
        /// 清理所有用户会话
        /// </summary>
        /// <returns>清理的会话数量</returns>
        public static async Task<int> ClearAllSessionsAsync()
        {
            try
            {
                using var connection = new MySqlConnection(DatabaseConfig.ConnectionString);
                await connection.OpenAsync();

                const string query = "DELETE FROM user_sessions";
                using var command = new MySqlCommand(query, connection);
                
                return await command.ExecuteNonQueryAsync();
            }
            catch
            {
                return 0;
            }
        }

        /// <summary>
        /// 获取数据库统计信息
        /// </summary>
        /// <returns>统计信息</returns>
        public static async Task<DatabaseStats> GetDatabaseStatsAsync()
        {
            var stats = new DatabaseStats();
            
            try
            {
                using var connection = new MySqlConnection(DatabaseConfig.ConnectionString);
                await connection.OpenAsync();

                // 统计用户数量
                using (var command = new MySqlCommand("SELECT COUNT(*) FROM users", connection))
                {
                    stats.UserCount = Convert.ToInt32(await command.ExecuteScalarAsync());
                }

                // 统计活跃会话数量
                using (var command = new MySqlCommand("SELECT COUNT(*) FROM user_sessions WHERE expires_at > NOW()", connection))
                {
                    stats.ActiveSessionCount = Convert.ToInt32(await command.ExecuteScalarAsync());
                }

                // 统计过期会话数量
                using (var command = new MySqlCommand("SELECT COUNT(*) FROM user_sessions WHERE expires_at <= NOW()", connection))
                {
                    stats.ExpiredSessionCount = Convert.ToInt32(await command.ExecuteScalarAsync());
                }

                // 统计登录日志数量
                using (var command = new MySqlCommand("SELECT COUNT(*) FROM login_logs", connection))
                {
                    stats.LoginLogCount = Convert.ToInt32(await command.ExecuteScalarAsync());
                }
            }
            catch
            {
                // 统计失败时返回默认值
            }

            return stats;
        }
    }

    /// <summary>
    /// 数据库统计信息
    /// </summary>
    public class DatabaseStats
    {
        public int UserCount { get; set; }
        public int ActiveSessionCount { get; set; }
        public int ExpiredSessionCount { get; set; }
        public int LoginLogCount { get; set; }
    }
}