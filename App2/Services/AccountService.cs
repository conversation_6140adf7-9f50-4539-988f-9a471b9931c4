using App2.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using MySql.Data.MySqlClient;
using App2.Config;

namespace App2.Services
{
    internal class AccountService
    {
        /// <summary>
        /// 获取用户的所有账号
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="source">账号来源过滤（可选）</param>
        /// <param name="status">账号状态过滤（可选）</param>
        /// <returns>账号列表</returns>
        public static async Task<List<UserAccount>> GetUserAccountsAsync(int userId, AccountSource? source = null, AccountStatus? status = null)
        {
            var accounts = new List<UserAccount>();

            try
            {
                using var connection = new MySqlConnection(DatabaseConfig.ConnectionString);
                await connection.OpenAsync();

                // 根据实际数据库结构使用正确的字段名
                var queryBuilder = new System.Text.StringBuilder(@"
                    SELECT DISTINCT id, user_id, simulator_id, account_name, account, 
                           password, account_status, source, created_at, updated_at
                    FROM user_account 
                    WHERE user_id = @userId");

                var parameters = new List<MySqlParameter>
                {
                    new MySqlParameter("@userId", userId)
                };

                if (source.HasValue)
                {
                    queryBuilder.Append(" AND source = @source");
                    parameters.Add(new MySqlParameter("@source", source.Value.ToString()));
                }

                if (status.HasValue)
                {
                    queryBuilder.Append(" AND account_status = @status");
                    parameters.Add(new MySqlParameter("@status", status.Value.ToString()));
                }

                queryBuilder.Append(" ORDER BY updated_at DESC");

                using var command = new MySqlCommand(queryBuilder.ToString(), connection);
                command.Parameters.AddRange(parameters.ToArray());

                System.Diagnostics.Debug.WriteLine($"执行查询: {queryBuilder}");
                System.Diagnostics.Debug.WriteLine($"用户ID: {userId}, 平台: {source}, 状态: {status}");

                using var reader = await command.ExecuteReaderAsync();

                var processedIds = new HashSet<int>(); // 防止重复

                while (await reader.ReadAsync())
                {
                    var accountId = reader.GetInt32(reader.GetOrdinal("id"));
                    
                    // 检查是否已经处理过这个ID
                    if (processedIds.Contains(accountId))
                    {
                        System.Diagnostics.Debug.WriteLine($"跳过重复ID: {accountId}");
                        continue;
                    }
                    processedIds.Add(accountId);

                    var account = new UserAccount
                    {
                        Id = accountId,
                        UserId = reader.GetInt32(reader.GetOrdinal("user_id")),
                        SimulatorId = reader.GetInt32(reader.GetOrdinal("simulator_id")),
                        AccountName = reader.GetString(reader.GetOrdinal("account_name")),
                        LoginAccount = reader.GetString(reader.GetOrdinal("account")), // 使用正确的字段名
                        Password = reader.IsDBNull(reader.GetOrdinal("password")) ? string.Empty : reader.GetString(reader.GetOrdinal("password")),
                        AccountStatus = Enum.Parse<AccountStatus>(reader.GetString(reader.GetOrdinal("account_status")), true),
                        Source = Enum.Parse<AccountSource>(reader.GetString(reader.GetOrdinal("source")), true),
                        CreatedAt = reader.GetDateTime(reader.GetOrdinal("created_at")),
                        UpdatedAt = reader.GetDateTime(reader.GetOrdinal("updated_at"))
                    };

                    accounts.Add(account);
                    System.Diagnostics.Debug.WriteLine($"加载账号: {account.AccountName} ({account.LoginAccount}) - {account.Source}");
                }

                System.Diagnostics.Debug.WriteLine($"总共加载了 {accounts.Count} 个账号");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取用户账号列表失败: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"异常详情: {ex}");
            }

            return accounts;
        }

        /// <summary>
        /// 根据ID获取单个账号
        /// </summary>
        /// <param name="accountId">账号ID</param>
        /// <returns>账号信息</returns>
        public static async Task<UserAccount?> GetAccountByIdAsync(int accountId)
        {
            try
            {
                using var connection = new MySqlConnection(DatabaseConfig.ConnectionString);
                await connection.OpenAsync();

                const string query = @"
                    SELECT id, user_id, simulator_id, account_name, account, 
                           password, account_status, source, created_at, updated_at
                    FROM user_account 
                    WHERE id = @accountId";

                using var command = new MySqlCommand(query, connection);
                command.Parameters.AddWithValue("@accountId", accountId);

                using var reader = await command.ExecuteReaderAsync();

                if (await reader.ReadAsync())
                {
                    return new UserAccount
                    {
                        Id = reader.GetInt32(reader.GetOrdinal("id")),
                        UserId = reader.GetInt32(reader.GetOrdinal("user_id")),
                        SimulatorId = reader.GetInt32(reader.GetOrdinal("simulator_id")),
                        AccountName = reader.GetString(reader.GetOrdinal("account_name")),
                        LoginAccount = reader.GetString(reader.GetOrdinal("account")), // 使用正确的字段名
                        Password = reader.IsDBNull(reader.GetOrdinal("password")) ? string.Empty : reader.GetString(reader.GetOrdinal("password")),
                        AccountStatus = Enum.Parse<AccountStatus>(reader.GetString(reader.GetOrdinal("account_status")), true),
                        Source = Enum.Parse<AccountSource>(reader.GetString(reader.GetOrdinal("source")), true),
                        CreatedAt = reader.GetDateTime(reader.GetOrdinal("created_at")),
                        UpdatedAt = reader.GetDateTime(reader.GetOrdinal("updated_at"))
                    };
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取账号详情失败: {ex.Message}");
            }

            return null;
        }

        /// <summary>
        /// 添加新账号
        /// </summary>
        /// <param name="account">账号信息</param>
        /// <returns>操作结果</returns>
        public static async Task<AccountOperationResult> AddAccountAsync(UserAccount account)
        {
            var result = new AccountOperationResult();

            try
            {
                using var connection = new MySqlConnection(DatabaseConfig.ConnectionString);
                await connection.OpenAsync();

                // 检查账号是否已存在
                const string checkQuery = @"
                    SELECT COUNT(*) FROM user_account 
                    WHERE user_id = @userId AND account = @loginAccount AND source = @source";

                using var checkCommand = new MySqlCommand(checkQuery, connection);
                checkCommand.Parameters.AddWithValue("@userId", account.UserId);
                checkCommand.Parameters.AddWithValue("@loginAccount", account.LoginAccount);
                checkCommand.Parameters.AddWithValue("@source", account.Source.ToString());

                var existingCount = Convert.ToInt32(await checkCommand.ExecuteScalarAsync());
                if (existingCount > 0)
                {
                    result.Success = false;
                    result.Message = "该账号已存在";
                    return result;
                }

                // 插入新账号
                const string insertQuery = @"
                    INSERT INTO user_account (user_id, simulator_id, account_name, account, 
                                            password, account_status, source, created_at, updated_at)
                    VALUES (@userId, @simulatorId, @accountName, @loginAccount, 
                            @password, @accountStatus, @source, NOW(), NOW())";

                using var insertCommand = new MySqlCommand(insertQuery, connection);
                insertCommand.Parameters.AddWithValue("@userId", account.UserId);
                insertCommand.Parameters.AddWithValue("@simulatorId", account.SimulatorId);
                insertCommand.Parameters.AddWithValue("@accountName", account.AccountName);
                insertCommand.Parameters.AddWithValue("@loginAccount", account.LoginAccount);
                insertCommand.Parameters.AddWithValue("@password", account.Password ?? string.Empty);
                insertCommand.Parameters.AddWithValue("@accountStatus", account.AccountStatus.ToString());
                insertCommand.Parameters.AddWithValue("@source", account.Source.ToString());

                await insertCommand.ExecuteNonQueryAsync();

                // 获取新插入的ID
                const string getIdQuery = "SELECT LAST_INSERT_ID()";
                using var getIdCommand = new MySqlCommand(getIdQuery, connection);
                account.Id = Convert.ToInt32(await getIdCommand.ExecuteScalarAsync());

                result.Success = true;
                result.Message = "账号添加成功";
                result.Account = account;
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.Message = $"添加账号失败：{ex.Message}";
                System.Diagnostics.Debug.WriteLine($"添加账号失败: {ex.Message}");
            }

            return result;
        }

        /// <summary>
        /// 更新账号信息
        /// </summary>
        /// <param name="account">账号信息</param>
        /// <returns>操作结果</returns>
        public static async Task<AccountOperationResult> UpdateAccountAsync(UserAccount account)
        {
            var result = new AccountOperationResult();

            try
            {
                using var connection = new MySqlConnection(DatabaseConfig.ConnectionString);
                await connection.OpenAsync();

                const string updateQuery = @"
                    UPDATE user_account 
                    SET simulator_id = @simulatorId, 
                        account_name = @accountName, 
                        account = @loginAccount,
                        proxy_id = @proxyId,
                        password = @password,
                        account_status = @accountStatus,
                        source = @source,
                        updated_at = NOW()
                    WHERE id = @accountId";

                using var command = new MySqlCommand(updateQuery, connection);
                command.Parameters.AddWithValue("@accountId", account.Id);
                command.Parameters.AddWithValue("@simulatorId", account.SimulatorId);
                command.Parameters.AddWithValue("@accountName", account.AccountName);
                command.Parameters.AddWithValue("@loginAccount", account.LoginAccount);
                command.Parameters.AddWithValue("@proxyId", account.ProxyId);
                command.Parameters.AddWithValue("@password", account.Password); // 直接存储明文密码
                command.Parameters.AddWithValue("@accountStatus", account.AccountStatus.ToString());
                command.Parameters.AddWithValue("@source", account.Source.ToString());

                int rowsAffected = await command.ExecuteNonQueryAsync();

                if (rowsAffected > 0)
                {
                    account.UpdatedAt = DateTime.Now;
                    result.Success = true;
                    result.Message = "账号更新成功";
                    result.Account = account;
                }
                else
                {
                    result.Success = false;
                    result.Message = "账号不存在或更新失败";
                }
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.Message = $"更新账号失败：{ex.Message}";
                System.Diagnostics.Debug.WriteLine($"更新账号失败: {ex.Message}");
            }

            return result;
        }

        /// <summary>
        /// 检查账号是否绑定了模拟器
        /// </summary>
        /// <param name="accountId">账号ID</param>
        /// <returns>是否绑定了模拟器</returns>
        public static async Task<bool> IsAccountBoundToSimulatorAsync(int accountId)
        {
            try
            {
                using var connection = new MySqlConnection(DatabaseConfig.ConnectionString);
                await connection.OpenAsync();

                const string query = "SELECT simulator_id FROM user_account WHERE id = @accountId";

                using var command = new MySqlCommand(query, connection);
                command.Parameters.AddWithValue("@accountId", accountId);

                var result = await command.ExecuteScalarAsync();
                if (result != null && int.TryParse(result.ToString(), out int simulatorId))
                {
                    return simulatorId > 0; // 大于0表示已绑定模拟器
                }

                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"检查账号模拟器绑定状态失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 批量检查账号是否绑定了模拟器
        /// </summary>
        /// <param name="accountIds">账号ID列表</param>
        /// <returns>绑定了模拟器的账号ID列表</returns>
        public static async Task<List<int>> GetBoundToSimulatorAccountIdsAsync(List<int> accountIds)
        {
            var boundAccounts = new List<int>();

            if (accountIds == null || accountIds.Count == 0)
                return boundAccounts;

            try
            {
                using var connection = new MySqlConnection(DatabaseConfig.ConnectionString);
                await connection.OpenAsync();

                var idsParameter = string.Join(",", accountIds);
                var query = $"SELECT id FROM user_account WHERE id IN ({idsParameter}) AND simulator_id > 0";

                using var command = new MySqlCommand(query, connection);
                using var reader = await command.ExecuteReaderAsync();

                while (await reader.ReadAsync())
                {
                    boundAccounts.Add(reader.GetInt32(reader.GetOrdinal("id")));
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"批量检查账号模拟器绑定状态失败: {ex.Message}");
            }

            return boundAccounts;
        }

        /// <summary>
        /// 删除账号
        /// </summary>
        /// <param name="accountId">账号ID</param>
        /// <returns>操作结果</returns>
        public static async Task<AccountOperationResult> DeleteAccountAsync(int accountId)
        {
            var result = new AccountOperationResult();

            try
            {
                // 首先检查账号是否绑定了模拟器
                bool isBound = await IsAccountBoundToSimulatorAsync(accountId);
                if (isBound)
                {
                    result.Success = false;
                    result.Message = "该账号已绑定模拟器，请先解绑模拟器后再删除";
                    return result;
                }

                using var connection = new MySqlConnection(DatabaseConfig.ConnectionString);
                await connection.OpenAsync();

                const string deleteQuery = "DELETE FROM user_account WHERE id = @accountId";

                using var command = new MySqlCommand(deleteQuery, connection);
                command.Parameters.AddWithValue("@accountId", accountId);

                int rowsAffected = await command.ExecuteNonQueryAsync();

                if (rowsAffected > 0)
                {
                    result.Success = true;
                    result.Message = "账号删除成功";
                }
                else
                {
                    result.Success = false;
                    result.Message = "账号不存在或删除失败";
                }
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.Message = $"删除账号失败：{ex.Message}";
                System.Diagnostics.Debug.WriteLine($"删除账号失败: {ex.Message}");
            }

            return result;
        }

        /// <summary>
        /// 批量删除账号
        /// </summary>
        /// <param name="accountIds">账号ID列表</param>
        /// <returns>批量操作结果</returns>
        public static async Task<BatchOperationResult> BatchDeleteAccountsAsync(List<int> accountIds)
        {
            var result = new BatchOperationResult();

            if (accountIds == null || accountIds.Count == 0)
            {
                result.Success = false;
                result.Message = "请选择要删除的账号";
                return result;
            }

            try
            {
                // 检查是否有账号绑定了模拟器
                var boundAccountIds = await GetBoundToSimulatorAccountIdsAsync(accountIds);
                if (boundAccountIds.Count > 0)
                {
                    result.Success = false;
                    result.Message = $"有 {boundAccountIds.Count} 个账号已绑定模拟器，请先解绑模拟器后再删除";
                    result.ErrorMessages.Add($"绑定模拟器的账号ID: {string.Join(", ", boundAccountIds)}");
                    return result;
                }

                using var connection = new MySqlConnection(DatabaseConfig.ConnectionString);
                await connection.OpenAsync();

                var idsParameter = string.Join(",", accountIds);
                var deleteQuery = $"DELETE FROM user_account WHERE id IN ({idsParameter})";

                using var command = new MySqlCommand(deleteQuery, connection);
                int rowsAffected = await command.ExecuteNonQueryAsync();

                result.SuccessCount = rowsAffected;
                result.FailureCount = accountIds.Count - rowsAffected;

                if (rowsAffected > 0)
                {
                    result.Success = true;
                    result.Message = $"成功删除 {rowsAffected} 个账号";
                    
                    if (result.FailureCount > 0)
                    {
                        result.Message += $"，{result.FailureCount} 个账号删除失败";
                    }
                }
                else
                {
                    result.Success = false;
                    result.Message = "没有账号被删除";
                }
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.Message = $"批量删除失败：{ex.Message}";
                result.ErrorMessages.Add(ex.Message);
                System.Diagnostics.Debug.WriteLine($"批量删除账号失败: {ex.Message}");
            }

            return result;
        }

        /// <summary>
        /// 批量导入账号 - 密码以明文形式存储（不进行MD5加密）
        /// </summary>
        /// <param name="accounts">账号列表</param>
        /// <returns>批量操作结果</returns>
        public static async Task<BatchOperationResult> BatchImportAccountsAsync(List<UserAccount> accounts)
        {
            var result = new BatchOperationResult();

            if (accounts == null || accounts.Count == 0)
            {
                result.Success = false;
                result.Message = "没有要导入的账号";
                System.Diagnostics.Debug.WriteLine("批量导入失败: 账号列表为空");
                return result;
            }

            System.Diagnostics.Debug.WriteLine($"开始批量导入 {accounts.Count} 个账号");

            try
            {
                using var connection = new MySqlConnection(DatabaseConfig.ConnectionString);
                System.Diagnostics.Debug.WriteLine($"尝试连接数据库: {DatabaseConfig.ConnectionString.Substring(0, 50)}...");
                await connection.OpenAsync();
                System.Diagnostics.Debug.WriteLine("数据库连接成功");

                // 检查表结构以确定正确的字段名
                var columns = new List<string>();
                try
                {
                    const string describeQuery = "DESCRIBE user_account";
                    using var describeCommand = new MySqlCommand(describeQuery, connection);
                    using var describeReader = await describeCommand.ExecuteReaderAsync();

                    while (await describeReader.ReadAsync())
                    {
                        var columnName = describeReader.GetString(describeReader.GetOrdinal("Field"));
                        columns.Add(columnName);
                        System.Diagnostics.Debug.WriteLine($"表字段: {columnName}");
                    }
                    describeReader.Close();
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"获取表结构失败: {ex.Message}");
                }

                // 根据实际字段名调整查询 - 使用数据库中的实际字段
                string loginAccountField = "account";        // 数据库中实际使用的字段
                string accountNameField = "account_name";    // 数据库中实际使用的字段

                System.Diagnostics.Debug.WriteLine($"使用字段映射: 登录账号={loginAccountField}, 账号名称={accountNameField}");

                int successCount = 0;
                var errorMessages = new List<string>();

                foreach (var account in accounts)
                {
                    try
                    {
                        System.Diagnostics.Debug.WriteLine($"处理账号: {account.AccountName} ({account.LoginAccount}) - 用户ID: {account.UserId}, 来源: {account.Source}");

                        // 检查账号是否已存在 - 使用动态字段名
                        var checkQuery = $@"
                            SELECT COUNT(*) FROM user_account 
                            WHERE user_id = @userId AND {loginAccountField} = @loginAccount AND source = @source";

                        using var checkCommand = new MySqlCommand(checkQuery, connection);
                        checkCommand.Parameters.AddWithValue("@userId", account.UserId);
                        checkCommand.Parameters.AddWithValue("@loginAccount", account.LoginAccount);
                        checkCommand.Parameters.AddWithValue("@source", account.Source.ToString());

                        var existingCount = Convert.ToInt32(await checkCommand.ExecuteScalarAsync());
                        System.Diagnostics.Debug.WriteLine($"  重复检查: {existingCount} 个重复账号");
                        
                        if (existingCount > 0)
                        {
                            var errorMsg = $"账号 {account.LoginAccount} 已存在";
                            errorMessages.Add(errorMsg);
                            System.Diagnostics.Debug.WriteLine($"  跳过: {errorMsg}");
                            continue;
                        }

                        // 密码以明文形式存储，不进行MD5加密
                        System.Diagnostics.Debug.WriteLine($"  密码将以明文形式存储: {account.Password}");

                        // 插入新账号 - 使用动态字段名并包含密码字段
                        var insertQuery = $@"
                            INSERT INTO user_account (user_id, simulator_id, {accountNameField}, {loginAccountField}, 
                                                    password, account_status, source, created_at, updated_at)
                            VALUES (@userId, @simulatorId, @accountName, @loginAccount, 
                                    @password, @accountStatus, @source, NOW(), NOW())";

                        using var insertCommand = new MySqlCommand(insertQuery, connection);
                        insertCommand.Parameters.AddWithValue("@userId", account.UserId);
                        insertCommand.Parameters.AddWithValue("@simulatorId", account.SimulatorId);
                        insertCommand.Parameters.AddWithValue("@accountName", account.AccountName);
                        insertCommand.Parameters.AddWithValue("@loginAccount", account.LoginAccount);
                        insertCommand.Parameters.AddWithValue("@password", account.Password ?? string.Empty); // 确保密码不为null
                        insertCommand.Parameters.AddWithValue("@accountStatus", account.AccountStatus.ToString());
                        insertCommand.Parameters.AddWithValue("@source", account.Source.ToString());

                        int rowsAffected = await insertCommand.ExecuteNonQueryAsync();
                        System.Diagnostics.Debug.WriteLine($"  插入结果: {rowsAffected} 行受影响");
                        
                        if (rowsAffected > 0)
                        {
                            successCount++;
                            System.Diagnostics.Debug.WriteLine($"  成功插入账号: {account.LoginAccount} (密码以明文存储)");
                        }
                        else
                        {
                            var errorMsg = $"插入账号 {account.LoginAccount} 失败: 无行受影响";
                            errorMessages.Add(errorMsg);
                            System.Diagnostics.Debug.WriteLine($"  失败: {errorMsg}");
                        }
                    }
                    catch (Exception ex)
                    {
                        var errorMsg = $"导入账号 {account.LoginAccount} 失败：{ex.Message}";
                        errorMessages.Add(errorMsg);
                        System.Diagnostics.Debug.WriteLine($"  异常: {errorMsg}");
                        System.Diagnostics.Debug.WriteLine($"  异常详情: {ex}");
                    }
                }

                result.SuccessCount = successCount;
                result.FailureCount = accounts.Count - successCount;
                result.ErrorMessages = errorMessages;

                System.Diagnostics.Debug.WriteLine($"批量导入完成: 成功 {successCount}, 失败 {result.FailureCount}");

                if (successCount > 0)
                {
                    result.Success = true;
                    result.Message = $"成功导入 {successCount} 个账号";
                    
                    if (result.FailureCount > 0)
                    {
                        result.Message += $"，{result.FailureCount} 个账号导入失败";
                    }
                }
                else
                {
                    result.Success = false;
                    result.Message = "没有账号被成功导入";
                }
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.Message = $"批量导入失败：{ex.Message}";
                result.ErrorMessages.Add(ex.Message);
                System.Diagnostics.Debug.WriteLine($"批量导入账号异常: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"异常详情: {ex}");
            }

            return result;
        }

        /// <summary>
        /// 更新账号状态
        /// </summary>
        /// <param name="accountId">账号ID</param>
        /// <param name="status">新状态</param>
        /// <returns>操作结果</returns>
        public static async Task<AccountOperationResult> UpdateAccountStatusAsync(int accountId, AccountStatus status)
        {
            var result = new AccountOperationResult();

            try
            {
                using var connection = new MySqlConnection(DatabaseConfig.ConnectionString);
                await connection.OpenAsync();

                const string updateQuery = @"
                    UPDATE user_account 
                    SET account_status = @status, updated_at = NOW()
                    WHERE id = @accountId";

                using var command = new MySqlCommand(updateQuery, connection);
                command.Parameters.AddWithValue("@accountId", accountId);
                command.Parameters.AddWithValue("@status", status.ToString());

                int rowsAffected = await command.ExecuteNonQueryAsync();

                if (rowsAffected > 0)
                {
                    result.Success = true;
                    result.Message = "账号状态更新成功";
                }
                else
                {
                    result.Success = false;
                    result.Message = "账号不存在或状态更新失败";
                }
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.Message = $"更新账号状态失败：{ex.Message}";
                System.Diagnostics.Debug.WriteLine($"更新账号状态失败: {ex.Message}");
            }

            return result;
        }

        /// <summary>
        /// 获取账号统计信息
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>统计信息</returns>
        public static async Task<Dictionary<string, int>> GetAccountStatisticsAsync(int userId)
        {
            var statistics = new Dictionary<string, int>
            {
                ["Total"] = 0,
                ["Instagram"] = 0,
                ["Tiktok"] = 0,
                ["X"] = 0,
                ["Available"] = 0,
                ["Unavailable"] = 0
            };

            try
            {
                using var connection = new MySqlConnection(DatabaseConfig.ConnectionString);
                await connection.OpenAsync();

                // 总数统计
                const string totalQuery = "SELECT COUNT(*) FROM user_account WHERE user_id = @userId";
                using var totalCommand = new MySqlCommand(totalQuery, connection);
                totalCommand.Parameters.AddWithValue("@userId", userId);
                statistics["Total"] = Convert.ToInt32(await totalCommand.ExecuteScalarAsync());

                // 按来源统计
                const string sourceQuery = @"
                    SELECT source, COUNT(*) as count 
                    FROM user_account 
                    WHERE user_id = @userId 
                    GROUP BY source";

                using var sourceCommand = new MySqlCommand(sourceQuery, connection);
                sourceCommand.Parameters.AddWithValue("@userId", userId);
                using var sourceReader = await sourceCommand.ExecuteReaderAsync();

                while (await sourceReader.ReadAsync())
                {
                    var source = sourceReader.GetString(sourceReader.GetOrdinal("source"));
                    var count = sourceReader.GetInt32(sourceReader.GetOrdinal("count"));
                    if (statistics.ContainsKey(source))
                    {
                        statistics[source] = count;
                    }
                }
                sourceReader.Close();

                // 按状态统计
                const string statusQuery = @"
                    SELECT account_status, COUNT(*) as count 
                    FROM user_account 
                    WHERE user_id = @userId 
                    GROUP BY account_status";

                using var statusCommand = new MySqlCommand(statusQuery, connection);
                statusCommand.Parameters.AddWithValue("@userId", userId);
                using var statusReader = await statusCommand.ExecuteReaderAsync();

                while (await statusReader.ReadAsync())
                {
                    var status = statusReader.GetString(statusReader.GetOrdinal("account_status"));
                    var count = statusReader.GetInt32(statusReader.GetOrdinal("count"));
                    
                    if (status == "Yes")
                    {
                        statistics["Available"] = count;
                    }
                    else if (status == "No")
                    {
                        statistics["Unavailable"] = count;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取账号统计信息失败: {ex.Message}");
            }

            return statistics;
        }

        /// <summary>
        /// 搜索账号
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="keyword">搜索关键词</param>
        /// <param name="source">账号来源过滤（可选）</param>
        /// <param name="status">账号状态过滤（可选）</param>
        /// <returns>匹配的账号列表</returns>
        public static async Task<List<UserAccount>> SearchAccountsAsync(int userId, string keyword, AccountSource? source = null, AccountStatus? status = null)
        {
            var accounts = new List<UserAccount>();

            try
            {
                using var connection = new MySqlConnection(DatabaseConfig.ConnectionString);
                await connection.OpenAsync();

                var queryBuilder = new System.Text.StringBuilder(@"
                    SELECT id, user_id, simulator_id, account_name, account, password,
                           account_status, source, created_at, updated_at
                    FROM user_account 
                    WHERE user_id = @userId 
                      AND (account_name LIKE @keyword OR account LIKE @keyword)");

                var parameters = new List<MySqlParameter>
                {
                    new MySqlParameter("@userId", userId),
                    new MySqlParameter("@keyword", $"%{keyword}%")
                };

                if (source.HasValue)
                {
                    queryBuilder.Append(" AND source = @source");
                    parameters.Add(new MySqlParameter("@source", source.Value.ToString()));
                }

                if (status.HasValue)
                {
                    queryBuilder.Append(" AND account_status = @status");
                    parameters.Add(new MySqlParameter("@status", status.Value.ToString()));
                }

                queryBuilder.Append(" ORDER BY updated_at DESC");

                using var command = new MySqlCommand(queryBuilder.ToString(), connection);
                command.Parameters.AddRange(parameters.ToArray());

                using var reader = await command.ExecuteReaderAsync();

                while (await reader.ReadAsync())
                {
                    var account = new UserAccount
                    {
                        Id = reader.GetInt32(reader.GetOrdinal("id")),
                        UserId = reader.GetInt32(reader.GetOrdinal("user_id")),
                        SimulatorId = reader.GetInt32(reader.GetOrdinal("simulator_id")),
                        AccountName = reader.GetString(reader.GetOrdinal("account_name")),
                        LoginAccount = reader.GetString(reader.GetOrdinal("account")), // 使用正确的字段名
                        AccountStatus = Enum.Parse<AccountStatus>(reader.GetString(reader.GetOrdinal("account_status")), true),
                        Source = Enum.Parse<AccountSource>(reader.GetString(reader.GetOrdinal("source")), true),
                        CreatedAt = reader.GetDateTime(reader.GetOrdinal("created_at")),
                        UpdatedAt = reader.GetDateTime(reader.GetOrdinal("updated_at"))
                    };

                    accounts.Add(account);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"搜索账号失败: {ex.Message}");
            }

            return accounts;
        }


        /// <summary>
        /// 根据平台来源获取账号列表（不区分用户，适合平台切换展示）
        /// </summary>
        /// <param name="source">账号来源</param>
        /// <returns>账号列表</returns>
        public static List<UserAccount> GetBySource(AccountSource source)
        {
            var accounts = new List<UserAccount>();
            try
            {
                using var connection = new MySqlConnection(DatabaseConfig.ConnectionString);
                connection.Open();

                const string query = @"
                    SELECT id, user_id, simulator_id, account_name, account, proxy_id,password,
                           account_status, source, created_at, updated_at
                    FROM user_account
                    WHERE source = @source and account_status = 'yes'
                    ORDER BY updated_at DESC";

                using var command = new MySqlCommand(query, connection);
                command.Parameters.AddWithValue("@source", source.ToString());

                using var reader = command.ExecuteReader();
                while (reader.Read())
                {
                    var account = new UserAccount
                    {
                        Id = reader.GetInt32(reader.GetOrdinal("id")),
                        UserId = reader.GetInt32(reader.GetOrdinal("user_id")),
                        SimulatorId = reader.GetInt32(reader.GetOrdinal("simulator_id")),
                        ProxyId = reader.GetInt32(reader.GetOrdinal("proxy_id")),
                        AccountName = reader.GetString(reader.GetOrdinal("account_name")),
                        Password = reader.GetString(reader.GetOrdinal("password")),
                        LoginAccount = reader.GetString(reader.GetOrdinal("account")),
                        AccountStatus = Enum.Parse<AccountStatus>(reader.GetString(reader.GetOrdinal("account_status")), true),
                        Source = Enum.Parse<AccountSource>(reader.GetString(reader.GetOrdinal("source")), true),
                        CreatedAt = reader.GetDateTime(reader.GetOrdinal("created_at")),
                        UpdatedAt = reader.GetDateTime(reader.GetOrdinal("updated_at"))
                    };
                    accounts.Add(account);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"GetBySource 查询账号失败: {ex.Message}");
            }
            return accounts;
        }
    }
}
