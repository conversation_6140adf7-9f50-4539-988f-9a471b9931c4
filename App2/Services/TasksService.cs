using System;
using System.Collections.Generic;
using MySql.Data.MySqlClient;
using App2.Config;
using App2.Models;

namespace App2.Services
{
    /// <summary>
    /// ����������
    /// </summary>
    public class TasksService
    {
        /// <summary>
        /// ��ȡ��������ͬ����
        /// </summary>
        public static List<Tasks> GetAllTasks()
        {
            var tasks = new List<Tasks>();
            using var connection = new MySqlConnection(DatabaseConfig.ConnectionString);
            connection.Open();

            const string query = "SELECT * FROM tasks";
            using var command = new MySqlCommand(query, connection);
            using var reader = command.ExecuteReader();

            while (reader.Read())
            {
                
                tasks.Add(new Tasks
                {
                    Id = reader.GetInt32(reader.GetOrdinal("id")),
                    Tid = reader.GetInt32(reader.GetOrdinal("tid")),
                    Platform = reader.GetString(reader.GetOrdinal("platform")),
                    Uids = reader.GetString(reader.GetOrdinal("uids")),
                    SearchUrl = reader.GetString(reader.GetOrdinal("search_url")),
                    Keywords = reader.GetString(reader.GetOrdinal("keywords")),
                    Content = reader.GetString(reader.GetOrdinal("content")),
                    Results = reader.GetString(reader.GetOrdinal("results")),
                    Status = reader.GetInt32(reader.GetOrdinal("status")),
                    CreatedAt = reader.GetDateTime(reader.GetOrdinal("created_at")),
                    UpdatedAt = reader.GetDateTime(reader.GetOrdinal("updated_at"))
                });
            }
            return tasks;
        }

        /// <summary>
        /// ��ѯ�����б������������ͣ��ɰ�tid��platform��status��keywords��idɸѡ��֧�ַ�ҳ
        /// </summary>
        public static List<TasksList> QueryTasks(
             int? tid,
            string platform,
            string status,
            string keywords,
            int? id,
            int page,
            int pageSize,
            out int totalCount)
        {
            var tasks = new List<TasksList>();
            totalCount = 0;
            using var connection = new MySqlConnection(DatabaseConfig.ConnectionString);
            connection.Open();

            // ����ɸѡ����
            var where = " WHERE 1=1";
            var parameters = new List<MySqlParameter>();
            if (tid.HasValue)
            {
                where += " AND t.tid = @tid";
                parameters.Add(new MySqlParameter("@tid", tid.Value));
            }
            if (!string.IsNullOrEmpty(platform))
            {
                where += " AND t.platform = @platform";
                parameters.Add(new MySqlParameter("@platform", platform));
            }
            if (!string.IsNullOrEmpty(status))
            {
                where += " AND t.status = @status";
                parameters.Add(new MySqlParameter("@status", status));
            }
            if (!string.IsNullOrEmpty(keywords))
            {
                where += " AND t.keywords LIKE @keywords";
                parameters.Add(new MySqlParameter("@keywords", $"%{keywords}%"));
            }
            if (id.HasValue)
            {
                where += " AND t.id = @id";
                parameters.Add(new MySqlParameter("@id", id.Value));
            }

            // ��ѯ������
            var countQuery = $"SELECT COUNT(*) FROM tasks t LEFT JOIN task_type tt ON t.tid = tt.id {where}";
            using (var countCmd = new MySqlCommand(countQuery, connection))
            {
                countCmd.Parameters.AddRange(parameters.ToArray());
                totalCount = Convert.ToInt32(countCmd.ExecuteScalar());
            }

            // ��ѯ��ҳ����
            int offset = (page - 1) * pageSize;
            var query = $@"SELECT t.*, tt.name FROM tasks t LEFT JOIN task_type tt ON t.tid = tt.id {where} ORDER BY t.id DESC LIMIT @pageSize OFFSET @offset";
            using var command = new MySqlCommand(query, connection);
            command.Parameters.AddRange(parameters.ToArray());
            command.Parameters.AddWithValue("@pageSize", pageSize);
            command.Parameters.AddWithValue("@offset", offset);

            using var reader = command.ExecuteReader();
            while (reader.Read())
            {
                int count = 0;
                if (reader.GetString(reader.GetOrdinal("uids")) != "")
                {
                    var uidsStr = reader.GetString(reader.GetOrdinal("uids"));
                    var uidsArr = uidsStr.Split(',', StringSplitOptions.RemoveEmptyEntries);
                    count = uidsArr.Length;
                }
                var task = new TasksList
                {
                    Id = reader.GetInt32(reader.GetOrdinal("id")),
                    Tid = reader.GetInt32(reader.GetOrdinal("tid")),
                    Name = reader.GetString(reader.GetOrdinal("name")),
                    Platform = reader.GetString(reader.GetOrdinal("platform")),
                    Uids = reader.GetString(reader.GetOrdinal("uids")),
                    AccountNum = count + "��",

                    SearchUrl = reader.GetString(reader.GetOrdinal("search_url")),
                    Keywords = reader.GetString(reader.GetOrdinal("keywords")),
                    Content = reader.GetString(reader.GetOrdinal("content")),
                    Results = reader.GetString(reader.GetOrdinal("results")),
                    Status = reader.GetInt32(reader.GetOrdinal("status")),
                    StatusName = reader.GetInt32(reader.GetOrdinal("status")) == 1?"������":"�����",
                    StatusAble = reader.GetInt32(reader.GetOrdinal("status")) == 1?true:false,
                    CreatedAt = reader.GetDateTime(reader.GetOrdinal("created_at")),
                    UpdatedAt = reader.GetDateTime(reader.GetOrdinal("updated_at"))
                };
                tasks.Add(task);
            }
            return tasks;
        }

        /// <summary>
        /// ��������ͬ����
        /// </summary>
        public static int AddTask(Tasks task)
        {
            using var connection = new MySqlConnection(DatabaseConfig.ConnectionString);
            connection.Open();

            const string query = @"
                INSERT INTO tasks (tid, platform, uids, search_url, keywords, content, results, status)
                VALUES (@tid, @platform, @uids, @search_url, @keywords, @content, @results, @status);
                SELECT LAST_INSERT_ID();";

            using var command = new MySqlCommand(query, connection);
            command.Parameters.AddWithValue("@tid", task.Tid);
            command.Parameters.AddWithValue("@platform", task.Platform);
            command.Parameters.AddWithValue("@uids", task.Uids);
            command.Parameters.AddWithValue("@search_url", task.SearchUrl);
            command.Parameters.AddWithValue("@keywords", task.Keywords);
            command.Parameters.AddWithValue("@content", task.Content);
            command.Parameters.AddWithValue("@results", task.Results);
            command.Parameters.AddWithValue("@status", 1);

            var id = command.ExecuteScalar();
            return Convert.ToInt32(id);
        }

        /// <summary>
        /// ��������ͬ����
        /// </summary>
        public static bool UpdateTask(Tasks task)
        {
            using var connection = new MySqlConnection(DatabaseConfig.ConnectionString);
            connection.Open();

            const string query = @"
                UPDATE tasks SET
                    tid = @tid,
                    platform = @platform,
                    uids = @uids,
                    search_url = @search_url,
                    keywords = @keywords,
                    content = @content,
                    results = @results,
                    status = @status,
                    updated_at = NOW()
                WHERE id = @id";

            using var command = new MySqlCommand(query, connection);
            command.Parameters.AddWithValue("@id", task.Id);
            command.Parameters.AddWithValue("@tid", task.Tid);
            command.Parameters.AddWithValue("@platform", task.Platform);
            command.Parameters.AddWithValue("@uids", task.Uids);
            command.Parameters.AddWithValue("@search_url", task.SearchUrl);
            command.Parameters.AddWithValue("@keywords", task.Keywords);
            command.Parameters.AddWithValue("@content", task.Content);
            command.Parameters.AddWithValue("@results", task.Results);
            command.Parameters.AddWithValue("@status", task.Status);

            var rows = command.ExecuteNonQuery();
            return rows > 0;
        }

        public static bool UpdateStatus( int id,int status)
        {
            using var connection = new MySqlConnection(DatabaseConfig.ConnectionString);
            connection.Open();

            const string query = @"
                UPDATE tasks SET
                    status = @status,
                    updated_at = NOW()
                WHERE id = @id";

            using var command = new MySqlCommand(query, connection);
            command.Parameters.AddWithValue("@id", id);
            command.Parameters.AddWithValue("@status", status);

            var rows = command.ExecuteNonQuery();
            return rows > 0;
        }



        /// <summary>
        /// ɾ������ͬ����
        /// </summary>
        public static bool DeleteTask(int id)
        {
            using var connection = new MySqlConnection(DatabaseConfig.ConnectionString);
            connection.Open();

            const string query = "DELETE FROM tasks WHERE id = @id";
            using var command = new MySqlCommand(query, connection);
            command.Parameters.AddWithValue("@id", id);

            var rows = command.ExecuteNonQuery();
            return rows > 0;
        }
    }
}