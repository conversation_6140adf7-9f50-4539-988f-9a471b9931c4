using Microsoft.UI.Xaml;
using App2.Views.Windows;
using App2.Services;
using System;
using System.Threading.Tasks;
using System.IO;
using System.Diagnostics;

namespace App2
{
    /// <summary>
    /// Provides application-specific behavior to supplement the default Application class.
    /// </summary>
    public partial class App : Application
    {
        private Window? _window;
        private readonly object _windowLock = new object();
        private static string LogFilePath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData), "App2", "error.log");

        /// <summary>
        /// 获取当前主窗口
        /// </summary>
        public Window? MainWindow 
        { 
            get 
            { 
                lock (_windowLock) 
                { 
                    return _window; 
                } 
            } 
        }

        /// <summary>
        /// Initializes the singleton application object.  This is the first line of authored code
        /// executed, and as such is the logical equivalent of main() or WinMain().
        /// </summary>
        public App()
        {
            try
            {
                // 设置全局异常处理
                this.UnhandledException += App_UnhandledException;
                AppDomain.CurrentDomain.UnhandledException += CurrentDomain_UnhandledException;
                
                // 确保日志目录存在
                Directory.CreateDirectory(Path.GetDirectoryName(LogFilePath)!);
                
                LogMessage("应用程序初始化开始");
                InitializeComponent();
                LogMessage("应用程序初始化完成");
            }
            catch (Exception ex)
            {
                LogMessage($"应用程序初始化失败: {ex}");
                throw;
            }
        }

        private void App_UnhandledException(object sender, Microsoft.UI.Xaml.UnhandledExceptionEventArgs e)
        {
            LogMessage($"未处理的UI异常: {e.Exception}");
            e.Handled = true;
            
            // 尝试显示错误消息
            try
            {
                ShowErrorDialog($"应用程序遇到错误: {e.Exception.Message}");
            }
            catch
            {
                // 如果无法显示对话框，至少记录日志
            }
        }

        private void CurrentDomain_UnhandledException(object sender, System.UnhandledExceptionEventArgs e)
        {
            LogMessage($"未处理的域异常: {e.ExceptionObject}");
        }

        private static void LogMessage(string message)
        {
            try
            {
                var logEntry = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] {message}{Environment.NewLine}";
                File.AppendAllText(LogFilePath, logEntry);
                Debug.WriteLine(message);
            }
            catch
            {
                // 忽略日志写入错误
            }
        }

        private async void ShowErrorDialog(string message)
        {
            try
            {
                var dialog = new Microsoft.UI.Xaml.Controls.ContentDialog()
                {
                    Title = "错误",
                    Content = message,
                    CloseButtonText = "确定"
                };

                if (_window?.Content is Microsoft.UI.Xaml.FrameworkElement element)
                {
                    dialog.XamlRoot = element.XamlRoot;
                    await dialog.ShowAsync();
                }
            }
            catch
            {
                // 如果无法显示对话框，忽略错误
            }
        }

        /// <summary>
        /// Invoked when the application is launched.
        /// </summary>
        /// <param name="args">Details about the launch request and process.</param>
        protected override void OnLaunched(LaunchActivatedEventArgs args)
        {
            try
            {
                LogMessage("应用启动开始");

                // 使用同步启动，避免潜在的线程问题
                StartupSync();
            }
            catch (Exception ex)
            {
                LogMessage($"应用启动失败: {ex}");
                
                // 如果正常启动失败，尝试创建基本的登录窗口
                try
                {
                    CreateLoginWindow();
                }
                catch (Exception fallbackEx)
                {
                    LogMessage($"创建备用登录窗口也失败: {fallbackEx}");
                    // 显示错误对话框作为最后手段
                    ShowErrorDialog($"应用启动失败: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// 同步启动应用
        /// </summary>
        private void StartupSync()
        {
            try
            {
                LogMessage("开始同步启动流程");

                // 异步检查会话，但不等待结果
                _ = CheckAndCreateWindowAsync();
            }
            catch (Exception ex)
            {
                LogMessage($"同步启动失败: {ex}");
                // 如果启动失败，直接创建登录窗口
                CreateLoginWindow();
            }
        }

        /// <summary>
        /// 异步检查会话并创建窗口
        /// </summary>
        private async Task CheckAndCreateWindowAsync()
        {
            try
            {
                LogMessage("开始检查会话状态");

                // 运行自动修复
                LogMessage("运行数据库自动修复...");
                Utils.QuickFixTester.RunAutoFixOnStartup();

                // 首先检查是否有有效的会话
                bool hasValidSession = await SessionService.LoadAndValidateSessionAsync();
                LogMessage($"会话检查结果: {hasValidSession}");

                // 在UI线程上创建窗口 - 直接调用，因为我们在UI线程上
                CreateInitialWindow(hasValidSession);
            }
            catch (Exception ex)
            {
                LogMessage($"检查会话失败: {ex}");
                // 如果会话检查失败，创建登录窗口
                CreateLoginWindow();
            }
        }

        /// <summary>
        /// 创建初始窗口
        /// </summary>
        /// <param name="hasValidSession">是否有有效会话</param>
        private void CreateInitialWindow(bool hasValidSession)
        {
            try
            {
                if (hasValidSession)
                {
                    LogMessage("发现有效会话，直接进入主窗口");
                    CreateMainWindow();
                }
                else
                {
                    LogMessage("没有有效会话，显示登录窗口");
                    CreateLoginWindow();
                }
            }
            catch (Exception ex)
            {
                LogMessage($"创建初始窗口失败: {ex}");
                // 如果出错，确保有登录窗口
                CreateLoginWindow();
            }
        }

        /// <summary>
        /// 创建登录窗口
        /// </summary>
        private void CreateLoginWindow()
        {
            try
            {
                LogMessage("创建登录窗口");
                var loginWindow = new LoginWindow();
                SetCurrentWindow(loginWindow);
                loginWindow.Activate();
                LogMessage("登录窗口创建成功");
            }
            catch (Exception ex)
            {
                LogMessage($"创建登录窗口失败: {ex}");
                throw;
            }
        }

        /// <summary>
        /// 创建主窗口
        /// </summary>
        private void CreateMainWindow()
        {
            try
            {
                LogMessage("创建主窗口");
                
                // 重置AccountPage的气泡提示状态（用于有效会话直接进入主窗口）
                App2.Views.Pages.AccountPage.ResetImportTipState();
                
                var mainWindow = new MainWindow();
                SetCurrentWindow(mainWindow);
                mainWindow.Activate();
                LogMessage("主窗口创建成功");
            }
            catch (Exception ex)
            {
                LogMessage($"创建主窗口失败: {ex}");
                throw;
            }
        }

        /// <summary>
        /// 安全地切换到主窗口
        /// </summary>
        public void SwitchToMainWindow()
        {
            try
            {
                LogMessage("开始切换到主窗口");
                
                // 重置AccountPage的气泡提示状态（用于新登录会话）
                App2.Views.Pages.AccountPage.ResetImportTipState();
                
                lock (_windowLock)
                {
                    var currentWindow = _window;
                    
                    // 创建主窗口
                    var mainWindow = new MainWindow();
                    
                    // 更新窗口引用
                    _window = mainWindow;
                    
                    // 激活主窗口
                    mainWindow.Activate();
                    
                    // 延迟关闭旧窗口，确保新窗口完全激活
                    if (currentWindow != null)
                    {
                        _ = Task.Delay(100).ContinueWith(_ =>
                        {
                            try
                            {
                                currentWindow.DispatcherQueue.TryEnqueue(() =>
                                {
                                    currentWindow.Close();
                                });
                            }
                            catch (Exception ex)
                            {
                                LogMessage($"关闭旧窗口失败: {ex}");
                            }
                        });
                    }
                    
                    LogMessage("成功切换到主窗口");
                }
            }
            catch (Exception ex)
            {
                LogMessage($"切换到主窗口失败: {ex}");
                throw;
            }
        }

        /// <summary>
        /// 线程安全地设置当前窗口
        /// </summary>
        private void SetCurrentWindow(Window window)
        {
            lock (_windowLock)
            {
                _window = window;
            }
        }
    }
}
