@echo off
echo 开始发布自包含WinUI应用程序...

REM 清理之前的发布文件
if exist "output" rmdir /s /q "output"

REM 首先构建Release版本
echo 正在构建 Release 版本...
dotnet build -c Release -p:Platform=x64

if %ERRORLEVEL% NEQ 0 (
    echo 构建失败！
    pause
    exit /b 1
)

REM 创建发布目录
mkdir "output\Release"

REM 复制所有必需的文件
echo 正在复制应用程序文件...
robocopy "bin\x64\Release\net9.0-windows10.0.19041.0\win-x64" "output\Release" /E /XF *.pdb > nul

if %ERRORLEVEL% GEQ 8 (
    echo 文件复制失败！
    pause
    exit /b 1
)

echo.
echo 发布完成！
echo 可执行文件位置: %CD%\output\Release\promotion.exe
echo.
echo 正在启动应用程序进行测试...
start "" "%CD%\output\Release\promotion.exe"

echo.
echo 如果应用程序无法启动，请检查以下日志文件：
echo - %LOCALAPPDATA%\App2\error.log
echo.
echo 应用程序已成功发布为自包含版本！
echo 你可以将整个 output\Release 文件夹复制到任何Windows 10/11系统上运行。
echo.
pause